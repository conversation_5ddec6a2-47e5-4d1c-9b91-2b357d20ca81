#!/usr/bin/env python3
"""
Multi-Service gRPC Server

This server can run both connector and search services either:
1. On separate ports (recommended for production)
2. On the same port (for development/testing)
"""

import os
import sys
import grpc
import structlog
import threading
from concurrent import futures
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.grpc_ import connector_pb2_grpc, search_pb2_grpc
from app.services.connector_wrapper_service import ConnectorWrapperService
from app.services.search_wrapper_service import SearchWrapperService

# Load environment variables
load_dotenv()

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

def serve_combined():
    """Start both services on the same server (single port)"""
    port = os.getenv("GRPC_PORT", "50051")
    max_workers = int(os.getenv("MAX_WORKERS", "20"))
    
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers))
    
    # Add both services to the same server
    connector_service = ConnectorWrapperService()
    search_service = SearchWrapperService()
    
    connector_pb2_grpc.add_ConnectorServiceServicer_to_server(connector_service, server)
    search_pb2_grpc.add_SearchServiceServicer_to_server(search_service, server)
    
    # Configure server address
    listen_addr = f'[::]:{port}'
    server.add_insecure_port(listen_addr)
    
    # Start server
    server.start()
    logger.info(f"Combined gRPC server started on port {port}")
    logger.info(f"Server listening on {listen_addr}")
    logger.info(f"Services: ConnectorService, SearchService")
    logger.info(f"Max workers: {max_workers}")
    
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Shutting down Combined gRPC server...")
        server.stop(0)

def serve_separate():
    """Start services on separate ports"""
    connector_port = os.getenv("CONNECTOR_GRPC_PORT", "50052")
    search_port = os.getenv("SEARCH_GRPC_PORT", "50053")
    max_workers = int(os.getenv("MAX_WORKERS", "10"))
    
    # Create separate servers
    connector_server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers))
    search_server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers))
    
    # Add services to respective servers
    connector_service = ConnectorWrapperService()
    search_service = SearchWrapperService()
    
    connector_pb2_grpc.add_ConnectorServiceServicer_to_server(connector_service, connector_server)
    search_pb2_grpc.add_SearchServiceServicer_to_server(search_service, search_server)
    
    # Configure server addresses
    connector_addr = f'[::]:{connector_port}'
    search_addr = f'[::]:{search_port}'
    
    connector_server.add_insecure_port(connector_addr)
    search_server.add_insecure_port(search_addr)
    
    # Start servers
    connector_server.start()
    search_server.start()
    
    logger.info(f"Connector gRPC server started on port {connector_port}")
    logger.info(f"Search gRPC server started on port {search_port}")
    logger.info(f"Connector server listening on {connector_addr}")
    logger.info(f"Search server listening on {search_addr}")
    logger.info(f"Max workers per server: {max_workers}")
    
    def shutdown_servers():
        logger.info("Shutting down gRPC servers...")
        connector_server.stop(0)
        search_server.stop(0)
    
    try:
        # Wait for both servers
        connector_thread = threading.Thread(target=connector_server.wait_for_termination)
        search_thread = threading.Thread(target=search_server.wait_for_termination)
        
        connector_thread.start()
        search_thread.start()
        
        connector_thread.join()
        search_thread.join()
        
    except KeyboardInterrupt:
        shutdown_servers()

def main():
    """Main entry point"""
    mode = os.getenv("SERVER_MODE", "combined").lower()
    
    if mode == "separate":
        logger.info("Starting servers in separate mode")
        serve_separate()
    else:
        logger.info("Starting servers in combined mode")
        serve_combined()

if __name__ == '__main__':
    main()