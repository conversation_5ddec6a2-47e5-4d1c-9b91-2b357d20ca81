"""
Neo4j database service module.
Contains query execution functions and database operations.
"""
import logging
from typing import Any, Optional, List, Dict

from neo4j import Transaction
from app.db.neo4j import get_neo4j_session, get_neo4j_transaction

logger = logging.getLogger(__name__)


def execute_read_query(query: str, params: dict = None) -> List[Dict[str, Any]]:
    """
    Executes a read-only query and returns the results.
    
    Args:
        query: Cypher query string
        params: Query parameters
        
    Returns:
        List of query results as dictionaries
    """
    with get_neo4j_session() as session:
        result = session.run(query, params or {})
        return [dict(record) for record in result]


def execute_write_query(
    query: str, 
    params: dict = None, 
    tx: Optional[Transaction] = None
) -> List[Dict[str, Any]]:
    """
    Executes a write query, either within a provided transaction or standalone.
    If tx is provided, uses that transaction (caller manages commit/rollback).
    Otherwise, auto-commits as a single operation.
    
    Args:
        query: Cypher query string
        params: Query parameters
        tx: Optional transaction context
        
    Returns:
        List of query results as dictionaries
    """
    if tx:
        # Use provided transaction - caller manages lifecycle
        result = tx.run(query, params or {})
        return [dict(record) for record in result]
    else:
        # Standalone write operation with auto-commit
        with get_neo4j_session() as session:
            result = session.run(query, params or {})
            return [dict(record) for record in result]


def create_constraints() -> None:
    """
    Creates Neo4j schema constraints.
    """
    constraints = [
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (o:Organisation) 
        REQUIRE o.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (d:Department) 
        REQUIRE d.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (u:User) 
        REQUIRE u.id IS UNIQUE
        """,
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (u:User) 
        REQUIRE u.email IS UNIQUE
        """
    ]

    for query in constraints:
        execute_write_query(query)

    logger.info("Neo4j constraints created")