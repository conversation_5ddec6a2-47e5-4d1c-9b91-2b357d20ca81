"""
File type detection utilities.
"""

import mimetypes
import os
from typing import Optional
import structlog

logger = structlog.get_logger()

class FileTypeDetector:
    """
    Detects file types from URLs, content types, and file content.
    """
    
    # Supported MIME types
    SUPPORTED_MIME_TYPES = {
        # Documents
        'application/pdf': 'pdf',
        'application/msword': 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'text/plain': 'txt',
        'text/html': 'html',
        'application/rtf': 'rtf',
        
        # Spreadsheets
        'application/vnd.ms-excel': 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
        'text/csv': 'csv',
        
        # Presentations
        'application/vnd.ms-powerpoint': 'ppt',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
        
        # Other text formats
        'application/json': 'json',
        'application/xml': 'xml',
        'text/xml': 'xml',
        'text/markdown': 'md',
    }
    
    # File extensions to MIME type mapping
    EXTENSION_TO_MIME = {
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.txt': 'text/plain',
        '.html': 'text/html',
        '.htm': 'text/html',
        '.rtf': 'application/rtf',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.csv': 'text/csv',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.json': 'application/json',
        '.xml': 'application/xml',
        '.md': 'text/markdown',
    }
    
    def __init__(self):
        # Initialize mimetypes
        mimetypes.init()
    
    def detect_from_url(self, url: str) -> Optional[str]:
        """
        Detect file type from URL extension.
        
        Args:
            url: The URL to analyze
            
        Returns:
            MIME type if detected, None otherwise
        """
        try:
            # Extract filename from URL (remove query parameters)
            url_path = url.split('?')[0]
            
            # Get file extension
            _, ext = os.path.splitext(url_path.lower())
            
            if ext in self.EXTENSION_TO_MIME:
                return self.EXTENSION_TO_MIME[ext]
            
            # Try using mimetypes library
            mime_type, _ = mimetypes.guess_type(url_path)
            if mime_type and mime_type in self.SUPPORTED_MIME_TYPES:
                return mime_type
                
            return None
            
        except Exception as e:
            logger.warning(f"Error detecting file type from URL: {str(e)}")
            return None
    
    def detect_from_content_type(self, content_type: str) -> Optional[str]:
        """
        Detect file type from HTTP Content-Type header.
        
        Args:
            content_type: The Content-Type header value
            
        Returns:
            MIME type if supported, None otherwise
        """
        try:
            # Clean up content type (remove charset, etc.)
            mime_type = content_type.split(';')[0].strip().lower()
            
            if mime_type in self.SUPPORTED_MIME_TYPES:
                return mime_type
                
            return None
            
        except Exception as e:
            logger.warning(f"Error detecting file type from content type: {str(e)}")
            return None
    
    def detect_from_content(self, content: bytes) -> Optional[str]:
        """
        Detect file type from file content (magic bytes).
        
        Args:
            content: The file content bytes
            
        Returns:
            MIME type if detected, None otherwise
        """
        try:
            if not content:
                return None
            
            # Check for common file signatures
            if content.startswith(b'%PDF'):
                return 'application/pdf'
            elif content.startswith(b'PK\x03\x04'):
                # ZIP-based formats (DOCX, XLSX, PPTX)
                # Need more sophisticated detection for these
                return None
            elif content.startswith(b'\xd0\xcf\x11\xe0'):
                # Old Office formats (DOC, XLS, PPT)
                return None
            elif content.startswith(b'<html') or content.startswith(b'<!DOCTYPE'):
                return 'text/html'
            elif content.startswith(b'<?xml'):
                return 'application/xml'
            elif content.startswith(b'{') or content.startswith(b'['):
                # Might be JSON
                try:
                    content.decode('utf-8')
                    return 'application/json'
                except UnicodeDecodeError:
                    pass
            
            # Try to decode as text
            try:
                content.decode('utf-8')
                return 'text/plain'
            except UnicodeDecodeError:
                pass
                
            return None
            
        except Exception as e:
            logger.warning(f"Error detecting file type from content: {str(e)}")
            return None
    
    def detect_file_type(self, url: str, content_type: str = None, content: bytes = None) -> Optional[str]:
        """
        Detect file type using multiple methods.
        
        Args:
            url: The source URL
            content_type: HTTP Content-Type header (optional)
            content: File content bytes (optional)
            
        Returns:
            MIME type if detected, None otherwise
        """
        # Try content type first (most reliable)
        if content_type:
            mime_type = self.detect_from_content_type(content_type)
            if mime_type:
                return mime_type
        
        # Try URL extension
        mime_type = self.detect_from_url(url)
        if mime_type:
            return mime_type
        
        # Try content analysis as last resort
        if content:
            mime_type = self.detect_from_content(content)
            if mime_type:
                return mime_type
        
        return None
    
    def is_supported_type(self, mime_type: str) -> bool:
        """
        Check if a MIME type is supported.
        
        Args:
            mime_type: The MIME type to check
            
        Returns:
            True if supported, False otherwise
        """
        return mime_type in self.SUPPORTED_MIME_TYPES
    
    def get_file_extension(self, mime_type: str) -> Optional[str]:
        """
        Get file extension for a MIME type.
        
        Args:
            mime_type: The MIME type
            
        Returns:
            File extension (with dot) if known, None otherwise
        """
        if mime_type in self.SUPPORTED_MIME_TYPES:
            file_type = self.SUPPORTED_MIME_TYPES[mime_type]
            return f'.{file_type}'
        
        return None