import structlog
from app.utils.kafka.kafka_service import KafkaProducer

logger = structlog.get_logger()

class HelperService:
    def send_invite_email_async(self, email_data, email_type, action):
        """Async email sending to avoid blocking the main flow"""
        try:
            kafka_producer = KafkaProducer()
            
            kafka_producer.send_email_event_unified(
                email_type=email_type,
                data=email_data,
                action=[f'{action}']
            )
            
            logger.info("Invite email notification sent via Kafka")
            
        except Exception as e:
            logger.error("Error sending invite email notification", error=str(e))