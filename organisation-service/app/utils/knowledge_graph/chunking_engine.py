"""
Document chunking engine for Enterprise KG

This module handles the document chunking and metadata management
for the enhanced knowledge graph builder.
"""

import logging
from typing import List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ChunkMetadata:
    """Metadata for a document chunk."""
    chunk_id: str
    chunk_index: int
    word_count: int
    sentence_count: int
    start_position: int
    end_position: int
    strategy_used: str


@dataclass
class DocumentChunk:
    """Represents a chunk of text from document splitting."""
    text: str
    metadata: ChunkMetadata

    def __post_init__(self):
        """Validate the chunk after initialization."""
        if not self.text or not self.text.strip():
            raise ValueError("Chunk text cannot be empty")
        if not self.metadata:
            raise ValueError("Chunk metadata is required")


@dataclass
class ChunkGraphResult:
    """Result of processing a single chunk."""
    chunk_id: str
    chunk_index: int
    entities_extracted: int
    relationships_extracted: int
    graph_stored: bool
    error: Optional[str] = None


@dataclass
class DocumentGraphResult:
    """Result of processing an entire document."""
    file_id: str
    total_chunks: int
    successful_chunks: int
    failed_chunks: int
    total_entities: int
    total_relationships: int
    chunk_results: List[ChunkGraphResult]
    file_node_created: bool
    contains_relationships_created: int


class ChunkingEngine:
    """
    Handles document chunking with enhanced metadata tracking.
    """
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Initialize the chunking engine.
        
        Args:
            chunk_size: The size of each chunk in characters
            chunk_overlap: The overlap between chunks in characters
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def create_chunks_from_text(self, text: str, base_id: str) -> List[DocumentChunk]:
        """
        Create DocumentChunk objects from text.
        
        Args:
            text: The text to split into chunks
            base_id: Base identifier for chunk IDs
            
        Returns:
            List of DocumentChunk objects
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for chunking")
            return []
        
        # Split text into chunks
        text_chunks = self._split_text(text)
        document_chunks = []
        
        for i, chunk_text in enumerate(text_chunks):
            # Calculate positions
            start_pos = i * (self.chunk_size - self.chunk_overlap)
            end_pos = start_pos + len(chunk_text)
            
            # Count words and sentences
            word_count = len(chunk_text.split())
            sentence_count = len([s for s in chunk_text.split('.') if s.strip()])
            
            # Create metadata
            metadata = ChunkMetadata(
                chunk_id=f"{base_id}_chunk_{i}",
                chunk_index=i,
                word_count=word_count,
                sentence_count=sentence_count,
                start_position=start_pos,
                end_position=end_pos,
                strategy_used="overlap_chunking"
            )
            
            # Create document chunk
            document_chunk = DocumentChunk(text=chunk_text, metadata=metadata)
            document_chunks.append(document_chunk)
        
        logger.info(f"Created {len(document_chunks)} chunks from text")
        return document_chunks
    
    def _split_text(self, text: str) -> List[str]:
        """
        Split text into overlapping chunks.
        
        Args:
            text: The text to split into chunks
            
        Returns:
            List of text chunks
        """
        if len(text) <= self.chunk_size:
            return [text]
        
        chunks = []
        for i in range(0, len(text), self.chunk_size - self.chunk_overlap):
            chunk = text[i:i + self.chunk_size]
            if chunk:
                chunks.append(chunk)
        
        return chunks