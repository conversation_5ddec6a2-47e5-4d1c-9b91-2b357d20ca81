"""
Prompt templates for LLM-based entity and relationship extraction.
"""

from typing import List
from app.constants.entities import EntityType
from app.constants.relationships import RelationshipType


def create_entity_extraction_prompt(text: str, entity_types: List[str], relationship_types: List[str]) -> str:
    """
    Create a prompt for extracting entities and relationships from text.
    
    Args:
        text: The text to analyze
        entity_types: List of valid entity types
        relationship_types: List of valid relationship types
        
    Returns:
        Formatted prompt string
    """
    
    # Sample entity types for the prompt (showing most common ones)
    sample_entities = [
        "Person", "Employee", "Manager", "Department", "Team", "Project", 
        "Product", "System", "Application", "Document", "Process", "Goal"
    ]
    
    # Sample relationship types for the prompt
    sample_relationships = [
        "WORKS_FOR", "MANAGES", "REPORTS_TO", "MEMBER_OF", "OWNS", 
        "DEVELOPS", "USES", "DOCUMENTS", "PARTICIPATES_IN"
    ]
    
    prompt = f"""
You are an expert at extracting entities and relationships from enterprise documents. 

Analyze the following text and extract:
1. **Entities**: Important people, organizations, projects, systems, documents, and concepts
2. **Relationships**: How these entities are connected

**Entity Types** (use these exact values):
{', '.join(sample_entities[:12])}... and others

**Relationship Types** (use these exact values):
{', '.join(sample_relationships[:9])}... and others

**Instructions:**
- Only extract entities that are clearly mentioned in the text
- Only create relationships that are explicitly stated or strongly implied
- Use the exact entity and relationship type names provided
- For entities, provide a brief description
- For relationships, include a description of the connection

**Text to analyze:**
{text}

**Return JSON format:**
{{
    "entities": [
        {{
            "name": "entity_name",
            "type": "EntityType",
            "description": "brief description of the entity"
        }}
    ],
    "relationships": [
        {{
            "source": "source_entity_name",
            "target": "target_entity_name", 
            "type": "RELATIONSHIP_TYPE",
            "description": "description of the relationship"
        }}
    ]
}}

**Important:** Only return valid JSON. Do not include any other text or explanations.
"""
    
    return prompt


def create_query_analysis_prompt(query: str) -> str:
    """
    Create a prompt for analyzing search queries to determine routing strategy.
    
    Args:
        query: The search query to analyze
        
    Returns:
        Formatted prompt string
    """
    
    prompt = f"""
You are an expert at analyzing search queries to determine the best search strategy.

Analyze this query and determine:
1. Whether it needs simple vector search or complex graph search
2. What type of information the user is looking for
3. Whether it involves relationships between entities

**Query Types:**
- **Simple**: Factual questions about policies, procedures, or content (What is the vacation policy?)
- **Relational**: Questions about connections between people, projects, or entities (Who reports to John?, What projects is the marketing team working on?)
- **Entity-focused**: Questions about specific people, departments, or systems (Tell me about the CRM system)

**Query to analyze:**
{query}

**Return JSON format:**
{{
    "query_type": "simple|relational|entity_focused",
    "needs_graph_search": true|false,
    "entity_focus": true|false,
    "relationship_focus": true|false,
    "reasoning": "brief explanation of the analysis"
}}

**Important:** Only return valid JSON. Do not include any other text.
"""
    
    return prompt


def get_entity_types_for_prompt() -> List[str]:
    """Get a curated list of entity types for prompts."""
    return [e.value for e in EntityType]


def get_relationship_types_for_prompt() -> List[str]:
    """Get a curated list of relationship types for prompts."""
    return [r.value for r in RelationshipType]