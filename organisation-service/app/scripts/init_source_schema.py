import structlog
from app.modules.connectors.handlers.gdrive.source.neo4j_setup import create_source_schema

logger = structlog.get_logger()

def main():
    """
    Initialize the Source schema in Neo4j.
    """
    logger.info("Initializing Source schema in Neo4j")
    
    try:
        create_source_schema()
        logger.info("Source schema initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing Source schema: {str(e)}")

if __name__ == "__main__":
    main()