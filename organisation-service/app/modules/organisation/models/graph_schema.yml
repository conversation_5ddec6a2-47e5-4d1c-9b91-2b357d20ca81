# graph_schema.yml
version: 1.0
# Add Node and <PERSON><PERSON> at the last, don't insert in between
nodes:
  Organisation:
    properties:
      id:
        type: string
        required: true
      name: string
      website_url: string
      industry: string
      created_by: string  # User ID
      created_at: timestamp
      updated_at: timestamp

  User:
    properties:
      id:
        type: string
        required: true
      email:
        type: string
        required: true
      organisation_id:
        type: string
        required: true
      name: string
      creation_type: string  # e.g., "manual" or "auto_created"
      created_at: timestamp
      updated_at: timestamp

  Department:
    properties:
      id:
        type: string
        required: true
      name: string
      description: string
      organisation_id: string
      visibility: string  # e.g., "public" or "private"
      created_by: string  # User ID
      created_at: timestamp
      updated_at: timestamp

relationships:
  CREATED_BY:
    from: Organisation
    to: User
    description: "Links an organisation to its admin creator"
    direction: "->"

  HAS_DEPARTMENT:
    from: Organisation
    to: Department
    description: "Organisation contains departments"
    direction: "->"

  BELONGS_TO:
    from: User
    to: Department
    description: "User membership in a department"
    properties:
      role: string
      joined_at: timestamp
      invited_by: string  # User ID (nullable)
      permission: string  # e.g., "admin" or "member"
    direction: "->"
  
  HAS_SUBDEPARTMENT:
    from: Department
    to: Department
    description: "Parent-child department relationship"
    direction: "->"