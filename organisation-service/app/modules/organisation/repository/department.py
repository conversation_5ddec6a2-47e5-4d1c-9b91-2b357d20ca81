from app.modules.organisation.models.schema_loader import schema

class DepartmentQueries:
    def __init__(self):
        # Get schema definitions
        self.org_label = schema.get_node_labels()[0]  # "Organisation"
        self.user_label = schema.get_node_labels()[1]  # "User"
        self.dept_label = schema.get_node_labels()[2]  # "Department"
        self.org_creater_rel = schema.get_relationship_types()[0] # "CREATED_BY"
        self.org_dept_rel = schema.get_relationship_types()[1]  # "HAS_DEPARTMENT"
        self.user_dept_rel = schema.get_relationship_types()[2]  # "BELONGS_TO"
        self.subdept_rel = schema.get_relationship_types()[3] # "HAS_SUBDEPARTMENT"

    @property
    def VALIDATE_DEPARTMENT_CREATION(self):
        return f"""
            // Check if organisation exists
            OPTIONAL MATCH (o:{self.org_label} {{id: $org_id}})
            
            // Check if user is admin in the General department
            OPTIONAL MATCH (o)-[:{self.org_dept_rel}]->(general:{self.dept_label} {{name: $general_dept}})
                      <-[admin_rel:{self.user_dept_rel} {{role: $admin_role}}]-(u:{self.user_label} {{id: $user_id}})
            
            // Check if department name already exists (case-insensitive)
            OPTIONAL MATCH (o)-[:{self.org_dept_rel}]->(existing:{self.dept_label})
            WHERE toLower(existing.name) = toLower($dept_name)
            
            // Check if parent department exists (if specified)
            OPTIONAL MATCH (parent:{self.dept_label} {{id: $parent_id}})
            WHERE $parent_id IS NOT NULL
            
            RETURN 
                o IS NOT NULL as org_exists,
                admin_rel.role as user_role,
                existing IS NOT NULL as dept_exists,
                CASE 
                    WHEN $parent_id IS NULL THEN true 
                    ELSE parent IS NOT NULL 
                END as parent_valid
        """

    @property
    def CREATE_DEPARTMENT(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $org_id}})
            MATCH (u:{self.user_label} {{id: $created_by}})
            
            CREATE (d:{self.dept_label} {{
                id: $id,
                name: $name,
                description: $description,
                parent_department_id: $parent_id,
                created_by: $created_by,
                created_at: $created_at,
                updated_at: $updated_at,
                visibility: $visibility,
                organisation_id: $org_id
            }})
            
            CREATE (o)-[:{self.org_dept_rel}]->(d)
            CREATE (u)-[:{self.user_dept_rel} {{
                role: $user_role, 
                permission: $user_permission,
                joined_at: $joined_at
            }}]->(d)
            
            // Conditionally create parent relationship
            WITH d
            CALL apoc.do.when(
                $parent_id IS NOT NULL,
                'MATCH (p:{self.dept_label} {{id: $parent_id}}) CREATE (p)-[:{self.subdept_rel}]->(d) RETURN d',
                'RETURN d',
                {{d: d, parent_id: $parent_id}}
            ) YIELD value
            
            RETURN d
        """
        
    @property
    def GET_DEPARTMENT_USERS(self):
        return f"""
            // Match the organization first
            MATCH (o:{self.org_label} {{id: $org_id}})
            
            // Match the department - if department_id is provided, use that; otherwise find the GENERAL department
            CALL (o) {{
                MATCH (o)-[:{self.org_dept_rel}]->(d:{self.dept_label})
                WHERE CASE
                    WHEN $dept_id IS NOT NULL THEN d.id = $dept_id
                    ELSE toLower(d.name) = toLower($default_dept_name)
                END
                RETURN d
            }}
            
            // Find users that belong to this department
            MATCH (u:{self.user_label})-[rel:{self.user_dept_rel}]->(d)
            
            // Return user information along with their role and permission in the department
            RETURN {{
                id: u.id,
                name: u.name,
                email: u.email,
                role: COALESCE(rel.role, "MEMBER"),
                permission: COALESCE(rel.permission, "READ")
            }} as user
            
            // Apply sorting and pagination
            ORDER BY u.name
            SKIP $skip
            LIMIT $limit
        """
        
    @property
    def GET_DEPARTMENT_USERS_COUNT(self):
        return f"""
            // Match the organization first
            MATCH (o:{self.org_label} {{id: $org_id}})
            
            // Match the department - if department_id is provided, use that; otherwise find the GENERAL department
            CALL (o) {{
                MATCH (o)-[:{self.org_dept_rel}]->(d:{self.dept_label})
                WHERE CASE
                    WHEN $dept_id IS NOT NULL THEN d.id = $dept_id
                    ELSE toLower(d.name) = toLower($default_dept_name)
                END
                RETURN d
            }}
            
            // Find users that belong to this department and count them
            MATCH (u:{self.user_label})-[rel:{self.user_dept_rel}]->(d)
            
            RETURN count(u) as user_count, d
            """
    
    @property
    def VALIDATE_DEPARTMENT_EXISTS(self):
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        RETURN d
        """

    @property
    def VALIDATE_USER_DEPARTMENT_PERMISSION(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        MATCH (u)-[r:{self.user_dept_rel}]->(d)
        RETURN
            true as is_member,
            r.role = 'ADMIN' as is_admin
        """

    @property
    def GRANT_DEPARTMENT_FILE_ACCESS(self):
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        MATCH (f:GoogleDriveFile)
        WHERE f.id IN $file_ids
        MERGE (d)-[r:HAS_ACCESS]->(f)
        ON CREATE SET r.granted_at = $current_time, r.granted_by = $user_id
        RETURN count(f) as file_count
        """

    @property
    def GRANT_DEPARTMENT_FOLDER_ACCESS(self):
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        MATCH (f:GoogleDriveFolder)
        WHERE f.id IN $folder_ids
        MERGE (d)-[r:HAS_ACCESS]->(f)
        ON CREATE SET r.granted_at = $current_time, r.granted_by = $user_id
        RETURN count(f) as folder_count
        """
    
    @property
    def GET_DEPARTMENT_USERS_FOR_CACHE(self):
        return f"""
        MATCH (u:{self.user_label})-[:{self.user_dept_rel}]->(d:{self.dept_label} {{id: $dept_id}})
        RETURN u.id as user_id
        """

    @property
    def LIST_TOP_LEVEL_FOLDERS_USER_DIRECT_DEPT_DIRECT(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:HAS_ACCESS]->(f:GoogleDriveFolder)
        MATCH (d:{self.dept_label} {{id: $dept_id}})-[:HAS_ACCESS]->(f)
        WHERE NOT EXISTS {{
            MATCH (parent:GoogleDriveFolder)-[:CONTAINS]->(f)
        }}
        RETURN f.id as id, f.name as name
        """

    @property
    def LIST_TOP_LEVEL_FOLDERS_USER_DEPT_DEPT_DIRECT(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.user_dept_rel}]->(ud:{self.dept_label})-[:HAS_ACCESS]->(f:GoogleDriveFolder)
        MATCH (d:{self.dept_label} {{id: $dept_id}})-[:HAS_ACCESS]->(f)
        WHERE NOT EXISTS {{
            MATCH (parent:GoogleDriveFolder)-[:CONTAINS]->(f)
        }}
        RETURN f.id as id, f.name as name
        """

    @property
    def LIST_TOP_LEVEL_FOLDERS_DIRECT_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:HAS_ACCESS]->(f:GoogleDriveFolder)
        WHERE NOT EXISTS {{
            MATCH (parent:GoogleDriveFolder)-[:CONTAINS]->(f)
        }}
        RETURN f.id as id, f.name as name
        """

    @property
    def LIST_TOP_LEVEL_FOLDERS_DEPT_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.user_dept_rel}]->(d:{self.dept_label})-[:HAS_ACCESS]->(f:GoogleDriveFolder)
        WHERE NOT EXISTS {{
            MATCH (parent:GoogleDriveFolder)-[:CONTAINS]->(f)
        }}
        RETURN f.id as id, f.name as name
        """
        
    @property
    def VALIDATE_ADD_MEMBER_TO_DEPARTMENT(self):
        return f"""
        // Match the organization, department, and both users
        MATCH (o:{self.org_label} {{id: $org_id}})
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        MATCH (admin_user:{self.user_label} {{id: $admin_id}})
        MATCH (member_user:{self.user_label} {{id: $member_id}})
        
        // Check if both users belong to the organization (via any department)
        MATCH (admin_user)-[:{self.user_dept_rel}]->(admin_dept:{self.dept_label})<-[:{self.org_dept_rel}]-(o)
        MATCH (member_user)-[:{self.user_dept_rel}]->(member_dept:{self.dept_label})<-[:{self.org_dept_rel}]-(o)
        
        // Check if admin user is either:
        // 1. An admin of the department, or
        // 2. The creator of the organization
        OPTIONAL MATCH (admin_user)-[admin_rel:{self.user_dept_rel} {{role: 'ADMIN'}}]->(d)
        OPTIONAL MATCH (o)-[:{self.org_creater_rel}]->(admin_user)
        
        // Check if member is already in the department
        OPTIONAL MATCH (member_user)-[existing_rel:{self.user_dept_rel}]->(d)
        
        RETURN
            o IS NOT NULL as org_exists,
            d IS NOT NULL as dept_exists,
            admin_user IS NOT NULL as admin_exists,
            member_user IS NOT NULL as member_exists,
            admin_rel IS NOT NULL OR EXISTS((o)-[:{self.org_creater_rel}]->(admin_user)) as is_authorized,
            existing_rel IS NOT NULL as already_member
        """
        
    @property
    def ADD_MEMBER_TO_DEPARTMENT(self):
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        MATCH (u:{self.user_label} {{id: $member_id}})
        
        CREATE (u)-[:{self.user_dept_rel} {{
            role: $role,
            permission: $permission,
            joined_at: $timestamp,
            invited_by: $admin_id
        }}]->(d)
        
        RETURN d, u
        """
        
    @property
    def GET_DEPARTMENTS_FOR_USERS(self):
        return f"""
        // Match the organization first
        MATCH (o:{self.org_label} {{id: $org_id}})
        
        // Match all users and their departments in a single query
        MATCH (u:{self.user_label})-[rel:{self.user_dept_rel}]->(d:{self.dept_label})<-[:{self.org_dept_rel}]-(o)
        WHERE u.id IN $user_ids
        AND ($dept_id IS NULL OR d.id = $dept_id)
        
        // Return user ID and department information
        RETURN u.id as user_id, {{
            id: d.id,
            name: d.name,
            description: d.description,
            role: COALESCE(rel.role, "MEMBER"),
            permission: COALESCE(rel.permission, "READ")
        }} as department
        
        // Apply sorting
        ORDER BY u.id, d.name
        """