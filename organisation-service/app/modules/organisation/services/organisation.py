import json
import secrets
import string
import uuid
from datetime import datetime

import grpc
import structlog

from app.db.neo4j import get_neo4j_transaction
from app.grpc_ import organisation_pb2, organisation_pb2_grpc
from app.modules.organisation.repository.organisation import OrganisationQuery
from app.modules.organisation.services.department import DepartmentService
from app.modules.organisation.services.invitation import InvitationService
from app.modules.organisation.services.source import SourceService
from app.modules.organisation.utils.common import OrganisationMembershipChecker
from app.services.neo4j_service import execute_read_query, execute_write_query
from app.utils.constants.departments import (
    DEFAULT_DEPARTMENT_CONFIG,
    DefaultDepartments,
    DefaultPermissions,
    DefaultRoles,
    Visibility,
)
from app.utils.constants.sources import SourceType
from app.utils.constants.user_creation_type import UserCreationType

logger = structlog.get_logger()


class OrganisationService(organisation_pb2_grpc.OrganisationServiceServicer):
    """
    Service handling organisation and department operations in Neo4j database.
    """

    def __init__(self):
        self.queries = OrganisationQuery()
        self.source_service = SourceService()
        self.department_service = DepartmentService()
        self.invitation_service = InvitationService()

    def generate_mcp_key(self, org_id: str) -> str:
        """
        Generate a unique MCP API key for an organization.

        Args:
            org_id: The organization ID to include in the key

        Returns:
            str: A unique API key in the format 'mcp_org_{org_id}_{random_string}'
        """
        # Generate a random string of 24 characters
        alphabet = string.ascii_letters + string.digits
        random_part = "".join(secrets.choice(alphabet) for _ in range(24))

        # Create the key with a prefix and the org_id
        return f"sk-{org_id}-{random_part}"

    def validateUserOrganisationMembership(
        self, user_id: str, organisation_id: str
    ) -> bool:
        """
        Validate if a user is a member of the given organisation.

        Args:
            user_id: The user ID to validate
            organisation_id: The organisation ID to check membership for

        Returns:
            bool: True if user is a member of the organisation, False otherwise
        """
        try:
            query = self.queries.VALIDATE_USER_ORGANISATION_MEMBERSHIP
            params = {"user_id": user_id, "organisation_id": organisation_id}
            result = execute_read_query(query, params)

            if result and len(result) > 0:
                return result[0].get("is_member", False)
            return False

        except Exception as e:
            logger.error(
                "Error validating user organisation membership",
                user_id=user_id,
                organisation_id=organisation_id,
                error=str(e),
            )
            return False

    def grantDepartmentAccess(self, request, context):
        """
        Grant a department access to specific files and folders.
        Delegates to DepartmentService.
        """
        return self.department_service.grantDepartmentAccess(request, context)

    def batchGrantDepartmentAccess(self, request, context):
        """
        Grant multiple departments access to specific files and folders in a batch operation.
        Delegates to DepartmentService.
        """
        return self.department_service.batchGrantDepartmentAccess(request, context)

    def listTopLevelFolders(self, request, context):
        """
        List all top-level folders (those without parent folders) that a user has access to.
        Delegates to DepartmentService.
        """
        return self.department_service.listTopLevelFolders(request, context)

    def createOrganisation(
        self,
        request: organisation_pb2.CreateOrganisationRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.OrganisationResponse:
        """
        Create a new organisation in Neo4j.
        First checks if the user exists and creates a user node if not.
        Also creates default departments for the organisation based on configuration.

        Args:
            request: Contains organisation details including optional logo
            context: gRPC context

        Returns:
            OrganisationResponse with created organisation details
        """
        logger.info("Received request to create organisation", name=request.name)
        request.name = request.name.upper()

        try:
            # Check if user is already part of any organisation
            if OrganisationMembershipChecker.isUserPartOfAnyOrganisation(
                request.created_by,
                execute_read_query,
                self.queries.CHECK_USER_IN_ANY_ORGANISATION,
            ):
                logger.warning(
                    "User is already part of an organisation",
                    user_id=request.created_by,
                )
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "User is already part of an organisation and cannot create another one"
                )
                return organisation_pb2.OrganisationResponse(
                    message="User is already part of an organisation and cannot create another one",
                    success=False,
                )
            org_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()

            # Generate a unique MCP key for this organization
            mcp_key = self.generate_mcp_key(org_id)

            # Prepare department data
            DEFAULT_DEPARTMENTS = list(DEFAULT_DEPARTMENT_CONFIG.keys())
            departments_data = []

            for dept_name in DEFAULT_DEPARTMENTS:
                dept_config = DEFAULT_DEPARTMENT_CONFIG.get(dept_name, {})
                departments_data.append(
                    {
                        "id": str(uuid.uuid4()),
                        "name": dept_name,
                        "description": dept_config.get("description", ""),
                        "visibility": Visibility.PUBLIC.value,
                    }
                )

            # Get logo from request if available, otherwise set to None
            logo = getattr(request, "logo", None)

            with get_neo4j_transaction() as tx:
                # STEP 1: Create Organisation and Admin User (Core entities)
                result_step1 = execute_write_query(
                    self.queries.CREATE_ORG_AND_ADMIN,
                    {
                        "org_id": org_id,
                        "org_name": request.name,
                        "website_url": request.website_url,
                        "industry": request.industry,
                        "admin_id": request.created_by,
                        "admin_name": request.admin_name,
                        "admin_email": request.admin_email,
                        "timestamp": current_time,
                        "creation_type": UserCreationType.SIGNED_IN.value,
                        "auto_created": UserCreationType.AUTO_CREATED.value,
                        "logo": logo,
                        "mcp_key": mcp_key,
                    },
                    tx,
                )

                if not result_step1:
                    logger.error("Failed to create organization and admin user")
                    # tx.rollback()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to create organization")
                    return organisation_pb2.OrganisationResponse()

                # STEP 2: Create Departments in Batch (Split into smaller batches if needed)
                result_step2 = execute_write_query(
                    self.queries.CREATE_DEPARTMENTS_BATCH,
                    {
                        "org_id": org_id,
                        "admin_id": request.created_by,
                        "departments": departments_data,
                        "timestamp": current_time,
                        "admin_role": DefaultRoles.ADMIN.value,
                        "admin_permission": DefaultPermissions.UNIVERSAL.value,
                    },
                    tx,
                )

                if not result_step2:
                    logger.error(f"Failed to create departments")
                    # tx.rollback()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to create departments")
                    return organisation_pb2.OrganisationResponse()

                logger.info(
                    f"Successfully created organisation {org_id} with {len(departments_data)} departments"
                )

            # Create OrganisationModel
            org_model = organisation_pb2.OrganisationModel(
                id=org_id,
                name=request.name,
                website_url=request.website_url,
                logo=request.logo,
                industry=request.industry,
                created_by=request.created_by,
                created_at=current_time,
                updated_at=current_time,
                mcp_key=mcp_key,
                is_key_revoked=False,
            )

            # Create OrganisationAndDeptResponse with the OrganisationModel
            org_dept_response = organisation_pb2.OrganisationAndDeptResponse(
                organisation=org_model
            )

            # Add departments to the response
            for dept_data in departments_data:
                department = org_dept_response.departments.add()
                department.id = dept_data.get("id", "")
                department.name = dept_data.get("name", "")
                department.description = dept_data.get("description", "")
                department.member_count = 1  # New departments has 1 member admin
                department.visibility = dept_data.get("visibility", "")
                department.agent_count = 0

            # Return the response with the properly nested structure
            return organisation_pb2.OrganisationResponse(
                organisation=org_dept_response,
                message=f"Organisation created successfully with {len(departments_data)} default departments",
                success=True,
            )

        except Exception as e:
            logger.error("Error creating organisation", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error creating organisation: {str(e)}")
            return organisation_pb2.OrganisationResponse()

    def getOrganisation(
        self,
        request: organisation_pb2.GetOrganisationRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.OrganisationResponse:
        """
        Get organisation details by ID.
        """
        logger.info("Received request to get organisation", id=request.id)

        try:
            # Enhanced query to get organisation with member count
            result = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.id}
            )

            if not result:
                logger.error("Organisation not found", id=request.id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organisation with ID {request.id} not found")
                return organisation_pb2.OrganisationResponse()

            org_dept_response = self.convertToProto(result)

            if org_dept_response is None:
                logger.error("Failed to convert organisation data to proto format")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to process organisation data")
                return organisation_pb2.OrganisationResponse(
                    success=False, message="Failed to process organisation data"
                )

            # Return the response with the OrganisationAndDeptResponse in the organisation field
            return organisation_pb2.OrganisationResponse(
                organisation=org_dept_response,
                message="Organisation retrieved successfully",
                success=True,
            )

        except Exception as e:
            logger.error("Error getting organisation", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving organisation: {str(e)}")
            return organisation_pb2.OrganisationResponse()

    def convertToProto(self, result):
        """
        Converts Neo4j query result to OrganisationAndDeptResponse proto object
        """
        if not result or not result[0]:
            return None

        org_data = result[0].get("o", {})
        departments_data = result[0].get("departments", [])

        # Create OrganisationModel first
        org_model = organisation_pb2.OrganisationModel(
            id=org_data.get("id", ""),
            name=org_data.get("name", ""),
            website_url=org_data.get("website_url", ""),
            logo=org_data.get("logo", ""),
            industry=org_data.get("industry", ""),
            created_by=org_data.get("created_by", ""),
            created_at=org_data.get("created_at", ""),
            updated_at=org_data.get("updated_at", ""),
        )

        # Add MCP key if it exists
        if "mcp_key" in org_data:
            org_model.mcp_key = org_data.get("mcp_key", "")

        # Add key revocation status if it exists
        if "is_key_revoked" in org_data:
            org_model.is_key_revoked = org_data.get("is_key_revoked", False)

        # Create OrganisationAndDeptResponse with the OrganisationModel
        org_dept_response = organisation_pb2.OrganisationAndDeptResponse(
            organisation=org_model
        )
        # Add departments
        for dept_data in departments_data:
            if not dept_data:
                continue

            # Create a new department and add it to the repeated field
            department = (
                org_dept_response.departments.add()
            )  # This creates and adds a new Department
            department.id = dept_data.get("id", "")
            department.name = dept_data.get("name", "")
            department.description = dept_data.get("description", "")
            department.member_count = dept_data.get("member_count", 0)

        return org_dept_response

    def createDepartment(
        self,
        request: organisation_pb2.CreateDepartmentRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.DepartmentResponse:
        """
        Create a new department within an organisation.
        Only admins can create departments, and department names must be unique within an organisation.
        """
        return self.department_service.createDepartment(request, context)

    def inviteUser(
        self, request: organisation_pb2.InviteUserRequest, context: grpc.ServicerContext
    ) -> organisation_pb2.InviteResponse:
        """
        Create a new invite for a user to join an organisation.
        This will:
        1. Check if user is already in the department
        2. Check if there's an existing non-expired invite
        3. Create an invite record with a unique link in Postgres
        4. Send an email notification via Kafka with the link
        """
        return self.invitation_service.inviteUser(request, context)

    def listDepartments(self, request, context):
        """
        List all departments in an organisation.
        Supports pagination and optional search filtering.
        Supports filtering by specific department ID.
        """
        return self.department_service.listDepartments(request, context)

    def acceptInviteByLink(
        self,
        request: organisation_pb2.AcceptInviteByLinkRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.InviteResponse:
        """
        Accept or reject an invitation using an invite link.
        This will:
        1. Decode the link to get the invite information (supports both short token and full encoded payload)
        2. Verify that the invite exists, is pending, and hasn't expired
        3. Verify the user email matches the invited email
        4. Update the invite status to ACCEPTED or DECLINED based on the accept flag
        5. If accepting: Create the user in Neo4j if they don't already exist
        6. If accepting: Create relationships between user, organisation, and department in Neo4j
        7. If accepting: Ensure user is added to General department alongside any other department
        """
        return self.invitation_service.acceptInviteByLink(request, context)

    def getUserOrganisations(self, request, context):
        """
        Fetch the current user's organisations, pending invites, and related metadata.
        Returns:
        1. All organisations the user belongs to
        2. Pending invites with organisation, department, role, etc.
        3. personal_space flag: true if user has a primary org (can't create more)
        4. has_joined flag: true if user has joined any org with an invite (can't join more)
        """
        logger.info(
            "Received request to get user organisations", user_id=request.user_id
        )

        try:
            org_records = execute_read_query(
                self.queries.FETCH_ORG_DETAILS_OF_USER,
                {
                    "user_email": request.user_id,
                    "dept": DefaultDepartments.GENERAL.value,
                    "user_role": DefaultRoles.ADMIN.value,
                },
            )

            # Process the organisations
            all_orgs = []
            has_primary_org = False
            has_joined_org = False

            for record in org_records or []:
                org_data = record["o"]
                is_primary = record["creator_id"] == request.user_id
                is_admin = record.get("is_admin", False)

                # Update flags
                has_primary_org |= is_primary
                has_joined_org |= not is_primary

                # Convert to protobuf models
                # Create organisation model
                org_model = organisation_pb2.OrganisationModel(
                    id=org_data.get("id", ""),
                    name=org_data.get("name", ""),
                    website_url=org_data.get("website_url", ""),
                    industry=org_data.get("industry", ""),
                    logo=org_data.get("logo", ""),
                    created_by=org_data.get("created_by", ""),
                    created_at=org_data.get("created_at", ""),
                    updated_at=org_data.get("updated_at", ""),
                )

                # Add MCP key if it exists
                if "mcp_key" in org_data:
                    org_model.mcp_key = org_data.get("mcp_key", "")

                # Add key revocation status if it exists
                if "is_key_revoked" in org_data:
                    org_model.is_key_revoked = org_data.get("is_key_revoked", False)

                all_orgs.append(
                    organisation_pb2.UserOrganisation(
                        organisation=org_model, is_primary=is_primary, is_admin=is_admin
                    )
                )

            # Get pending invites in optimized batch
            pending_invites = self.invitation_service.get_pending_invites(
                request.user_id
            )

            return organisation_pb2.UserOrganisationsResponse(
                organisations=all_orgs,
                pending_invites=pending_invites,
                personal_space=has_primary_org,
                has_joined=has_joined_org,
                message=(
                    "Organisations and invites retrieved successfully"
                    if all_orgs or pending_invites
                    else "No organisations or invites found"
                ),
                success=True,
            )

        except Exception as e:
            logger.error(
                "Error getting user organisations", error=str(e), exc_info=True
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving organisations: {str(e)}")
            return organisation_pb2.UserOrganisationsResponse()

    def addSource(self, request, context):
        """
        Add a new source with credentials to an organization.
        Delegates to SourceService.
        """
        return self.source_service.addSource(request, context)

    def listSources(self, request, context):
        """
        List all sources for an organization.
        Delegates to SourceService.
        """
        return self.source_service.listSources(request, context)

    def deleteSource(self, request, context):
        """
        Delete a source.
        Delegates to SourceService.
        """
        return self.source_service.deleteSource(request, context)

    def get_source_by_id(self, request, context):
        """
        Get a source by its ID.
        Delegates to SourceService.
        """
        return self.source_service.getSourceById(request, context)

    def getInviterInvites(self, request, context):
        """
        Get invites sent by a user (as inviter).
        Returns ACCEPTED invites (from Neo4j) or PENDING invites (from Postgres) based on request.type.
        """
        return self.invitation_service.get_inviter_invites(request, context)

    def getDepartmentUsers(self, request, context):
        """
        Get all users in a department.
        If department ID is not provided, defaults to retrieving users from the GENERAL department.
        """
        logger.info(
            "Forwarding request to get department users",
            org_id=request.organisation_id,
            dept_id=request.department_id if request.department_id else "GENERAL",
        )
        return self.department_service.getDepartmentUsers(request, context)

    def updateSourceCredentials(self, request, context):
        """Update source credentials."""
        return self.source_service.updateSourceCredentials(request, context)

    def validateSource(self, request, context):
        """Validate source and return accessible folders."""
        return self.source_service.validateSource(request, context)

    def listDepartmentFolders(self, request, context):
        """
        List folders accessible by specified departments.
        Delegates to DepartmentService.
        """
        logger.info(
            "OrganisationService: Received listDepartmentFolders request - delegating to DepartmentService",
            org_id=request.organisation_id,
            dept_ids=list(request.department_ids),
        )
        return self.department_service.listDepartmentFolders(request, context)

    def addMemberToDepartment(self, request, context):
        """
        Add a member to a department.
        Delegates to DepartmentService.
        """
        logger.info(
            "OrganisationService: Received addMemberToDepartment request - delegating to DepartmentService",
            org_id=request.organisation_id,
            dept_id=request.department_id,
            admin_id=request.user_id,
            member_id=request.member_id,
        )
        return self.department_service.addMemberToDepartment(request, context)

    def updateOrganisation(self, request, context):
        """
        Update an existing organisation's details.
        Only the admin of the organisation or the creator can update the organisation.

        Args:
            request: Contains organisation ID, user ID, and updated details
            context: gRPC context

        Returns:
            OrganisationResponse with updated organisation details
        """
        logger.info(
            "Received request to update organisation",
            id=request.id,
            user_id=request.user_id,
        )

        try:
            # Verify the organisation exists
            org_check = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.id}
            )

            if not org_check:
                logger.error("Organisation not found", id=request.id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organisation with ID {request.id} not found")
                return organisation_pb2.OrganisationResponse()

            # Get existing organisation data
            existing_org = org_check[0]["o"]

            # Check if the user is the creator of the organisation
            is_creator = existing_org.get("created_by") == request.user_id

            # If not the creator, check if the user is an admin
            is_admin = False
            if not is_creator:
                # Check if the user is an admin of the organisation
                admin_check_query = """
                MATCH (u:User {id: $user_id})-[r:BELONGS_TO {role: 'ADMIN'}]->(d:Department)<-[:HAS_DEPARTMENT]-(o:Organisation {id: $org_id})
                RETURN COUNT(u) > 0 as is_admin
                """
                admin_check_params = {"user_id": request.user_id, "org_id": request.id}
                admin_check_result = execute_read_query(
                    admin_check_query, admin_check_params
                )
                is_admin = admin_check_result and admin_check_result[0].get(
                    "is_admin", False
                )

            # If the user is neither the creator nor an admin, deny access
            if not (is_creator or is_admin):
                logger.error(
                    "User not authorized to update organisation",
                    user_id=request.user_id,
                    org_id=request.id,
                )
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "Only organisation admins or creators can update organisation details"
                )
                return organisation_pb2.OrganisationResponse()

            current_time = datetime.utcnow().isoformat()

            # Prepare update parameters with only the fields that are present in the request
            update_params = {
                "org_id": request.id,
                "timestamp": current_time,
                # Default to existing values
                "org_name": existing_org.get("name"),
                "website_url": existing_org.get("website_url"),
                "industry": existing_org.get("industry"),
                "logo": existing_org.get("logo"),
            }

            # Only update fields that are present in the request
            if hasattr(request, "name") and request.name:
                update_params["org_name"] = request.name.upper()

            if hasattr(request, "website_url") and request.HasField("website_url"):
                update_params["website_url"] = request.website_url

            if hasattr(request, "industry") and request.HasField("industry"):
                update_params["industry"] = request.industry

            if hasattr(request, "logo") and request.HasField("logo"):
                update_params["logo"] = request.logo

            # Log the fields being updated
            logger.info(
                "Updating organisation fields",
                id=request.id,
                fields={
                    k: v
                    for k, v in update_params.items()
                    if k != "timestamp" and k != "org_id"
                },
            )

            # Update organisation
            result = execute_write_query(
                self.queries.UPDATE_ORGANISATION, update_params
            )

            if not result:
                logger.error("Failed to update organisation", id=request.id)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to update organisation")
                return organisation_pb2.OrganisationResponse()

            # Get updated organisation details
            updated_org = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.id}
            )

            if not updated_org:
                logger.error("Failed to retrieve updated organisation", id=request.id)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to retrieve updated organisation")
                return organisation_pb2.OrganisationResponse()

            org_dept_response = self.convertToProto(updated_org)

            return organisation_pb2.OrganisationResponse(
                organisation=org_dept_response,
                message="Organisation updated successfully",
                success=True,
            )

        except Exception as e:
            logger.error("Error updating organisation", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating organisation: {str(e)}")
            return organisation_pb2.OrganisationResponse()

    def revokeMcpKey(
        self,
        request: organisation_pb2.RevokeMcpKeyRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.OrganisationResponse:
        """
        Revoke an organisation's MCP key.
        Only admins can revoke the key.

        Args:
            request: Contains organisation ID and user ID
            context: gRPC context

        Returns:
            OrganisationResponse with updated organisation details
        """
        logger.info(
            "Received request to revoke MCP key",
            org_id=request.organisation_id,
            user_id=request.user_id,
        )

        try:
            # Verify the organisation exists
            org_check = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.organisation_id}
            )

            if not org_check:
                logger.error("Organisation not found", id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Organisation with ID {request.organisation_id} not found"
                )
                return organisation_pb2.OrganisationResponse()

            # Check if the user is authorized (admin or creator)
            existing_org = org_check[0]["o"]
            is_creator = existing_org.get("created_by") == request.user_id

            # If not the creator, check if the user is an admin
            is_admin = False
            if not is_creator:
                # Check if the user is an admin of the organisation
                admin_check_query = """
                MATCH (u:User {id: $user_id})-[r:BELONGS_TO {role: 'ADMIN'}]->(d:Department)<-[:HAS_DEPARTMENT]-(o:Organisation {id: $org_id})
                RETURN COUNT(u) > 0 as is_admin
                """
                admin_check_params = {
                    "user_id": request.user_id,
                    "org_id": request.organisation_id,
                }
                admin_check_result = execute_read_query(
                    admin_check_query, admin_check_params
                )
                is_admin = admin_check_result and admin_check_result[0].get(
                    "is_admin", False
                )

            # If the user is neither the creator nor an admin, deny access
            if not (is_creator or is_admin):
                logger.error(
                    "User not authorized to revoke MCP key",
                    user_id=request.user_id,
                    org_id=request.organisation_id,
                )
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "Only organisation admins or creators can revoke the MCP key"
                )
                return organisation_pb2.OrganisationResponse()

            # Revoke the MCP key
            result = execute_write_query(
                self.queries.REVOKE_MCP_KEY, {"org_id": request.organisation_id}
            )

            if not result:
                logger.error("Failed to revoke MCP key", id=request.organisation_id)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to revoke MCP key")
                return organisation_pb2.OrganisationResponse()

            # Get updated organisation details
            updated_org = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.organisation_id}
            )

            if not updated_org:
                logger.error(
                    "Failed to retrieve updated organisation",
                    id=request.organisation_id,
                )
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to retrieve updated organisation")
                return organisation_pb2.OrganisationResponse()

            org_dept_response = self.convertToProto(updated_org)

            return organisation_pb2.OrganisationResponse(
                organisation=org_dept_response,
                message="MCP key revoked and deleted successfully",
                success=True,
            )

        except Exception as e:
            logger.error("Error revoking MCP key", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error revoking MCP key: {str(e)}")
            return organisation_pb2.OrganisationResponse()

    def regenerateMcpKey(
        self,
        request: organisation_pb2.RegenerateMcpKeyRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.OrganisationResponse:
        """
        Regenerate an organisation's MCP key.
        Only admins can regenerate the key.

        Args:
            request: Contains organisation ID and user ID
            context: gRPC context

        Returns:
            OrganisationResponse with updated organisation details
        """
        logger.info(
            "Received request to regenerate MCP key",
            org_id=request.organisation_id,
            user_id=request.user_id,
        )

        try:
            # Verify the organisation exists
            org_check = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.organisation_id}
            )

            if not org_check:
                logger.error("Organisation not found", id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Organisation with ID {request.organisation_id} not found"
                )
                return organisation_pb2.OrganisationResponse()

            # Check if the user is authorized (admin or creator)
            existing_org = org_check[0]["o"]
            is_creator = existing_org.get("created_by") == request.user_id

            # If not the creator, check if the user is an admin
            is_admin = False
            if not is_creator:
                # Check if the user is an admin of the organisation
                admin_check_query = """
                MATCH (u:User {id: $user_id})-[r:BELONGS_TO {role: 'ADMIN'}]->(d:Department)<-[:HAS_DEPARTMENT]-(o:Organisation {id: $org_id})
                RETURN COUNT(u) > 0 as is_admin
                """
                admin_check_params = {
                    "user_id": request.user_id,
                    "org_id": request.organisation_id,
                }
                admin_check_result = execute_read_query(
                    admin_check_query, admin_check_params
                )
                is_admin = admin_check_result and admin_check_result[0].get(
                    "is_admin", False
                )

            # If the user is neither the creator nor an admin, deny access
            if not (is_creator or is_admin):
                logger.error(
                    "User not authorized to regenerate MCP key",
                    user_id=request.user_id,
                    org_id=request.organisation_id,
                )
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "Only organisation admins or creators can regenerate the MCP key"
                )
                return organisation_pb2.OrganisationResponse()

            # Generate a new MCP key
            new_mcp_key = self.generate_mcp_key(request.organisation_id)

            # Update the MCP key
            result = execute_write_query(
                self.queries.REGENERATE_MCP_KEY,
                {"org_id": request.organisation_id, "new_mcp_key": new_mcp_key},
            )

            if not result:
                logger.error("Failed to regenerate MCP key", id=request.organisation_id)
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to regenerate MCP key")
                return organisation_pb2.OrganisationResponse()

            # Get updated organisation details
            updated_org = execute_read_query(
                self.queries.FETCH_ORG_DETAILS, {"id": request.organisation_id}
            )

            if not updated_org:
                logger.error(
                    "Failed to retrieve updated organisation",
                    id=request.organisation_id,
                )
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to retrieve updated organisation")
                return organisation_pb2.OrganisationResponse()

            org_dept_response = self.convertToProto(updated_org)

            return organisation_pb2.OrganisationResponse(
                organisation=org_dept_response,
                message="MCP key regenerated successfully",
                success=True,
            )

        except Exception as e:
            logger.error("Error regenerating MCP key", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error regenerating MCP key: {str(e)}")
            return organisation_pb2.OrganisationResponse()

    def getOrganisationWithAdmin(
        self,
        request: organisation_pb2.GetOrganisationWithAdminRequest,
        context: grpc.ServicerContext,
    ) -> organisation_pb2.OrganisationWithAdminResponse:
        """
        Get organisation details with admin information by ID.

        Args:
            request: Contains organisation ID
            context: gRPC context

        Returns:
            OrganisationWithAdminResponse with organisation details and admin information
        """
        logger.info("Received request to get organisation with admin", id=request.id)

        try:
            # Query to get organisation with admin details
            result = execute_read_query(
                self.queries.FETCH_ORG_WITH_ADMIN_DETAILS, {"id": request.id}
            )

            if not result:
                logger.error("Organisation not found", id=request.id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Organisation with ID {request.id} not found")
                return organisation_pb2.OrganisationWithAdminResponse()

            # Convert organisation data to proto
            org_dept_response = self.convertToProto(result)

            if org_dept_response is None:
                logger.error("Failed to convert organisation data to proto format")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to process organisation data")
                return organisation_pb2.OrganisationWithAdminResponse(
                    success=False, message="Failed to process organisation data"
                )

            # Extract admin data from result
            admin_data = result[0].get("admin", {})

            # Create AdminInfo proto
            admin_info = organisation_pb2.AdminInfo(
                admin_id=admin_data.get("id", ""),
                email=admin_data.get("email", ""),
                full_name=admin_data.get("name", ""),
                created_at=admin_data.get("created_at", ""),
                updated_at=admin_data.get("updated_at", ""),
            )

            # Return the response with organisation and admin information
            return organisation_pb2.OrganisationWithAdminResponse(
                organisation=org_dept_response,
                admin=admin_info,
                message="Organisation with admin details retrieved successfully",
                success=True,
            )

        except Exception as e:
            logger.error("Error getting organisation with admin", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving organisation with admin: {str(e)}")
            return organisation_pb2.OrganisationWithAdminResponse()
