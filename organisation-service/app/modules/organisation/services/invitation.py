import uuid
import json
import base64
from datetime import datetime
import structlog
import grpc

from app.services.neo4j_service import execute_write_query, execute_read_query
from app.modules.organisation.utils.common import OrganisationMembershipChecker
from app.modules.organisation.repository.invitation import InvitationQueries
from app.modules.organisation.repository.organisation import OrganisationQuery
from app.grpc_ import organisation_pb2
from app.utils.constants.departments import (
    DefaultRoles,
    DefaultDepartments,
    DefaultPermissions
)
from app.db.postgres import db_session
from app.core.config import settings
from sqlalchemy import select, text
from app.modules.organisation.models.invites import Invite, InviteStatus
from app.utils.constants.invites import DEFAULT_INVITE_EXPIRATION
from app.utils.constants.user_creation_type import UserCreationType
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum, KafkaAction
from app.utils.helper import HelperService

logger = structlog.get_logger()
      

class InvitationService():
    def __init__(self):
        self.queries = InvitationQueries()
        self.helper = HelperService()

    def _generate_invite_token(self, invite_id, email, org_id, department, role, permission):
        """
        Generate a secure invite link with encoded information
        """
        # Create a payload with the necessary information
        payload = {
            "invite_id": invite_id,
            "email": email,
            "org_id": org_id,
            "department": department,
            "role": role,
            "permission": permission
        }
        
        # Convert the payload to JSON and encode it
        json_payload = json.dumps(payload)
        encoded_payload = base64.urlsafe_b64encode(json_payload.encode()).decode()

        # Store this full encoded payload as the invite_token (for backward compatibility)
        return encoded_payload
    
    def generate_short_invite_url(self, short_token):
        """
        Generate a short invite URL using the short token
        """
        return f"{settings.FRONTEND_URL}/authenticate?token={short_token}&accept=true"
    
    def inviteUser(self, request, context):
        logger.info("Received request to invite user", email=request.email, org_id=request.organisation_id)
        
        try:
            # Set defaults early
            role = getattr(request, 'role', None).upper() or DefaultRoles.MEMBER.value
            department = getattr(request, 'department', None).upper() or DefaultDepartments.GENERAL.value
            permission = getattr(request, 'permission', None).upper() or DefaultPermissions.READ.value
            
            # Single comprehensive query to check:
            # 1. Admin permissions
            # 2. Department existence
            # 3. User membership status
            # 4. Existing active invites
            result = execute_read_query(self.queries.VERIFY_ADMIN_AND_DEPARTMENT, {
                "organisation_id": request.organisation_id,
                "user_id": request.created_by,
                "general_dept": DefaultDepartments.GENERAL.value,
                "target_dept_name": department,
                "target_email": request.email
            })
            
            # Validate results from single query
            if not result or result[0]['admin_role'] != DefaultRoles.ADMIN.value:
                logger.warning("Not Admin", email=request.email, org_id=request.organisation_id)
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Not the Organisation Admin")
                return organisation_pb2.InviteResponse(message="Invite sent successfully", success=False)
            
            if not result[0]['dept_exists']:
                logger.warning("Department not found", department=department, org_id=request.organisation_id)
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Department '{department}' not found in this organisation")
                return organisation_pb2.InviteResponse(message="Department not found", success=False)
            
            if result[0]['user_exists']:
                logger.warning("User is already a member of this department",
                            email=request.email, org_id=request.organisation_id, department=department)
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"User {request.email} is already a member of the {department} department")
                return organisation_pb2.InviteResponse(message="Invite sent successfully", success=False)
            
            # Check for existing active invites and create new invite atomically
            now = datetime.utcnow()
            expires_at = now + DEFAULT_INVITE_EXPIRATION
            
            # First check for existing active invites
            existing_invite_query = """
                SELECT id FROM invites 
                WHERE email = :email 
                AND organisation_id = :org_id 
                AND department = :department 
                AND status = :status
                AND (expires_at > :now OR expires_at IS NULL)
            """
            
            existing_result = db_session.execute(text(existing_invite_query), {
                'email': request.email,
                'org_id': request.organisation_id,
                'department': department,
                'status': InviteStatus.PENDING.value,
                'now': now
            }).fetchone()
            
            if existing_result:
                logger.warning("Active invite already exists for this user",
                            email=request.email, org_id=request.organisation_id)
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"An active invite already exists for {request.email}")
                return organisation_pb2.InviteResponse(message="Invite sent successfully", success=False)
            
            # Generate invite data
            invite_id = str(uuid.uuid4())
            encoded_payload = self._generate_invite_token(
                invite_id=invite_id,
                email=request.email,
                org_id=request.organisation_id,
                department=department,
                permission=permission.upper() if permission else None,
                role=role.upper()
            )
            
            # Create new invite using ORM for simplicity
            invite = Invite(
                id=invite_id,
                email=request.email,
                organisation_id=request.organisation_id,
                department=department,
                role=role.upper(),
                permission=permission.upper() if permission else None,
                created_by=request.created_by,
                expires_at=expires_at,
                invite_token=encoded_payload
            )
            
            db_session.add(invite)
            db_session.flush()  # Get the short_token generated by default
            
            db_session.commit()

            short_invite_url = self.generate_short_invite_url(invite.short_token)
            
            email_data = {
                "email": request.email,
                "name": result[0]['admin_name'],
                "org_name": result[0]['org_name'],
                "role": role,
                "department": department,
                "link": short_invite_url
            }
            
            # Send email notification asynchronously
            self.helper.send_invite_email_async(email_data, SendEmailTypeEnum.INVITE_MEMBER.value, KafkaAction.SEND_TO_MEMBER.value)
            
            # Construct response with minimal data
            invite_model = organisation_pb2.InviteModel(
                id=invite_id,
                email=request.email,
                organisation_id=request.organisation_id,
                department=department,
                role=role,
                permission=permission,
                created_by=request.created_by,
                status=InviteStatus.PENDING.value,
                expires_at=expires_at.isoformat(),
                created_at=now.isoformat(),
                updated_at=now.isoformat()
            )
            
            return organisation_pb2.InviteResponse(
                invite=invite_model,
                message="Invite sent successfully",
                success=True
            )
            
        except Exception as e:
            db_session.rollback()
            logger.error("Error creating invite", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error creating invite: {str(e)}")
            return organisation_pb2.InviteResponse()
        
    def acceptInviteByLink(self, request, context):
        """
        Accept or reject an invitation using an invite link.
        Handles invite validation, user creation, and Neo4j relationship setup.
        """
        action = "accept" if hasattr(request, "accept") and request.accept else "reject"
        logger.info(f"Received request to {action} invite by link")
        
        try:
            # Validate and decode invite
            invite = self._validate_invite_request(request, context)
            if not invite:
                return organisation_pb2.InviteResponse()
            
            # Handle rejection
            if not getattr(request, "accept", True):
                return self._handle_invite_rejection(invite)

            # Handle acceptance
            return self._handle_invite_acceptance(request, invite, context)
            
        except Exception as e:
            return self._handle_error(e, context)
        
    def _decode_invite_token(self, encoded_link):
        """
        Decode an invite link to extract the information
        """
        try:
            # Check if this is a short token (typically 8-12 chars) or full encoded payload
            if len(encoded_link) <= 12 and not encoded_link.startswith('ey'):
                # This is likely a short token - fetch the invite from database
                invite = db_session.query(Invite).filter(
                    Invite.short_token == encoded_link,
                    Invite.status == InviteStatus.PENDING.value
                ).first()
                
                if not invite:
                    logger.error("Invalid or expired short token", token=encoded_link)
                    return None
                    
                # Return the payload directly constructed from the invite data
                return {
                    "invite_id": invite.id,
                    "email": invite.email,
                    "org_id": invite.organisation_id,
                    "department": invite.department,
                    "permission": invite.permission,
                    "role": invite.role
                }
            else:
                # This is the original long token format
                # Decode the base64 string
                decoded_bytes = base64.urlsafe_b64decode(encoded_link)
                decoded_json = decoded_bytes.decode()
                
                # Parse the JSON data
                payload = json.loads(decoded_json)
                
                return payload
                
        except Exception as e:
            logger.error("Error decoding invite link", error=str(e))
            return None

    def _validate_invite_request(self, request, context):
        """Validate invite token and retrieve invite from database."""
        # Decode the invite link
        payload = self._decode_invite_token(request.invite_token)
        if not payload:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details("Invalid invite link")
            return None
        
        # Extract and validate payload information
        invite_id = payload.get("invite_id")
        email = payload.get("email")
        
        if not all([invite_id, email]):
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details("Missing information in invite link")
            return None
        
        # Verify email match
        if email != request.current_user_email:
            logger.error("User email doesn't match invite",
                        provided_email=request.current_user_email, 
                        invite_email=email)
            context.set_code(grpc.StatusCode.PERMISSION_DENIED)
            context.set_details("The email address doesn't match the invited user")
            return None
        
        # Retrieve and validate invite
        invite = self._get_valid_invite(invite_id)
        if not invite:
            logger.error("Invite not found, expired, or already processed", id=invite_id)
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details("The invite link is invalid, expired, or already used")
            return None
        
        # Store payload in invite for later use
        invite._payload = payload
        return invite

    def _get_valid_invite(self, invite_id):
        """Retrieve valid invite from database."""
        now = datetime.utcnow()
        return db_session.query(Invite).filter(
            Invite.id == invite_id,
            Invite.status == InviteStatus.PENDING,
            (Invite.expires_at > now) | (Invite.expires_at.is_(None))
        ).first()

    def _handle_invite_rejection(self, invite):
        """Handle invite rejection."""
        now = datetime.utcnow()
        invite.status = InviteStatus.DECLINED
        invite.updated_at = now
        
        db_session.commit()
        
        return self._build_invite_response(invite, "Invite declined successfully")

    def _handle_invite_acceptance(self, request, invite, context):
        """Handle invite acceptance with Neo4j setup."""
        # Check if user is already part of any organisation
        if OrganisationMembershipChecker.isUserPartOfAnyOrganisation(
            request.user_id,
            execute_read_query,
            OrganisationQuery().CHECK_USER_IN_ANY_ORGANISATION
        ):
            logger.warning("User is already part of an organisation", user_id=request.user_id)
            context.set_code(grpc.StatusCode.PERMISSION_DENIED)
            context.set_details("User is already part of an organisation and cannot join another one")
            return organisation_pb2.InviteResponse(
                message="User is already part of an organisation and cannot join another one",
                success=False
            )
            
        now = datetime.utcnow()
        invite.status = InviteStatus.ACCEPTED
        invite.accepted_at = now
        invite.updated_at = now
        
        # Setup Neo4j relationships
        neo4j_success = self._setup_neo4j_relationships(request, invite, context)

        if not neo4j_success:
            # If Neo4j setup fails, mark invite as expired and return error
            logger.error("Error creating neo graph")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Issue with the graph db")
            return organisation_pb2.InviteResponse()
        
        db_session.commit()
        return self._build_invite_response(invite, "Invite accepted successfully")

    def _setup_neo4j_relationships(self, request, invite, context):
        """Setup user and department relationships in Neo4j with minimal round trips."""
        try:
            current_time = datetime.utcnow().isoformat()
            payload = invite._payload
            
            # First, quick validation that department exists (1 round trip)
            if not self._validate_department_exists(payload, context):
                return False
            # Single query to handle all user and relationship setup (1 round trip)
            self._setup_user_and_all_relationships(request, invite, payload, current_time)
            return True
            
        except Exception as e:
            logger.error(f"Error creating Neo4j relationships: {str(e)}")
            # Continue even if Neo4j fails - invite status will still be updated
            return False

    def _validate_department_exists(self, payload, context):
        """Quick validation that the department exists (1 round trip)."""
        org_id = payload.get("org_id")
        department = payload.get("department")
        
        dept_params = {
            "org_id": org_id,
            "department_name": department
        }
        
        dept_result = execute_read_query(
            self.queries.VALIDATE_DEPARTMENT_EXISTS, 
            dept_params
        )
        
        if not dept_result:
            logger.error(f"Department '{department}' not found in organisation {org_id}")
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(f"Department '{department}' not found in this organisation")
            return False
        
        return True

    def _setup_user_and_all_relationships(self, request, invite, payload, current_time):
        """Single query to create user and all relationships (1 round trip)."""
        setup_params = {
            "user_id": request.user_id,
            "user_name": request.user_name,
            "user_email": invite.email,
            "timestamp": current_time,
            "org_id": payload.get("org_id"),
            "department_name": payload.get("department"),
            "role": payload.get("role"),
            "permission": invite.permission,
            "creation_type": UserCreationType.SIGNED_IN.value,
            "inviter": invite.created_by,
            "general_dept_name": DefaultDepartments.GENERAL.value,
            "general_role": DefaultRoles.MEMBER.value,
            "general_permission": DefaultPermissions.READ.value
        }
        
        result = execute_write_query(
            self.queries.SETUP_USER_AND_RELATIONSHIPS,
            setup_params
        )
        
        if result:
            target_dept = result[0].get("target_department")
            general_added = result[0].get("general_added", 0)
            
            logger.info(f"Created user {request.user_id} and relationship to department {target_dept}")
            if general_added > 0:
                logger.info(f"Also added user {request.user_id} to General department")

    def _build_invite_response(self, invite, message):
        """Build standardized invite response."""
        invite_model = organisation_pb2.InviteModel(
            id=invite.id,
            email=invite.email,
            organisation_id=invite.organisation_id,
            department=invite.department or "",
            role=invite.role or "",
            permission=invite.permission or "",
            created_by=invite.created_by,
            status=invite.status,
            created_at=invite.created_at.isoformat(),
            updated_at=invite.updated_at.isoformat(),
        )
        
        return organisation_pb2.InviteResponse(
            invite=invite_model,
            message=message,
            success=True
        )

    def _handle_error(self, error, context):
        """Handle and log errors with proper cleanup."""
        db_session.rollback()
        logger.error("Error accepting invite by link", error=str(error))
        context.set_code(grpc.StatusCode.INTERNAL)
        context.set_details(f"Error accepting invite: {str(error)}")
        return organisation_pb2.InviteResponse()
    
    def get_pending_invites(self, user_email):
        """Helper method to fetch pending invites."""
        # Get user email first
        # email_result = execute_read_query(
        #     "MATCH (u:User {id: $user_id}) RETURN u.email as email",
        #     {"user_id": user_id}
        # )
        
        # if not email_result or not email_result[0].get('email'):
        #     return []
            
        # user_email = email_result[0]['email']
        now = datetime.utcnow()
        
        # Batch fetch all pending invites
        invites = db_session.query(Invite).filter(
            Invite.email == user_email,
            Invite.status == InviteStatus.PENDING.value,
            (Invite.expires_at > now) | (Invite.expires_at.is_(None))
        ).all()
        
        if not invites:
            return []
        
        # Batch fetch all org details in one query
        org_ids = [invite.organisation_id for invite in invites]
        org_results = execute_read_query(self.queries.FETCH_ORGS_DETAILS, {"org_ids": org_ids})
        org_map = {res['id']: res['o'] for res in org_results}
        
        # Batch fetch all inviter names in one query
        inviter_ids = {invite.created_by for invite in invites}
        inviter_results = execute_read_query(self.queries.FETCH_USERS_DETAILS, {"user_ids": list(inviter_ids)})
        inviter_map = {res['id']: res['name'] for res in inviter_results}
        
        # Build all pending invites
        return [
            organisation_pb2.PendingInvite(
                invite_id=invite.id,
                organisation=organisation_pb2.OrganisationModel(
                    id=org_map[invite.organisation_id].get('id', ''),
                    name=org_map[invite.organisation_id].get('name', ''),
                    website_url=org_map[invite.organisation_id].get('website_url', ''),
                    industry=org_map[invite.organisation_id].get('industry', ''),
                    created_by=org_map[invite.organisation_id].get('created_by', ''),
                    created_at=org_map[invite.organisation_id].get('created_at', ''),
                    updated_at=org_map[invite.organisation_id].get('updated_at', '')
                ),
                department=invite.department,
                role=invite.role,
                permission=invite.permission or "",
                inviter_id=invite.created_by,
                inviter_name=inviter_map.get(invite.created_by, "Unknown"),
                created_at=invite.created_at.isoformat(),
                expires_at=invite.expires_at.isoformat() if invite.expires_at else ""
            )
            for invite in invites
            if invite.organisation_id in org_map
        ]
        
    def get_inviter_invites(self, request, context):
        """
        Fetch all invites created by a specific user (inviter).
        Can return either accepted invites (from Neo4j) or pending invites (from PostgreSQL).
        
        Args:
            request: The gRPC request containing user_id and type parameter
            context: The gRPC context
            
        Returns:
            ListInviterInvitesResponse: A response containing the list of invites
        """
        logger.info("Fetching invites sent by user", user_id=request.user_id, type=request.type)
        
        try:
            if request.type.upper() == "ACCEPTED":
                return self._get_accepted_invites(request.user_id, request.organisation_id)
            elif request.type.upper() == "PENDING":
                return self._get_pending_invites_by_inviter(request.user_id, request.organisation_id)
            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Invalid invite type: {request.type}. Must be 'ACCEPTED' or 'PENDING'")
                return organisation_pb2.ListInviterInvitesResponse()
        except Exception as e:
            logger.error("Error fetching invites", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching invites: {str(e)}")
            return organisation_pb2.ListInviterInvitesResponse()
    
    def _get_accepted_invites(self, user_id, org_id):
        """Get accepted invites from Neo4j where the user is the inviter."""
        # Query Neo4j for users with relationships where invited_by = user_id
        result = execute_read_query(self.queries.FETCH_ACCEPTED_INVITES_BY_INVITER, {"org_id": org_id, "dept_name": DefaultDepartments.GENERAL.value})
        
        if not result:
            return organisation_pb2.ListInviterInvitesResponse(
                invites=[],
                message="No accepted invites found",
                success=True
            )
            
        # Process the results
        invites = []
        for record in result:
            invites.append(organisation_pb2.InviterInvite(
                invitee_id=record['invitee_id'],
                invitee_name=record['invitee_name'],
                invitee_email=record['invitee_email'],
                organisation_id=record['org_id'],
                organisation_name=record['org_name'],
                department=record['department_name'],
                role=record['role'],
                permission=record['permission'] or "",
                status="ACCEPTED",
                joined_at=record['joined_at']
            ))
            
        return organisation_pb2.ListInviterInvitesResponse(
            invites=invites,
            message=f"Found {len(invites)} accepted invites",
            success=True
        )
    
    def _get_pending_invites_by_inviter(self, user_id, org_id):
        """Get pending invites from PostgreSQL where the user is the inviter."""
        now = datetime.utcnow()
        
        # Query PostgreSQL for invites where created_by = user_id and status = PENDING
        invites = db_session.query(Invite).filter(
            # Invite.created_by == user_id,
            Invite.organisation_id == org_id,
            Invite.status == InviteStatus.PENDING.value,
            (Invite.expires_at > now) | (Invite.expires_at.is_(None))
        ).all()
        
        if not invites:
            return organisation_pb2.ListInviterInvitesResponse(
                invites=[],
                message="No pending invites found",
                success=True
            )
            
        # Batch fetch all org details in one query
        org_ids = list({invite.organisation_id for invite in invites})
        org_results = execute_read_query(self.queries.FETCH_ORGS_DETAILS, {"org_ids": org_ids})
        org_map = {res['id']: res['o'] for res in org_results}
        
        # Build all pending invites
        processed_invites = []
        for invite in invites:
            if invite.organisation_id not in org_map:
                continue
                
            processed_invites.append(organisation_pb2.InviterInvite(
                invitee_id="",  # No user ID for pending invites
                invitee_name="",  # No name for pending invites
                invitee_email=invite.email,
                organisation_id=invite.organisation_id,
                organisation_name=org_map[invite.organisation_id].get('name', ''),
                department=invite.department,
                role=invite.role,
                permission=invite.permission or "",
                status="PENDING",
                created_at=invite.created_at.isoformat(),
                expires_at=invite.expires_at.isoformat() if invite.expires_at else ""
            ))
            
        return organisation_pb2.ListInviterInvitesResponse(
            invites=processed_invites,
            message=f"Found {len(processed_invites)} pending invites",
            success=True
        )