# Agent Schema Definition
version: 1.0
description: "Schema definition for Agent entities and relationships"

nodes:
  Agent:
    description: "Represents an AI agent in the system"
    properties:
      id:
        type: string
        required: true
        description: "Unique agent identifier"
      name:
        type: string
        required: true
        description: "Agent name"
      description:
        type: string
        description: "Agent description"
      department_id:
        type: string
        description: "Department the agent belongs to"
      owner_id:
        type: string
        required: true
        description: "User ID of the agent owner"
      owner_name:
        type: string
        description: "Name of the agent owner"
      user_ids:
        type: list
        description: "List of user IDs with access to the agent"
      created_at:
        type: timestamp
        description: "Agent creation timestamp"
      updated_at:
        type: timestamp
        description: "Agent last update timestamp"
      visibility:
        type: string
        description: "Agent visibility (PRIVATE/PUBLIC)"
      status:
        type: string
        description: "Agent status (ACTIVE/BENCH/INACTIVE)"
      creator_role:
        type: string
        description: "Role of the creator (MEMBER/CREATOR/VIEWER)"

relationships:
  OWNS:
    from: User
    to: Agent
    description: "User owns an agent"
    direction: "->"
    properties:
      created_at:
        type: timestamp
        description: "When ownership was established"

  BELONGS_TO:
    from: Agent
    to: Department
    description: "Agent belongs to a department"
    direction: "->"
    properties:
      assigned_at:
        type: timestamp
        description: "When agent was assigned to department"

  HAS_ACCESS:
    from: User
    to: Agent
    description: "User has access to an agent"
    direction: "->"
    properties:
      access_type:
        type: string
        description: "Type of access (read/write/admin)"
      granted_at:
        type: timestamp
        description: "When access was granted"
      granted_by:
        type: string
        description: "User ID who granted access"