"""
Connectors Module

This module provides a factory pattern architecture for managing multiple data source connectors.
It includes base interfaces, utilities, and specific connector implementations.

Key Components:
- BaseConnector: Abstract interface for all connectors
- ConnectorFactory: Factory pattern for dynamic connector loading
- Utilities: Shared schemas, entities, and relationships
- Handlers: Specific connector implementations (e.g., Google Drive)
"""

from .base import BaseConnector
from .connector_factory import ConnectorFactory, get_connector_factory

__all__ = [
    'BaseConnector',
    'ConnectorFactory', 
    'get_connector_factory'
]