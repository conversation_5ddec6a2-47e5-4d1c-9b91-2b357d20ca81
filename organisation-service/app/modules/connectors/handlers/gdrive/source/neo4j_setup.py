import structlog
from app.services.neo4j_service import execute_write_query

logger = structlog.get_logger()

def create_source_schema():
    """
    Create Neo4j constraints and indexes for Source entities.
    """
    constraints = [
        """
        CREATE CONSTRAINT IF NOT EXISTS FOR (s:Source) 
        REQUIRE s.id IS UNIQUE
        """
    ]
    
    indexes = [
        """
        CREATE INDEX IF NOT EXISTS FOR (s:Source)
        ON (s.type)
        """
    ]
    
    for query in constraints:
        execute_write_query(query)
    
    for query in indexes:
        execute_write_query(query)
    
    logger.info("Source Neo4j schema created")