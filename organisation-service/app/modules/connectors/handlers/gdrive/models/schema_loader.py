import os
import structlog
from app.modules.connectors.utilities.schema_loader import ConnectorSchema, ConnectorSchemaError

logger = structlog.get_logger()

# Pre-initialized schemas for Google Drive connector
try:
    schema_path = os.path.join(os.path.dirname(__file__), "google_drive_schema.yml")
    google_drive_schema = ConnectorSchema("google_drive", schema_path)
except ConnectorSchemaError as e:
    logger.warning(f"Could not load Google Drive schema: {e}")
    google_drive_schema = None