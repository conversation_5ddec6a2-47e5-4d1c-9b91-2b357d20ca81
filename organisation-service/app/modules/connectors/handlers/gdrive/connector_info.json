{"source_type": "gdrive", "name": "Google Drive", "connector_type": "unstructured", "category": "Cloud Storage", "icon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNEY4NUY0Ii8+Cjwvc3ZnPgo=", "description": "Comprehensive Google Drive connector that provides seamless integration with Google Drive for file and folder synchronization, content processing, and intelligent search capabilities. Supports real-time sync, permission management, and advanced content extraction.", "purpose": "This connector enables organizations to integrate their Google Drive data into the knowledge graph, providing unified search across documents, automatic content processing, and intelligent relationship mapping between files, folders, and users.", "nodes": ["GoogleDriveFile", "GoogleDriveFolder", "GoogleDriveDocument", "GoogleDriveSpreadsheet", "GoogleDrivePresentatio", "GoogleDriveImage", "GoogleDriveVideo", "GoogleDriveTextChunk", "GoogleDriveSection", "GoogleDriveUser", "GoogleDrivePermission", "GoogleDriveVersion", "GoogleDriveComment", "GoogleDriveRevision"], "relationships": ["CONTAINS", "BELONGS_TO", "HAS_ACCESS", "CAN_READ", "CAN_WRITE", "CAN_DELETE", "CAN_SHARE", "OWNS", "SHARED_WITH", "PART_OF", "EXTRACTED_FROM", "REFERENCES", "LINKS_TO", "VERSION_OF", "CREATED_BY", "MODIFIED_BY", "COMMENTED_ON", "REVIEWED_BY", "COLLABORATED_ON", "SYNCED_BY", "PROCESSED_BY", "INDEXED_BY", "SIMILAR_TO"], "example_usage": "Perfect for organizations that store documents, presentations, and collaborative content in Google Drive. Enables unified search across all Google Drive content, automatic content extraction from documents, intelligent file organization, and comprehensive permission tracking. Ideal for knowledge management, content discovery, and collaborative workflows.", "example_queries": ["Find all documents related to project planning", "Show me presentations created by <PERSON> in the last month", "What files are shared with the marketing team?", "Find documents similar to this quarterly report", "Show all spreadsheets in the Finance folder", "What files have been modified recently?", "Find documents that mention budget or financial planning", "Show me all files I have edit access to"], "supported_features": ["Real-time file and folder synchronization", "Intelligent content extraction and processing", "Permission-based access control", "Semantic search across document content", "Automatic relationship mapping", "Version tracking and history", "Collaborative workflow support", "Incremental sync for performance", "Batch processing capabilities", "Advanced filtering and querying", "Multi-format document support", "Embedding generation for semantic search"], "api_endpoints": {"main_api": "https://www.googleapis.com/drive/v3", "documentation": "https://developers.google.com/drive/api/v3/reference"}, "authentication": {"type": "service_account", "scopes_required": ["https://www.googleapis.com/auth/drive.readonly", "https://www.googleapis.com/auth/drive"], "documentation": "https://developers.google.com/drive/api/v3/about-auth"}, "rate_limits": {"requests_per_hour": "1000", "burst_limit": "100 per minute", "quota_management": "Automatic retry with exponential backoff"}, "data_freshness": {"real_time": "Supports real-time updates through periodic sync and change detection", "batch_sync": "Configurable full synchronization intervals (daily, weekly, or on-demand)", "incremental": "Efficient delta updates based on file modification timestamps and content hashes"}, "compliance": {"data_privacy": "Respects Google Drive privacy settings and organizational access controls. Only processes files accessible to the configured service account.", "security": "Uses secure OAuth 2.0 service account authentication with encrypted credential storage", "audit": "Maintains comprehensive audit logs of all data access, modifications, and sync operations"}, "supported_file_types": ["Google Docs (documents)", "Google Sheets (spreadsheets)", "Google Slides (presentations)", "PDF documents", "Microsoft Office files (Word, Excel, PowerPoint)", "Text files", "Images (JPEG, PNG, GIF, etc.)", "Videos (MP4, AVI, MOV, etc.)", "Audio files", "HTML files", "CSV files"], "configuration_options": {"sync_frequency": "Configurable sync intervals", "file_filters": "MIME type and size-based filtering", "content_processing": "Toggleable text extraction and embedding generation", "permission_sync": "Optional permission and sharing information sync", "folder_scope": "Configurable folder-level sync scope"}}