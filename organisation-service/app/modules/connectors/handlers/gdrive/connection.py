"""
Google Drive Connection Management

This module handles connection establishment and management for Google Drive API.
"""

import logging
from typing import Dict, Any, Optional
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.utils.source_credentials import get_service_account_credentials
from app.utils.google_service_account import GoogleServiceAccountManager

logger = logging.getLogger(__name__)


class GoogleDriveConnection:
    """
    Manages connection to Google Drive API.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize connection with configuration.
        
        Args:
            config: Connection configuration dictionary
        """
        self.config = config
        self.service = None
        self._authenticated = False
        
        # Extract connection parameters
        self.organisation_id = config.get('organisation_id')
        self.scopes = config.get('scopes', [
            'https://www.googleapis.com/auth/drive.readonly',
            'https://www.googleapis.com/auth/drive'
        ])
        
        # Initialize service account manager
        self.service_account_manager = GoogleServiceAccountManager()
    
    def connect(self) -> bool:
        """
        Establish connection and authenticate with Google Drive API.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to Google Drive API for organisation {self.organisation_id}")
            
            if not self.organisation_id:
                logger.error("Organisation ID is required for Google Drive connection")
                return False
            
            # Get service account credentials
            credentials = get_service_account_credentials(self.organisation_id)
            if not credentials:
                logger.error(f"No service account credentials found for organisation {self.organisation_id}")
                return False
            
            # Build the Google Drive service
            self.service = build('drive', 'v3', credentials=credentials)
            
            # Test connection with a simple API call
            self._test_connection()
            
            self._authenticated = True
            logger.info("Successfully connected to Google Drive API")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Google Drive API: {str(e)}")
            self._authenticated = False
            return False
    
    def disconnect(self):
        """Clean up connection resources."""
        try:
            if self.service:
                # Google API client doesn't require explicit cleanup
                # but we can clear the reference
                self.service = None
            
            self._authenticated = False
            logger.info("Disconnected from Google Drive API")
            
        except Exception as e:
            logger.warning(f"Error during Google Drive disconnect: {str(e)}")
    
    def is_connected(self) -> bool:
        """
        Check if connection is active and authenticated.
        
        Returns:
            bool: True if connected and authenticated
        """
        return self._authenticated and self.service is not None
    
    def get_service(self):
        """
        Get the Google Drive service instance.
        
        Returns:
            Google Drive service instance
            
        Raises:
            ConnectionError: If not connected
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to Google Drive API. Call connect() first.")
        
        return self.service
    
    def _test_connection(self):
        """Test the connection by making a simple API call."""
        try:
            # Test with a simple about() call to verify credentials
            about = self.service.about().get(fields="user").execute()
            logger.debug(f"Connection test successful. Service account: {about.get('user', {}).get('emailAddress', 'Unknown')}")
            
        except HttpError as e:
            if e.resp.status == 401:
                raise ConnectionError("Authentication failed - invalid credentials")
            elif e.resp.status == 403:
                raise ConnectionError("Access denied - insufficient permissions")
            else:
                raise ConnectionError(f"API test failed: {str(e)}")
        except Exception as e:
            raise ConnectionError(f"Connection test failed: {str(e)}")
    
    def validate_permissions(self) -> Dict[str, bool]:
        """
        Validate that the service account has required permissions.
        
        Returns:
            Dict[str, bool]: Dictionary of permission checks
        """
        permissions = {
            'can_list_files': False,
            'can_read_files': False,
            'can_access_metadata': False
        }
        
        if not self.is_connected():
            return permissions
        
        try:
            # Test listing files (basic read permission)
            self.service.files().list(pageSize=1, fields="files(id,name)").execute()
            permissions['can_list_files'] = True
            permissions['can_read_files'] = True
            
            # Test getting file metadata
            self.service.about().get(fields="user").execute()
            permissions['can_access_metadata'] = True
            
        except HttpError as e:
            logger.warning(f"Permission validation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error validating permissions: {str(e)}")
        
        return permissions
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get information about the current connection.
        
        Returns:
            Dict[str, Any]: Connection information
        """
        info = {
            'connected': self.is_connected(),
            'organisation_id': self.organisation_id,
            'scopes': self.scopes,
            'service_available': self.service is not None
        }
        
        if self.is_connected():
            try:
                about = self.service.about().get(fields="user,storageQuota").execute()
                info.update({
                    'service_account_email': about.get('user', {}).get('emailAddress'),
                    'storage_quota': about.get('storageQuota', {}),
                    'permissions': self.validate_permissions()
                })
            except Exception as e:
                logger.warning(f"Could not get connection details: {str(e)}")
        
        return info
    
    def refresh_connection(self) -> bool:
        """
        Refresh the connection (useful for long-running processes).
        
        Returns:
            bool: True if refresh successful, False otherwise
        """
        try:
            if self.is_connected():
                # Test current connection
                self._test_connection()
                return True
            else:
                # Reconnect if not connected
                return self.connect()
                
        except Exception as e:
            logger.warning(f"Connection refresh failed, attempting reconnect: {str(e)}")
            return self.connect()