from app.modules.connectors.handlers.gdrive.models.schema_loader import google_drive_schema
from app.modules.organisation.models.schema_loader import schema as organisation_schema
from app.modules.agents.models.schema_loader import agent_schema
import uuid
from datetime import datetime

class GoogleDriveUserQueries:
    """Queries for managing users based on Google Drive permissions."""
    
    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.org_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.file_label = google_drive_schema.get_node_labels()[0]  # GoogleDriveFile
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.belongsTo = organisation_schema.get_relationship_types()[2] #BELONGS_TO
        
    @property
    def CREATE_OR_FIND_USER_BY_EMAIL(self):
        return f"""
        // First, try to find existing user by email
        MERGE (u:{self.user_label} {{email: $email}})
        ON CREATE SET
            u.id = $user_id,
            u.organisation_id = $organisation_id,
            u.name = $name,
            u.creation_type = 'auto_created',
            u.created_at = $created_at,
            u.updated_at = $updated_at
        ON MATCH SET
            u.updated_at = $updated_at
        WITH u
        // Ensure user is connected to the organisation
        MATCH (org:{self.org_label} {{id: $organisation_id}})
        WITH u, org
        RETURN u
        """
    
    @property
    def CREATE_USER_FOLDER_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label} {{email: $email}})
        MATCH (f:{self.folder_label} {{id: $folder_id, organisation_id: $organisation_id}})
        MERGE (u)-[r:{self.has_access_rel}]->(f)
        SET r.role = $role,
            r.granted_at = $granted_at,
            r.permission_type = 'google_drive'
        RETURN r
        """
    
    @property
    def CREATE_USER_FILE_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label} {{email: $email}})
        MATCH (f:{self.file_label} {{id: $file_id, organisation_id: $organisation_id}})
        MERGE (u)-[r:{self.has_access_rel}]->(f)
        SET r.role = $role,
            r.granted_at = $granted_at,
            r.permission_type = 'google_drive'
        RETURN r
        """


class GoogleDriveFileQueries:
    """Queries for Google Drive file management."""
    
    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.file_label = google_drive_schema.get_node_labels()[0]  # GoogleDriveFile
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.contains_rel = google_drive_schema.get_relationship_types()[1]  # CONTAINS

    @property
    def CREATE_OR_UPDATE_FILE(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MERGE (f:{self.file_label} {{id: $file_id}})
        SET f.organisation_id = $organisation_id,
            f.name = $name,
            f.mime_type = $mime_type,
            f.size = $size,
            f.web_view_link = $web_view_link,
            f.created_time = $created_time,
            f.modified_time = $modified_time,
            f.vector_id = $vector_id,
            f.content_hash = $content_hash,
            f.permissions = $permissions
        MERGE (u)-[:{self.has_access_rel}]->(f)
        RETURN f
        """

    @property
    def CREATE_OR_UPDATE_FILE_WITH_PERMISSIONS(self):
        return f"""
        // Create or update the file
        MERGE (f:{self.file_label} {{id: $file_id}})
        SET f.organisation_id = $organisation_id,
            f.name = $name,
            f.mime_type = $mime_type,
            f.size = $size,
            f.web_view_link = $web_view_link,
            f.created_time = $created_time,
            f.modified_time = $modified_time,
            f.vector_id = $vector_id,
            f.content_hash = $content_hash,
            f.permissions = $permissions,
            f.shared_with = $shared_with
        
        // Create users from email permissions and establish relationships
        WITH f
        UNWIND $permission_emails as email
        MERGE (u:{self.user_label} {{email: email}})
        ON CREATE SET u.id = randomUUID(),
                     u.organisation_id = $organisation_id,
                     u.created_at = datetime(),
                     u.creation_type = 'auto_created',
                     u.updated_at = datetime()
        ON MATCH SET u.updated_at = datetime()
        MERGE (u)-[:{self.has_access_rel}]->(f)
        
        RETURN f, collect(u) as users_with_access
        """

    @property
    def CREATE_FILE_FOLDER_RELATIONSHIP(self):
        return f"""
        MATCH (folder:{self.folder_label} {{id: $folder_id}})
        MATCH (file:{self.file_label} {{id: $file_id}})
        MERGE (folder)-[:{self.contains_rel}]->(file)
        """

    @property
    def CREATE_FOLDER_HIERARCHY_RELATIONSHIP(self):
        return f"""
        MATCH (parent:{self.folder_label} {{id: $parent_id}})
        MATCH (child:{self.folder_label} {{id: $child_id}})
        WHERE parent.organisation_id = $organisation_id AND child.organisation_id = $organisation_id
        MERGE (parent)-[:{self.contains_rel}]->(child)
        """

    @property
    def LIST_USER_FILES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f:{self.file_label})
        OPTIONAL MATCH (folder:{self.folder_label})-[:{self.contains_rel}]->(f)
        WHERE $folder_id IS NULL OR folder.id = $folder_id
        RETURN f, folder
        ORDER BY f.name
        SKIP $skip
        LIMIT $limit
        """

    @property
    def COUNT_USER_FILES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f:{self.file_label})
        OPTIONAL MATCH (folder:{self.folder_label})-[:{self.contains_rel}]->(f)
        WHERE $folder_id IS NULL OR folder.id = $folder_id
        RETURN count(f) as total_count
        """

    @property
    def GET_FILE_DETAILS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f)
        WHERE (f:{self.file_label} OR f:{self.folder_label}) AND f.id = $file_id
        RETURN f,
               CASE WHEN f:{self.folder_label} THEN true ELSE false END as is_folder,
               f.id as id,
               f.name as name,
               f.mime_type as mime_type,
               f.web_view_link as web_view_link,
               f.created_at as created_time,
               f.modified_time as modified_time,
               f.size as size,
               f.shared_with as shared_with,
               CASE WHEN f:{self.folder_label} THEN
                 SIZE((f)-[:{self.contains_rel}]->())
               ELSE
                 0
               END as child_count
        """

    @property
    def GET_FILE_WITH_FOLDERS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f:{self.file_label} {{id: $file_id}})
        OPTIONAL MATCH (folder:{self.folder_label})-[:{self.contains_rel}]->(f)
        RETURN f, collect(folder) as folders
        """

    @property
    def CHECK_FILE_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f:{self.file_label} {{id: $file_id}})
        RETURN f IS NOT NULL as has_access
        """

    @property
    def DELETE_USER_FILES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[r:{self.has_access_rel}]->(f:{self.file_label})
        DETACH DELETE f
        """

    @property
    def DELETE_ORGANIZATION_FILES(self):
        return f"""
        MATCH (f:{self.file_label} {{organisation_id: $organisation_id}})
        DETACH DELETE f
        """

    @property
    def UPDATE_FILE_VECTOR_ID(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        SET f.vector_id = $vector_id
        RETURN f
        """


class GoogleDriveFolderQueries:
    """Queries for Google Drive folder management."""
    
    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.contains_rel = google_drive_schema.get_relationship_types()[1]  # CONTAINS

    @property
    def CREATE_OR_UPDATE_FOLDER(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MERGE (folder:{self.folder_label} {{id: $folder_id}})
        SET folder.organisation_id = $organisation_id,
            folder.name = $name,
            folder.created_time = $created_time,
            folder.modified_time = $modified_time,
            folder.permissions = $permissions
        MERGE (u)-[:{self.has_access_rel}]->(folder)
        RETURN folder
        """
    
    @property
    def CREATE_OR_UPDATE_FOLDER_WITH_PERMISSIONS(self):
        return f"""
        // Create or update the folder
        MERGE (f:{self.folder_label} {{id: $folder_id}})
        SET f.organisation_id = $organisation_id,
            f.name = $name,
            f.created_time = $created_time,
            f.modified_time = $modified_time,
            f.permissions = $permissions,
            f.owners = $owners,
            f.permissions_count = $permissions_count,
            f.is_top_level = $is_top_level,
            f.updated_at = $updated_at
        
        // Create users from email permissions and establish relationships
        WITH f
        UNWIND $permission_emails as email
        MERGE (u:{self.user_label} {{email: email}})
        ON CREATE SET u.id = randomUUID(),
                     u.organisation_id = $organisation_id,
                     u.created_at = datetime(),
                     u.creation_type = 'auto_created',
                     u.updated_at = datetime()
        ON MATCH SET u.updated_at = datetime()
        MERGE (u)-[:{self.has_access_rel}]->(f)
        
        RETURN f, collect(u) as users_with_access
        """

    @property
    def CREATE_FOLDER_RELATIONSHIPS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(folder:{self.folder_label})
        WHERE folder.id IN $folder_ids
        WITH folder
        UNWIND $parent_data as parent_info
        WITH folder, parent_info
        WHERE folder.id = parent_info.folder_id
        MATCH (parent:{self.folder_label} {{id: parent_info.parent_id}})
        MERGE (parent)-[:{self.contains_rel}]->(folder)
        """

    @property
    def GET_TOP_LEVEL_FOLDERS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(folder:{self.folder_label})
        WHERE NOT EXISTS((:{self.folder_label})-[:{self.contains_rel}]->(folder))
        RETURN folder
        ORDER BY folder.name
        """

    @property
    def GET_FOLDER_CONTENTS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(folder:{self.folder_label} {{id: $folder_id}})
        OPTIONAL MATCH (folder)-[:{self.contains_rel}]->(item)
        WHERE (u)-[:{self.has_access_rel}]->(item)
        RETURN folder, collect(item) as contents
        """

    @property
    def FIND_FOLDER_BY_NAME(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(folder:{self.folder_label})
        WHERE toLower(folder.name) = toLower($folder_name)
        RETURN folder
        """

    @property
    def DELETE_USER_FOLDERS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[r:{self.has_access_rel}]->(folder:{self.folder_label})
        DETACH DELETE folder
        """

    @property
    def DELETE_ORGANIZATION_FOLDERS(self):
        return f"""
        MATCH (folder:{self.folder_label} {{organisation_id: $organisation_id}})
        DETACH DELETE folder
        """


class GoogleDriveListQueries:
    """Queries for listing and searching Google Drive items."""
    
    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.file_label = google_drive_schema.get_node_labels()[0]  # GoogleDriveFile
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.contains_rel = google_drive_schema.get_relationship_types()[1]  # CONTAINS

    @property
    def LIST_FILES_IN_FOLDER(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f)
        WHERE (f:{self.file_label} OR f:{self.folder_label})
        MATCH (folder:{self.folder_label} {{id: $folder_id}})-[:{self.contains_rel}]->(f)
        RETURN f,
               CASE WHEN f:{self.folder_label} THEN true ELSE false END as is_folder,
               f.id as id,
               f.name as name,
               f.mime_type as mime_type,
               f.web_view_link as web_view_link,
               f.created_at as created_time,
               f.modified_time as modified_time,
               f.size as size,
               f.shared_with as shared_with,
               CASE WHEN f:{self.folder_label} THEN
                 SIZE([x IN [(f)-[:{self.contains_rel}]->(c) | c] WHERE x IS NOT NULL])
               ELSE
                 0
               END as child_count
        ORDER BY is_folder DESC, f.name
        SKIP $skip
        LIMIT $limit
        """

    @property
    def LIST_ROOT_FILES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f)
        WHERE (f:{self.file_label} OR f:{self.folder_label})
        AND (
            f.parent_ids IS NULL OR
            NOT (f)<-[:{self.contains_rel}]-(:{self.folder_label})<-[:{self.has_access_rel}]-(u)
        )
        RETURN f,
               CASE WHEN f:{self.folder_label} THEN true ELSE false END as is_folder,
               f.id as id,
               f.name as name,
               f.mime_type as mime_type,
               f.web_view_link as web_view_link,
               f.created_at as created_time,
               f.modified_time as modified_time,
               f.size as size,
               f.shared_with as shared_with,
               CASE WHEN f:{self.folder_label} THEN
                 SIZE([x IN [(f)-[:{self.contains_rel}]->(c) | c] WHERE x IS NOT NULL])
               ELSE
                 0
               END as child_count
        ORDER BY is_folder DESC, f.name
        SKIP $skip
        LIMIT $limit
        """

    @property
    def COUNT_FILES_IN_FOLDER(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f)
        WHERE (f:{self.file_label} OR f:{self.folder_label})
        MATCH (folder:{self.folder_label} {{id: $folder_id}})-[:{self.contains_rel}]->(f)
        RETURN COUNT(f) as total
        """

    @property
    def COUNT_ROOT_FILES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(f)
        WHERE (f:{self.file_label} OR f:{self.folder_label})
        AND (
            f.parent_ids IS NULL OR
            NOT (f)<-[:{self.contains_rel}]-(:{self.folder_label})<-[:{self.has_access_rel}]-(u)
        )
        RETURN COUNT(f) as total
        """

    @property
    def GET_FOLDER_CONTENTS_BY_NAME(self):
        return f"""
        MATCH (folder:{self.folder_label} {{id: $folder_id}})-[:{self.contains_rel}]->(child)
        RETURN child,
               CASE WHEN child:{self.folder_label} THEN true ELSE false END as is_folder,
               child.id as id,
               child.name as name,
               child.mime_type as mime_type,
               child.web_view_link as web_view_link,
               child.created_at as created_time,
               child.modified_time as modified_time,
               child.size as size,
               child.shared_with as shared_with,
               CASE WHEN child:{self.folder_label} THEN
                 SIZE([x IN [(child)-[:{self.contains_rel}]->(c) | c] WHERE x IS NOT NULL])
               ELSE
                 0
               END as child_count
        ORDER BY is_folder DESC, child.name
        """


class GoogleDriveSyncQueries:
    """Queries for Google Drive sync operations."""
    
    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.file_label = google_drive_schema.get_node_labels()[0]  # GoogleDriveFile
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.contains_rel = google_drive_schema.get_relationship_types()[1]  # CONTAINS

    @property
    def BATCH_CREATE_FILES(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        UNWIND $files as file_data
        MERGE (f:{self.file_label} {{id: file_data.id}})
        SET f += file_data.properties
        SET f.organisation_id = COALESCE(file_data.properties.organisation_id, u.organisation_id)
        MERGE (u)-[:{self.has_access_rel}]->(f)
        """

    @property
    def BATCH_CREATE_FOLDERS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        UNWIND $folders as folder_data
        MERGE (folder:{self.folder_label} {{id: folder_data.id}})
        SET folder += folder_data.properties
        SET folder.organisation_id = COALESCE(folder_data.properties.organisation_id, u.organisation_id)
        MERGE (u)-[:{self.has_access_rel}]->(folder)
        """

    @property
    def CREATE_FOLDER_HIERARCHY_RELATIONSHIPS(self):
        return f"""
        MATCH (child:{self.folder_label})
        WHERE child.parent_ids IS NOT NULL
        UNWIND child.parent_ids AS parent_id
        MATCH (parent:{self.folder_label} {{id: parent_id}})
        WHERE parent.organisation_id = child.organisation_id
        MERGE (parent)-[r:{self.contains_rel}]->(child)
        """

    @property
    def CREATE_FOLDER_HIERARCHY_RELATIONSHIP(self):
        return f"""
        MATCH (parent:{self.folder_label} {{id: $parent_id}})
        MATCH (child:{self.folder_label} {{id: $child_id}})
        WHERE parent.organisation_id = $organisation_id AND child.organisation_id = $organisation_id
        MERGE (parent)-[:{self.contains_rel}]->(child)
        """

    @property
    def CREATE_FILE_FOLDER_RELATIONSHIPS(self):
        return f"""
        MATCH (f:{self.file_label})
        WHERE f.parent_ids IS NOT NULL AND f.organisation_id = $organisation_id
        UNWIND f.parent_ids AS parent_id
        OPTIONAL MATCH (folder:{self.folder_label} {{id: parent_id}})
        WHERE folder.organisation_id = f.organisation_id
        WITH f, parent_id, folder
        WHERE folder IS NOT NULL
        MERGE (folder)-[r:{self.contains_rel}]->(f)
        RETURN count(r) as relationships_created
        """

    @property
    def CREATE_FILE_FOLDER_RELATIONSHIP(self):
        return f"""
        MATCH (folder:{self.folder_label} {{id: $folder_id}})
        MATCH (file:{self.file_label} {{id: $file_id}})
        WHERE folder.organisation_id = $organisation_id AND file.organisation_id = $organisation_id
        MERGE (folder)-[:{self.contains_rel}]->(file)
        """

    @property
    def BATCH_CREATE_FOLDER_RELATIONSHIPS(self):
        return f"""
        UNWIND $relationships as rel_data
        MATCH (parent:{self.folder_label} {{id: rel_data.parent_id}})
        MATCH (child:{self.folder_label} {{id: rel_data.child_id}})
        WHERE parent.organisation_id = child.organisation_id
        MERGE (parent)-[:{self.contains_rel}]->(child)
        """

    @property
    def BATCH_CREATE_FILE_FOLDER_RELATIONSHIPS(self):
        return f"""
        UNWIND $relationships as rel_data
        MATCH (folder:{self.folder_label} {{id: rel_data.folder_id}})
        MATCH (file:{self.file_label} {{id: rel_data.file_id}})
        WHERE folder.organisation_id = file.organisation_id
        MERGE (folder)-[:{self.contains_rel}]->(file)
        """

    @property
    def GET_MODIFIED_ITEMS_SINCE(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(item)
        WHERE (item:{self.file_label} OR item:{self.folder_label})
        AND item.modified_time > $since_time
        AND item.organisation_id = u.organisation_id
        RETURN item
        """

    @property
    def CLEANUP_DELETED_ITEMS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(item)
        WHERE (item:{self.file_label} OR item:{self.folder_label})
        AND NOT item.id IN $existing_ids
        AND item.organisation_id = u.organisation_id
        DETACH DELETE item
        """

    @property
    def GET_ALL_USER_DRIVE_ITEMS(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.has_access_rel}]->(item)
        WHERE (item:{self.file_label} OR item:{self.folder_label})
        AND item.organisation_id = u.organisation_id
        RETURN item.id as id, labels(item) as labels
        """

    @property
    def CREATE_SHARED_ACCESS_RELATIONSHIPS(self):
        return f"""
        MATCH (f)
        WHERE (f:{self.file_label} OR f:{self.folder_label})
        AND f.shared_with IS NOT NULL
        AND f.organisation_id = $organisation_id
        UNWIND f.shared_with AS email
        MERGE (u:{self.user_label} {{email: email, organisation_id: $organisation_id}})
        ON CREATE SET
            u.id = randomUUID(),
            u.created_at = $current_time,
            u.creation_type = 'auto_created',
        ON MATCH SET
            u.creation_type = CASE WHEN u.creation_type IS NULL THEN 'signed_in' ELSE u.creation_type END

        WITH f, u, $current_time as current_time
        WHERE NOT EXISTS {{
           MATCH (u)-[:{self.has_access_rel}]->(parent:{self.folder_label})-[:{self.contains_rel}]->(f)
           WHERE parent.organisation_id = f.organisation_id
        }}
        MERGE (u)-[r:{self.has_access_rel}]->(f)
        ON CREATE SET r.added_at = current_time, r.access_type = 'shared'
        """

    @property
    def DELETE_INHERITED_ACCESS_RELATIONSHIPS(self):
        return f"""
        MATCH (u:{self.user_label})-[:{self.has_access_rel}]->(f:{self.folder_label})
        MATCH (f)-[:{self.contains_rel}*]->(containedNode)
        WHERE (containedNode:{self.file_label} OR containedNode:{self.folder_label})
        AND f.organisation_id = containedNode.organisation_id
        MATCH (u)-[r:{self.has_access_rel}]->(containedNode)
        DELETE r
        """

    @property
    def GET_ALL_USERS_WITH_ACCESS(self):
        return f"""
        MATCH (u:{self.user_label})-[:{self.has_access_rel}]->()
        RETURN DISTINCT u.id as user_id
        """

    @property
    def FIND_USER_BY_EMAIL(self):
        return f"""
        MATCH (u:{self.user_label} {{email: $email}})
        RETURN u.id as user_id
        """


class GoogleDriveRelationshipQueries:
    """Queries for managing relationships between entities."""
    
    def __init__(self):
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.agent_label = agent_schema.get_node_labels()[0]  # Agent
        self.organisation_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.department_label = organisation_schema.get_node_labels()[2]  # Department
        self.file_label = google_drive_schema.get_node_labels()[0]  # GoogleDriveFile
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.belongs_to_rel = organisation_schema.get_relationship_types()[2]  # BELONGS_TO
        self.has_department_rel = organisation_schema.get_relationship_types()[1]  # HAS_DEPARTMENT

    @property
    def CREATE_AGENT_FILE_RELATIONSHIP(self):
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        MATCH (f:{self.file_label} {{id: $file_id}})
        WHERE a.organisation_id = f.organisation_id
        MERGE (a)-[:{self.has_access_rel}]->(f)
        """

    @property
    def CREATE_USER_FILE_RELATIONSHIP(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MATCH (f:{self.file_label} {{id: $file_id}})
        WHERE u.organisation_id = f.organisation_id
        MERGE (u)-[:{self.has_access_rel}]->(f)
        """

    @property
    def CREATE_DEPARTMENT_FILE_RELATIONSHIP(self):
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})-[:{self.belongs_to_rel}]->(d:{self.department_label})
        MATCH (f:{self.file_label} {{id: $file_id}})
        WHERE d.organisation_id = f.organisation_id
        MERGE (d)-[:{self.has_access_rel}]->(f)
        RETURN d.id as department_id
        """

    @property
    def CREATE_ORGANISATION_FOLDER_RELATIONSHIP(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (f:{self.folder_label} {{id: $folder_id}})
        MERGE (o)-[:{self.has_access_rel}]->(f)
        """

    @property
    def GET_USER_ORGANISATION(self):
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.belongs_to_rel}]->({self.department_label})<-[:{self.has_department_rel}]-(o:{self.organisation_label})
        RETURN o.id as organisation_id LIMIT 1
        """

    @property
    def GET_AGENT_DEPARTMENT(self):
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})-[:{self.belongs_to_rel}]->(d:{self.department_label})
        RETURN d.id as department_id LIMIT 1
        """


class GoogleDriveMetadataQueries:
    """Queries for managing file and folder metadata."""
    
    def __init__(self):
        self.file_label = google_drive_schema.get_node_labels()[0]  # GoogleDriveFile
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder

    @property
    def UPDATE_FOLDER_ADDITIONAL_PROPERTIES(self):
        return f"""
        MATCH (f:{self.folder_label} {{id: $folder_id}})
        SET f.parent_ids = $parent_ids,
            f.shared_with = $shared_with,
            f.source = 'google_drive',
            f.created_in_neo4j = $current_time,
            f.updated_at = $current_time,
            f.organisation_id = $organisation_id
        """

    @property
    def UPDATE_FILE_ADDITIONAL_PROPERTIES(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        SET f.parent_ids = $parent_ids,
            f.shared_with = $shared_with,
            f.source = 'google_drive',
            f.created_in_neo4j = $current_time,
            f.updated_at = $current_time,
            f.organisation_id = $organisation_id
        """

    @property
    def CHECK_FILE_VECTORIZATION_STATUS(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        RETURN f.vector_id, f.vectorized_at, f.last_vectorized_modified_time
        """

    @property
    def UPDATE_FILE_VECTORIZATION_METADATA(self):
        return f"""
        MATCH (f:{self.file_label} {{id: $file_id}})
        SET f.vectorized_at = $vectorized_at,
            f.last_vectorized_modified_time = $modified_time
        """


class GoogleDriveServiceAccountQueries:
    """Queries for service account operations."""
    
    def __init__(self):
        self.organisation_label = organisation_schema.get_node_labels()[0]  # Organisation
        self.department_label = organisation_schema.get_node_labels()[2]  # Department
        self.user_label = organisation_schema.get_node_labels()[1]  # User
        self.folder_label = google_drive_schema.get_node_labels()[1]  # GoogleDriveFolder
        self.has_access_rel = google_drive_schema.get_relationship_types()[0]  # HAS_ACCESS
        self.has_department_rel = organisation_schema.get_relationship_types()[1]  # HAS_DEPARTMENT
        self.belongs_to_rel = organisation_schema.get_relationship_types()[2]  # BELONGS_TO

    @property
    def CREATE_DEPARTMENT_FOLDER_ACCESS(self):
        return f"""
        MATCH (d:{self.department_label} {{organisation_id: $organisation_id}})
        MATCH (f:{self.folder_label} {{organisation_id: $organisation_id, is_top_level: true}})
        WHERE d.name = 'GENERAL'
        MERGE (d)-[:{self.has_access_rel}]->(f)
        """

    @property
    def MAP_USER_FOLDER_ACCESS(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (o)-[:{self.has_department_rel}]->(d:{self.department_label})
        MATCH (d)<-[:{self.belongs_to_rel}]-(u:{self.user_label})
        MATCH (o)-[:{self.has_access_rel}]->(f:{self.folder_label} {{is_top_level: true}})
        MERGE (u)-[:{self.has_access_rel}]->(f)
        """

    @property
    def MAP_GENERAL_DEPARTMENT_ACCESS(self):
        return f"""
        MATCH (o:{self.organisation_label} {{id: $organisation_id}})
        MATCH (o)-[:{self.has_department_rel}]->(d:{self.department_label} {{name: 'GENERAL'}})
        MATCH (o)-[:{self.has_access_rel}]->(f:{self.folder_label} {{is_top_level: true}})
        MERGE (d)-[:{self.has_access_rel}]->(f)
        """