"""
Google Drive Connector Entity Type Definitions

This module defines all entity types for the Google Drive connector.
"""

from enum import Enum


class EntityType(Enum):
    """
    Google Drive entity types for knowledge graph nodes.
    
    Define all possible node types that can be created in the knowledge graph
    for Google Drive data.
    """
    
    # Main Google Drive entities
    GOOGLE_DRIVE_FILE = "GoogleDriveFile"
    GOOGLE_DRIVE_FOLDER = "GoogleDriveFolder"
    
    # User and permission entities
    GOOGLE_DRIVE_USER = "GoogleDriveUser"
    GOOGLE_DRIVE_PERMISSION = "GoogleDrivePermission"
    
    # Content entities (for processed files)
    GOOGLE_DRIVE_DOCUMENT = "GoogleDriveDocument"
    GOOGLE_DRIVE_SPREADSHEET = "GoogleDriveSpreadsheet"
    GOOGLE_DRIVE_PRESENTATION = "GoogleDrivePresentation"
    GOOGLE_DRIVE_IMAGE = "GoogleDriveImage"
    GOOGLE_DRIVE_VIDEO = "GoogleDriveVideo"
    
    # Extracted content entities
    GOOGLE_DRIVE_TEXT_CHUNK = "GoogleDriveTextChunk"
    GOOGLE_DRIVE_SECTION = "GoogleDriveSection"
    
    # Metadata entities
    GOOGLE_DRIVE_VERSION = "GoogleDriveVersion"
    GOOGLE_DRIVE_COMMENT = "GoogleDriveComment"
    GOOGLE_DRIVE_REVISION = "GoogleDriveRevision"


def get_all_entity_types():
    """
    Returns all Google Drive entity types for connector registration.
    
    Returns:
        set: Set of all entity type string values
    """
    return {e.value for e in EntityType}


def get_file_entity_types():
    """
    Returns file-related entity types.
    
    Returns:
        set: Set of file entity type string values
    """
    return {
        EntityType.GOOGLE_DRIVE_FILE.value,
        EntityType.GOOGLE_DRIVE_DOCUMENT.value,
        EntityType.GOOGLE_DRIVE_SPREADSHEET.value,
        EntityType.GOOGLE_DRIVE_PRESENTATION.value,
        EntityType.GOOGLE_DRIVE_IMAGE.value,
        EntityType.GOOGLE_DRIVE_VIDEO.value
    }


def get_content_entity_types():
    """
    Returns content-related entity types.
    
    Returns:
        set: Set of content entity type string values
    """
    return {
        EntityType.GOOGLE_DRIVE_TEXT_CHUNK.value,
        EntityType.GOOGLE_DRIVE_SECTION.value
    }
    return {
        EntityType.GOOGLE_DRIVE_TEXT_CHUNK.value,
        EntityType.GOOGLE_DRIVE_SECTION.value
    }


def get_metadata_entity_types():
    """
    Returns metadata-related entity types.
    
    Returns:
        set: Set of metadata entity type string values
    """
    return {
        EntityType.GOOGLE_DRIVE_VERSION.value,
        EntityType.GOOGLE_DRIVE_COMMENT.value,
        EntityType.GOOGLE_DRIVE_REVISION.value
    }


def is_file_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents a file.
    
    Args:
        entity_type: The entity type string
        
    Returns:
        bool: True if it's a file entity, False otherwise
    """
    return entity_type in get_file_entity_types()


def is_folder_entity(entity_type: str) -> bool:
    """
    Check if an entity type represents a folder.
    
    Args:
        entity_type: The entity type string
        
    Returns:
        bool: True if it's a folder entity, False otherwise
    """
    return entity_type == EntityType.GOOGLE_DRIVE_FOLDER.value


def get_entity_type_from_mime_type(mime_type: str) -> str:
    """
    Get the appropriate entity type based on MIME type.
    
    Args:
        mime_type: The MIME type of the file
        
    Returns:
        str: The corresponding entity type
    """
    mime_type_mapping = {
        'application/vnd.google-apps.folder': EntityType.GOOGLE_DRIVE_FOLDER.value,
        'application/vnd.google-apps.document': EntityType.GOOGLE_DRIVE_DOCUMENT.value,
        'application/vnd.google-apps.spreadsheet': EntityType.GOOGLE_DRIVE_SPREADSHEET.value,
        'application/vnd.google-apps.presentation': EntityType.GOOGLE_DRIVE_PRESENTATION.value,
        'image/': EntityType.GOOGLE_DRIVE_IMAGE.value,
        'video/': EntityType.GOOGLE_DRIVE_VIDEO.value,
    }
    
    # Check for exact matches first
    if mime_type in mime_type_mapping:
        return mime_type_mapping[mime_type]
    
    # Check for partial matches (e.g., image/jpeg -> GoogleDriveImage)
    for mime_prefix, entity_type in mime_type_mapping.items():
        if mime_type.startswith(mime_prefix):
            return entity_type
    
    # Default to generic file
    return EntityType.GOOGLE_DRIVE_FILE.value


def get_entity_type_from_mime(mime_type: str) -> str:
    """
    Alias for get_entity_type_from_mime_type for backward compatibility.
    
    Args:
        mime_type: The MIME type of the file
        
    Returns:
        str: The corresponding entity type
    """
    return get_entity_type_from_mime_type(mime_type)


def is_processable_file(mime_type: str) -> bool:
    """
    Check if a file type is processable for content extraction.
    
    Args:
        mime_type: The MIME type of the file
        
    Returns:
        bool: True if the file can be processed for content extraction
    """
    processable_types = {
        'application/vnd.google-apps.document',
        'application/vnd.google-apps.spreadsheet',
        'application/vnd.google-apps.presentation',
        'application/pdf',
        'text/plain',
        'text/html',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/msword',
        'application/vnd.ms-excel',
        'application/vnd.ms-powerpoint'
    }
    
    return mime_type in processable_types