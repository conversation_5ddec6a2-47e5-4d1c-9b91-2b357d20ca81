"""
Common Schemas for Connector System

This module defines standardized schemas used across all connectors
for consistent data exchange and response formats.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class SearchStatus(str, Enum):
    """Status of search operation."""
    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"
    TIMEOUT = "timeout"


class SearchError(BaseModel):
    """Error information for failed searches."""
    error_code: str = Field(..., description="Error code identifier")
    error_message: str = Field(..., description="Human-readable error message")
    error_type: str = Field(..., description="Type of error (connection, timeout, etc.)")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class SearchMetrics(BaseModel):
    """Metrics for search performance."""
    execution_time_ms: float = Field(..., description="Search execution time in milliseconds")
    total_results_found: int = Field(..., description="Total number of results found")
    results_returned: int = Field(..., description="Number of results returned")
    sources_searched: Optional[int] = Field(None, description="Number of sources searched")
    cache_hit: Optional[bool] = Field(None, description="Whether results came from cache")


class SearchResultItem(BaseModel):
    """Individual search result item."""
    id: str = Field(..., description="Unique identifier for the result")
    title: str = Field(..., description="Title or name of the result")
    content: Optional[str] = Field(None, description="Content or description")
    source_type: str = Field(..., description="Type of source (gdrive, github, etc.)")
    entity_type: Optional[str] = Field(None, description="Type of entity")
    url: Optional[str] = Field(None, description="URL to access the item")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    relevance_score: Optional[float] = Field(None, description="Relevance score (0-1)")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    access_level: Optional[str] = Field(None, description="Access level for the user")
    tags: Optional[List[str]] = Field(None, description="Associated tags")


class ConnectorInfo(BaseModel):
    """Basic connector information."""
    source_type: str = Field(..., description="Connector source type identifier")
    name: str = Field(..., description="Human-readable connector name")
    version: str = Field(..., description="Connector version")
    connector_type: Optional[str] = Field(None, description="structured or unstructured")


class ConnectorSearchResponse(BaseModel):
    """Standardized response format for connector searches."""
    status: SearchStatus = Field(..., description="Status of the search operation")
    query: str = Field(..., description="Original search query")
    results: List[SearchResultItem] = Field(default_factory=list, description="Search results")
    total_count: int = Field(default=0, description="Total number of results")
    page: Optional[int] = Field(None, description="Current page number")
    page_size: Optional[int] = Field(None, description="Number of results per page")
    has_more: Optional[bool] = Field(None, description="Whether more results are available")
    connector_info: ConnectorInfo = Field(..., description="Information about the connector")
    metrics: Optional[SearchMetrics] = Field(None, description="Search performance metrics")
    error: Optional[SearchError] = Field(None, description="Error information if search failed")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    filters_applied: Optional[Dict[str, Any]] = Field(None, description="Filters that were applied")
    suggestions: Optional[List[str]] = Field(None, description="Search suggestions")


class ConnectorConfig(BaseModel):
    """Base configuration schema for connectors."""
    source_type: str = Field(..., description="Connector type identifier")
    enabled: bool = Field(default=True, description="Whether connector is enabled")
    timeout_seconds: int = Field(default=30, description="Request timeout in seconds")
    rate_limit_per_hour: Optional[int] = Field(None, description="Rate limit per hour")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    retry_delay_seconds: float = Field(default=1.0, description="Delay between retries")
    cache_ttl_seconds: Optional[int] = Field(None, description="Cache TTL in seconds")
    batch_size: int = Field(default=100, description="Batch size for data processing")


class EntityMetadata(BaseModel):
    """Metadata for entities in the knowledge graph."""
    entity_id: str = Field(..., description="Unique entity identifier")
    entity_type: str = Field(..., description="Type of entity")
    source_type: str = Field(..., description="Source connector type")
    organisation_id: str = Field(..., description="Organisation ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Entity properties")
    relationships: Optional[List[Dict[str, Any]]] = Field(None, description="Entity relationships")


class RelationshipMetadata(BaseModel):
    """Metadata for relationships in the knowledge graph."""
    relationship_id: str = Field(..., description="Unique relationship identifier")
    relationship_type: str = Field(..., description="Type of relationship")
    source_entity_id: str = Field(..., description="Source entity ID")
    target_entity_id: str = Field(..., description="Target entity ID")
    source_type: str = Field(..., description="Source connector type")
    organisation_id: str = Field(..., description="Organisation ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    properties: Dict[str, Any] = Field(default_factory=dict, description="Relationship properties")


class SyncStatus(str, Enum):
    """Status of sync operations."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SyncResult(BaseModel):
    """Result of a sync operation."""
    sync_id: str = Field(..., description="Unique sync operation ID")
    connector_type: str = Field(..., description="Connector type")
    organisation_id: str = Field(..., description="Organisation ID")
    status: SyncStatus = Field(..., description="Sync status")
    started_at: datetime = Field(..., description="Sync start time")
    completed_at: Optional[datetime] = Field(None, description="Sync completion time")
    entities_processed: int = Field(default=0, description="Number of entities processed")
    entities_created: int = Field(default=0, description="Number of entities created")
    entities_updated: int = Field(default=0, description="Number of entities updated")
    entities_deleted: int = Field(default=0, description="Number of entities deleted")
    relationships_processed: int = Field(default=0, description="Number of relationships processed")
    error_message: Optional[str] = Field(None, description="Error message if sync failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional sync metadata")


class ConnectorCapability(str, Enum):
    """Capabilities that connectors can support."""
    SEARCH = "search"
    SYNC = "sync"
    INCREMENTAL_SYNC = "incremental_sync"
    REAL_TIME = "real_time"
    WEBHOOKS = "webhooks"
    BATCH_PROCESSING = "batch_processing"
    FILE_PROCESSING = "file_processing"
    ENTITY_EXTRACTION = "entity_extraction"
    RELATIONSHIP_MAPPING = "relationship_mapping"
    SEMANTIC_SEARCH = "semantic_search"
    FULL_TEXT_SEARCH = "full_text_search"


class ConnectorRegistration(BaseModel):
    """Schema for registering a new connector."""
    source_type: str = Field(..., description="Unique connector identifier")
    name: str = Field(..., description="Human-readable connector name")
    connector_type: str = Field(..., description="structured or unstructured")
    category: str = Field(..., description="Connector category")
    description: str = Field(..., description="Connector description")
    version: str = Field(..., description="Connector version")
    capabilities: List[ConnectorCapability] = Field(..., description="Supported capabilities")
    entity_types: List[str] = Field(..., description="Supported entity types")
    relationship_types: List[str] = Field(..., description="Supported relationship types")
    configuration_schema: Dict[str, Any] = Field(..., description="Configuration schema")
    is_active: bool = Field(default=True, description="Whether connector is active")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class QueryFilter(BaseModel):
    """Filter for search queries."""
    field: str = Field(..., description="Field to filter on")
    operator: str = Field(..., description="Filter operator (eq, ne, gt, lt, in, etc.)")
    value: Union[str, int, float, bool, List[Any]] = Field(..., description="Filter value")


class SearchQuery(BaseModel):
    """Structured search query."""
    query: str = Field(..., description="Search query string")
    filters: Optional[List[QueryFilter]] = Field(None, description="Search filters")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("desc", description="Sort order (asc/desc)")
    page: int = Field(default=1, description="Page number")
    page_size: int = Field(default=10, description="Results per page")
    include_metadata: bool = Field(default=True, description="Include metadata in results")
    source_types: Optional[List[str]] = Field(None, description="Limit to specific source types")
    entity_types: Optional[List[str]] = Field(None, description="Limit to specific entity types")


class ConnectorMetadata(BaseModel):
    """Metadata for connector instances."""
    connector_id: str = Field(..., description="Unique connector instance ID")
    source_type: str = Field(..., description="Connector type identifier")
    organisation_id: str = Field(..., description="Organisation ID")
    name: str = Field(..., description="Human-readable connector name")
    description: Optional[str] = Field(None, description="Connector description")
    status: str = Field(..., description="Connector status (active, inactive, error)")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    last_sync_at: Optional[datetime] = Field(None, description="Last sync timestamp")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Connector configuration")
    capabilities: List[ConnectorCapability] = Field(default_factory=list, description="Supported capabilities")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Connector metrics")
    error_info: Optional[Dict[str, Any]] = Field(None, description="Error information if status is error")