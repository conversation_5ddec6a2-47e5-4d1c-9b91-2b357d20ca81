"""
Standard Entity Types for Unstructured Connectors

This module defines common entity types used by unstructured connectors
like Google Drive, SharePoint, file systems, etc.
"""

from enum import Enum


class StandardEntityType(Enum):
    """
    Standard entity types for unstructured data connectors.
    
    These are common entity types that can be used across different
    unstructured data sources like file systems, document repositories, etc.
    """
    
    # Document entities
    DOCUMENT = "Document"
    FILE = "File"
    FOLDER = "Folder"
    DIRECTORY = "Directory"
    
    # Content entities
    TEXT_CHUNK = "TextChunk"
    PARAGRAPH = "Paragraph"
    SECTION = "Section"
    CHAPTER = "Chapter"
    
    # Media entities
    IMAGE = "Image"
    VIDEO = "Video"
    AUDIO = "Audio"
    
    # Structured content within documents
    TABLE = "Table"
    CHART = "Chart"
    DIAGRAM = "Diagram"
    
    # Metadata entities
    AUTHOR = "Author"
    CREATOR = "Creator"
    EDITOR = "Editor"
    REVIEWER = "Reviewer"
    
    # Organizational entities
    TAG = "Tag"
    CATEGORY = "Category"
    COLLECTION = "Collection"
    WORKSPACE = "Workspace"
    
    # Version and history
    VERSION = "Version"
    REVISION = "Revision"
    SNAPSHOT = "Snapshot"
    
    # Access and permissions
    PERMISSION = "Permission"
    ACCESS_CONTROL = "AccessControl"
    SHARE_LINK = "ShareLink"
    
    # Extracted knowledge entities
    CONCEPT = "Concept"
    TOPIC = "Topic"
    KEYWORD = "Keyword"
    ENTITY_MENTION = "EntityMention"
    
    # Temporal entities
    EVENT = "Event"
    TIMELINE = "Timeline"
    MILESTONE = "Milestone"


def get_all_standard_entity_types():
    """
    Returns all standard entity types for unstructured connectors.
    
    Returns:
        set: Set of all standard entity type string values
    """
    return {e.value for e in StandardEntityType}


def get_document_entity_types():
    """
    Returns document-related entity types.
    
    Returns:
        set: Set of document entity type string values
    """
    return {
        StandardEntityType.DOCUMENT.value,
        StandardEntityType.FILE.value,
        StandardEntityType.FOLDER.value,
        StandardEntityType.DIRECTORY.value
    }


def get_content_entity_types():
    """
    Returns content-related entity types.
    
    Returns:
        set: Set of content entity type string values
    """
    return {
        StandardEntityType.TEXT_CHUNK.value,
        StandardEntityType.PARAGRAPH.value,
        StandardEntityType.SECTION.value,
        StandardEntityType.CHAPTER.value
    }


def get_media_entity_types():
    """
    Returns media-related entity types.
    
    Returns:
        set: Set of media entity type string values
    """
    return {
        StandardEntityType.IMAGE.value,
        StandardEntityType.VIDEO.value,
        StandardEntityType.AUDIO.value
    }


def get_metadata_entity_types():
    """
    Returns metadata-related entity types.
    
    Returns:
        set: Set of metadata entity type string values
    """
    return {
        StandardEntityType.AUTHOR.value,
        StandardEntityType.CREATOR.value,
        StandardEntityType.EDITOR.value,
        StandardEntityType.REVIEWER.value
    }


def get_knowledge_entity_types():
    """
    Returns knowledge extraction entity types.
    
    Returns:
        set: Set of knowledge entity type string values
    """
    return {
        StandardEntityType.CONCEPT.value,
        StandardEntityType.TOPIC.value,
        StandardEntityType.KEYWORD.value,
        StandardEntityType.ENTITY_MENTION.value
    }


# Entity type categories for easier management
ENTITY_CATEGORIES = {
    "document": get_document_entity_types(),
    "content": get_content_entity_types(),
    "media": get_media_entity_types(),
    "metadata": get_metadata_entity_types(),
    "knowledge": get_knowledge_entity_types()
}


def get_entity_category(entity_type: str) -> str:
    """
    Get the category of an entity type.
    
    Args:
        entity_type: The entity type string
        
    Returns:
        str: The category name, or "unknown" if not found
    """
    for category, types in ENTITY_CATEGORIES.items():
        if entity_type in types:
            return category
    return "unknown"


def is_valid_entity_type(entity_type: str) -> bool:
    """
    Check if an entity type is valid.
    
    Args:
        entity_type: The entity type string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    return entity_type in get_all_standard_entity_types()