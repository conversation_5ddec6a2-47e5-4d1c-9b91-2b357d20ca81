"""
Connector Utilities

This module provides shared utilities for all connectors including:
- Common schemas and data models
- Standard entity types and relationships
- Configuration management
- Error handling utilities
"""

from .constant.schemas import (
    ConnectorSearchResponse,
    SearchResultItem,
    SearchStatus,
    SearchMetrics,
    ConnectorConfig,
    ConnectorMetadata
)
from .constant.entities import StandardEntityType, get_entity_category
from .constant.relationships import StandardRelationshipType, get_relationship_category

__all__ = [
    'ConnectorSearchResponse',
    'SearchResultItem', 
    'SearchStatus',
    'SearchMetrics',
    'ConnectorConfig',
    'ConnectorMetadata',
    'StandardEntityType',
    'get_entity_category',
    'StandardRelationshipType',
    'get_relationship_category'
]