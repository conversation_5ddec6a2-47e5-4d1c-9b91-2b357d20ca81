"""
Task Status Service

This service provides a simplified interface for managing and tracking the
status of Celery tasks. It is designed to be used by gRPC services or
other background processes that need to report task state without being
tightly coupled to the underlying database implementation.

Key Features:
- Create a new task execution record.
- Update the status and progress of an existing task.
- Mark a task as failed with an error message.
- Abstract the database session management for easy integration.
"""

from contextlib import contextmanager

from sqlalchemy.orm import Session

from app.db.postgres import get_db
from app.modules.tasks.models.task_status import (
    ProcessingStageEnum,
    TaskExecutionStatus,
    TaskStatusEnum,
)


@contextmanager
def get_task_status_service():
    """
    Provides a transactional database session for the task status service.
    """
    db = next(get_db())
    try:
        yield TaskStatusService(db)
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()


@contextmanager
def get_task_status_service_with_rollback_recovery():
    """
    Provides a database session for the task status service with rollback recovery.
    This is useful when you need to handle errors and still perform database operations.
    """
    db = next(get_db())
    service = TaskStatusService(db)
    try:
        yield service
        db.commit()
    except Exception as e:
        db.rollback()
        # Create a new session for error handling operations
        db.close()
        db = next(get_db())
        service.db = db
        yield service  # Allow error handling operations
        db.commit()
        raise e
    finally:
        db.close()


class TaskStatusService:
    """
    Manages task execution status in the database.
    """

    def __init__(self, db: Session):
        self.db = db

    def create_task(
        self,
        celery_task_id: str,
        task_name: str,
        service_type: str,
        stage: ProcessingStageEnum = ProcessingStageEnum.INITIALIZATION,
        organisation_id: str = None,
        user_id: str = None,
    ) -> TaskExecutionStatus:
        """
        Creates a new task execution record in a PENDING state.

        Args:
            celery_task_id: The unique ID of the Celery task.
            task_name: A descriptive name for the task.
            service_type: The type of service (e.g., 'GDRIVE', 'JIRA').
            stage: The current processing stage (e.g., 'INITIALIZATION', 'PROCESSING').
            organisation_id: The ID of the organization, if applicable.
            user_id: The ID of the user who initiated the task, if applicable.

        Returns:
            The newly created TaskExecution object.
        """
        # Create task with stages
        task = TaskExecutionStatus(
            celery_task_id=celery_task_id,
            task_name=task_name,
            service_type=service_type,
            stages=stage,
            status=TaskStatusEnum.PENDING,
            organisation_id=organisation_id,
            user_id=user_id,
        )
        self.db.add(task)
        self.db.flush()  # Flush to get the ID without committing
        self.db.refresh(task)
        return task

    def update_task_status(
        self,
        celery_task_id: str,
        status: TaskStatusEnum,
        progress: int = None,
        stage: ProcessingStageEnum = None,
    ):
        """
        Updates the status, progress, and processing stage of a task.

        Args:
            celery_task_id: The ID of the Celery task.
            status: The new status (e.g., IN_PROGRESS, SUCCESS).
            progress: The current progress percentage (0-100).
            stage: The current processing stage.
        """
        task = (
            self.db.query(TaskExecutionStatus)
            .filter(TaskExecutionStatus.celery_task_id == celery_task_id)
            .first()
        )
        if task:
            task.status = status.value
            if progress is not None:
                task.progress = progress
            if stage is not None:
                task.stages = stage
            # Commit will be handled by the context manager

    def mark_as_failed(
        self, celery_task_id: str, error_message: str, stage: ProcessingStageEnum = None
    ):
        """
        Marks a task as FAILED and records the error message and the stage where the error occurred.

        Args:
            celery_task_id: The ID of the Celery task.
            error_message: The reason for the failure.
            stage: The processing stage where the error occurred.
        """
        task = (
            self.db.query(TaskExecutionStatus)
            .filter(TaskExecutionStatus.celery_task_id == celery_task_id)
            .first()
        )
        if task:
            task.status = TaskStatusEnum.FAILED
            task.error_message = error_message
            if stage is not None:
                task.stages = stage
            # Commit will be handled by the context manager
