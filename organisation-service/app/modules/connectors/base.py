"""
Base Connector Interface

This module defines the abstract base class that all connectors must inherit from
to ensure consistent interface and functionality across different data sources.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Iterator, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class BaseConnector(ABC):
    """
    Abstract base class for all connector implementations.
    
    This class defines the standard interface that all connectors must implement
    to ensure consistent behavior across different data sources.
    """
    
    # Must be set by subclasses
    CONNECTOR_TYPE: str = None  # "structured" or "unstructured"
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the connector with configuration.
        
        Args:
            config: Configuration dictionary for the connector
        """
        self.config = config or {}
        self.connection = None
        self._connected = False
        
        # Connector metadata - should be set by subclasses
        self.source_type = None
        self.connector_name = None
        self.version = "1.0.0"
    
    @abstractmethod
    def connect(self) -> Any:
        """
        Establish connection to the data source.
        
        Returns:
            Connection object or client instance
            
        Raises:
            ConnectionError: If connection fails
        """
        pass
    
    @abstractmethod
    def get_connector(self) -> dict:
        """
        Returns metadata about the connector.
        
        Returns:
            dict: Connector metadata including:
                - source_type: Unique identifier for the connector
                - name: Human-readable name
                - version: Connector version
                - connector_type: "structured" or "unstructured"
                - description: Connector description
                - supported_entities: List of entity types
                - supported_relationships: List of relationship types
                - capabilities: List of supported operations
                - connected: Connection status
        """
        pass
    
    @abstractmethod
    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        """
        Pulls all data from the source with pagination as an iterator.
        
        Yields:
            Dict[str, Any]: Data entity from the source
            
        Raises:
            ConnectionError: If not connected to data source
            Exception: For other fetch errors
        """
        pass
    
    @abstractmethod
    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        """
        Fetch a single entity by its ID.
        
        Args:
            id: Entity identifier (format may vary by connector)
            
        Returns:
            Dict[str, Any]: Entity data
            
        Raises:
            ValueError: If ID format is invalid
            NotFoundError: If entity doesn't exist
            ConnectionError: If not connected to data source
        """
        pass
    
    @abstractmethod
    def sync(self):
        """
        Perform a full sync of the data source.
        
        This method should:
        1. Fetch all data from the source
        2. Store context and embeddings
        3. Update knowledge graph
        
        Raises:
            ConnectionError: If not connected to data source
            Exception: For sync errors
        """
        pass
    
    @abstractmethod
    def sync_by_id(self, id: str):
        """
        Perform a partial sync for a single entity.
        
        Args:
            id: Entity identifier to sync
            
        Raises:
            ValueError: If ID format is invalid
            ConnectionError: If not connected to data source
        """
        pass
    
    @abstractmethod
    def store_context(self, data: Any):
        """
        Stores both context and embedding for given data.
        
        This method should:
        1. Transform data to standardized format
        2. Extract entities and relationships
        3. Store in knowledge graph (Neo4j)
        4. Generate and store embeddings (Pinecone)
        
        Args:
            data: Data to store (format varies by connector)
            
        Raises:
            Exception: For storage errors
        """
        pass
    
    @abstractmethod
    def search(self, query: str) -> 'ConnectorSearchResponse':
        """
        Search for data within the source.
        
        Args:
            query: Search query string
            
        Returns:
            ConnectorSearchResponse: Standardized search response
            
        Raises:
            ConnectionError: If not connected to data source
            Exception: For search errors
        """
        pass
    
    def disconnect(self):
        """
        Clean up connection resources.
        
        Default implementation - can be overridden by subclasses.
        """
        if hasattr(self, 'connection') and self.connection:
            try:
                if hasattr(self.connection, 'disconnect'):
                    self.connection.disconnect()
                elif hasattr(self.connection, 'close'):
                    self.connection.close()
            except Exception as e:
                logger.warning(f"Error during disconnect: {str(e)}")
            finally:
                self.connection = None
                self._connected = False
    
    def is_connected(self) -> bool:
        """
        Check if connector is connected to data source.
        
        Returns:
            bool: True if connected, False otherwise
        """
        return self._connected and self.connection is not None
    
    def validate_config(self) -> bool:
        """
        Validate connector configuration.
        
        Default implementation - should be overridden by subclasses
        that need specific validation.
        
        Returns:
            bool: True if configuration is valid
        """
        return True
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get connector health status.
        
        Returns:
            dict: Health status information
        """
        return {
            "connector_type": self.source_type,
            "connected": self.is_connected(),
            "last_check": datetime.now().isoformat(),
            "status": "healthy" if self.is_connected() else "disconnected"
        }