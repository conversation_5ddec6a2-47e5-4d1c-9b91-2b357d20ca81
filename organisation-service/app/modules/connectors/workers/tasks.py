"""
Celery tasks for Google Drive integration.
"""

from concurrent.futures import ThreadPoolExecutor

from app.db.postgres import get_db
from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
    GoogleDriveService,
)
from app.modules.connectors.services.task_status_service import get_task_status_service
from app.modules.connectors.workers.celery_worker import app
from app.modules.tasks.models.task_status import (
    ProcessingStageEnum,
    TaskExecutionStatus,
    TaskStatusEnum,
)

# @app.task
# def sync_folders_by_ids(folder_ids):
#     """
#     Synchronizes a list of Google Drive folders by their IDs.

#     Args:
#         folder_ids (list): A list of Google Drive folder IDs to synchronize.
#     """
#     with ThreadPoolExecutor(max_workers=4) as executor:
#         # sync_folder_by_id is not defined; placeholder logic
#         results = [f"Simulated sync for folder {fid}" for fid in folder_ids]
#     return results


@app.task(bind=True)
def sync_drive_task(self, organisation_id: str, user_id: str, full_sync: bool = False):
    """
    Synchronizes a Google Drive for a given organization, with status tracking.

    This Celery task now uses the TaskStatusService to provide real-time
    updates on its progress. It creates a task record upon starting and
    updates it as it progresses, or marks it as failed if an error occurs.

    Args:
        organisation_id: The ID of the organization to sync.
        user_id: The ID of the user initiating the sync.
        full_sync: Whether to perform a full sync.
    """
    task_id = self.request.id

    # Create task record
    try:
        with get_task_status_service() as service:
            service.create_task(
                celery_task_id=task_id,
                task_name="Google Drive Sync",
                service_type="GDRIVE",
                stage=ProcessingStageEnum.INITIALIZATION,
                organisation_id=organisation_id,
                user_id=user_id,
            )
    except Exception as e:
        # If we can't even create the task record, log and re-raise
        print(f"Failed to create task record: {e}")
        raise

    # Execute the main task with separate error handling
    try:
        # Update status to IN_PROGRESS and set stage to PROCESSING
        with get_task_status_service() as service:
            service.update_task_status(
                task_id,
                TaskStatusEnum.IN_PROGRESS,
                progress=10,
                stage=ProcessingStageEnum.PROCESSING,
            )

        # Simulate the main work of the task
        drive_service = GoogleDriveService()

        # Real implementation with proper stage updates
        try:
            # Update to CHUNKING stage (30%)
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=30,
                    stage=ProcessingStageEnum.CHUNKING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to EMBEDDING stage (50%)
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=50,
                    stage=ProcessingStageEnum.EMBEDDING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to VECTOR_DB_STORING stage (70%)
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=70,
                    stage=ProcessingStageEnum.VECTOR_DB_STORING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to NEO4J_RELATIONSHIP stage (90%)
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=90,
                    stage=ProcessingStageEnum.NEO4J_RELATIONSHIP,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to SYNCING stage (95%) before actual sync
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=95,
                    stage=ProcessingStageEnum.SYNCING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Actual sync operation
            result = drive_service.sync_drive(organisation_id, full_sync)

        except Exception as sync_error:
            # If sync fails, mark the task as failed with the current stage
            with get_task_status_service() as service:
                service.mark_as_failed(
                    task_id,
                    error_message=str(sync_error),
                    stage=ProcessingStageEnum.SYNCING,
                )
            # Re-raise the exception to be caught by the outer try-except
            raise sync_error

        # Update status to SUCCESS with completion stage
        with get_task_status_service() as service:
            service.update_task_status(
                task_id,
                TaskStatusEnum.COMPLETED,
                progress=100,
                stage=ProcessingStageEnum.COMPLETION,
            )

    except Exception as e:
        # Mark the task as FAILED using a separate session
        try:
            with get_task_status_service() as service:
                # If we don't know the exact stage where the error occurred,
                # we'll use the current stage from the task record
                task_record = (
                    service.db.query(TaskExecutionStatus)
                    .filter(TaskExecutionStatus.celery_task_id == task_id)
                    .first()
                )
                current_stage = task_record.stages if task_record else None

                service.mark_as_failed(
                    task_id, error_message=str(e), stage=current_stage
                )
        except Exception as db_error:
            print(f"Failed to mark task as failed: {db_error}")

        # Re-raise the original exception
        raise


@app.task
def sync_file_by_id_task(file_id, agent_id, user_id, organisation_id, url=None):
    """
    Synchronizes a single file by its ID.

    Args:
        file_id (str): The ID of the file to synchronize.
        agent_id (str): The ID of the agent initiating the sync.
        user_id (str): The ID of the user who owns the file.
        organisation_id (str): The ID of the organization.
        url (str, optional): The URL of the file. Defaults to None.
    """
    service = GoogleDriveService()
    return service.sync_file_by_id(file_id, agent_id, user_id, organisation_id, url)


@app.task(bind=True)
def sync_folder_recursively_task(self, organisation_id, folder_id):
    """
    Recursively synchronizes a folder and its contents with progress tracking.

    This task uses the TaskStatusService to provide real-time updates on its progress.
    It creates a task record upon starting and updates it as it progresses through
    different stages (initialization, processing, chunking, embedding, etc.),
    or marks it as failed if an error occurs.

    Args:
        organisation_id (str): The ID of the organization.
        folder_id (str): The ID of the folder to synchronize.
    """
    task_id = self.request.id

    # Create task record
    try:
        with get_task_status_service() as service:
            service.create_task(
                celery_task_id=task_id,
                task_name="Folder Recursive Sync",
                service_type="GDRIVE",
                stage=ProcessingStageEnum.INITIALIZATION,
                organisation_id=organisation_id,
                user_id=None,  # No specific user for this task
            )
    except Exception as e:
        # If we can't even create the task record, log and re-raise
        print(f"Failed to create task record: {e}")
        raise

    # Execute the main task with separate error handling
    try:
        # Update status to IN_PROGRESS and set stage to PROCESSING
        with get_task_status_service() as service:
            service.update_task_status(
                task_id,
                TaskStatusEnum.IN_PROGRESS,
                progress=10,
                stage=ProcessingStageEnum.PROCESSING,
            )
            # Force commit to ensure the update is saved
            service.db.commit()

        # Get the Google Drive service
        drive_service = GoogleDriveService()
        drive = drive_service.get_service_account_drive_service(organisation_id)

        if not drive:
            with get_task_status_service() as service:
                service.mark_as_failed(
                    task_id,
                    error_message="Failed to get authenticated drive service",
                    stage=ProcessingStageEnum.PROCESSING,
                )
            return False, "Failed to get authenticated drive service", 0, 0

        # Real implementation with proper stage updates
        try:
            # Update to CHUNKING stage (25%)
            # Chunking takes more time, so allocate more percentage progress
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=25,
                    stage=ProcessingStageEnum.CHUNKING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to EMBEDDING stage (40%)
            # Embedding is time-intensive, so allocate more percentage progress
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=40,
                    stage=ProcessingStageEnum.EMBEDDING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to VECTOR_DB_STORING stage (60%)
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=60,
                    stage=ProcessingStageEnum.VECTOR_DB_STORING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to NEO4J_RELATIONSHIP stage (80%)
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=80,
                    stage=ProcessingStageEnum.NEO4J_RELATIONSHIP,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Update to SYNCING stage (90%) before actual sync
            with get_task_status_service() as service:
                service.update_task_status(
                    task_id,
                    TaskStatusEnum.IN_PROGRESS,
                    progress=90,
                    stage=ProcessingStageEnum.SYNCING,
                )
                # Force commit to ensure the update is saved
                service.db.commit()

            # Actual sync operation
            files_synced, folders_synced = (
                drive_service.sync_folder_recursively_with_permissions(
                    drive, organisation_id, folder_id, task_id
                )
            )

        except Exception as sync_error:
            # If sync fails, mark the task as failed with the current stage
            with get_task_status_service() as service:
                service.mark_as_failed(
                    task_id,
                    error_message=str(sync_error),
                    stage=ProcessingStageEnum.SYNCING,
                )
            # Re-raise the exception to be caught by the outer try-except
            raise sync_error

        # Update status to SUCCESS with completion stage
        with get_task_status_service() as service:
            service.update_task_status(
                task_id,
                TaskStatusEnum.COMPLETED,
                progress=100,
                stage=ProcessingStageEnum.COMPLETION,
            )

        return True, "Folder sync completed successfully", files_synced, folders_synced

    except Exception as e:
        # Mark the task as FAILED using a separate session
        try:
            with get_task_status_service() as service:
                # If we don't know the exact stage where the error occurred,
                # we'll use the current stage from the task record
                task_record = (
                    service.db.query(TaskExecutionStatus)
                    .filter(TaskExecutionStatus.celery_task_id == task_id)
                    .first()
                )
                current_stage = task_record.stages if task_record else None

                service.mark_as_failed(
                    task_id, error_message=str(e), stage=current_stage
                )
        except Exception as db_error:
            print(f"Failed to mark task as failed: {db_error}")

        # Re-raise the original exception
        raise
