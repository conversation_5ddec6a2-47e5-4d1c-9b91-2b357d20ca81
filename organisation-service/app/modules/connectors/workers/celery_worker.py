from celery import Celery

from app.core.config import settings

app = Celery("gdrive_sync", broker=settings.REDIS_URI, backend=settings.REDIS_URI)
app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    broker_transport_options={
        "visibility_timeout": 3600,  # 1 hour
        "broker_connection_retry_on_startup": True,
        "keepalive": True,
    },
)

# Import tasks to register them with Celery
from app.modules.connectors.workers import tasks
