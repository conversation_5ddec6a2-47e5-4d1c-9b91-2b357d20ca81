"""
Test configuration and fixtures for the task execution system.
"""

import os
import sys
import uuid
from datetime import datetime

import pytest
from sqlalchemy import Column, DateTime, Enum, Integer, String, Text, create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import StaticPool

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "app"))

from app.modules.connectors.services.task_status_service import TaskStatusService
from app.modules.tasks.models.task_status import TaskStatusEnum

# Create a test-specific base and model for SQLite compatibility
TestBase = declarative_base()


class TestTaskExecutionStatus(TestBase):
    __tablename__ = "task_execution_status"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    task_name = Column(String, nullable=False)
    task_type = Column(String, nullable=True)
    service_type = Column(String, nullable=True)
    status = Column(
        Enum(TaskStatusEnum), nullable=False, default=TaskStatusEnum.PENDING
    )
    progress = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    celery_task_id = Column(String, nullable=True)
    organisation_id = Column(String(36), nullable=True)
    user_id = Column(String(36), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


@pytest.fixture(scope="session")
def test_engine():
    """Create a test database engine using SQLite in memory."""
    from sqlalchemy import String
    from sqlalchemy.dialects.postgresql import UUID

    # Monkey patch UUID to use String for SQLite
    original_uuid_type = UUID

    def mock_uuid_type(*args, **kwargs):
        return String(36)  # UUID string length

    # Replace UUID with String for SQLite
    import app.modules.tasks.models.task_status

    app.modules.tasks.models.task_status.UUID = mock_uuid_type

    engine = create_engine(
        "sqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False,
    )

    # Create all tables
    TestBase.metadata.create_all(bind=engine)

    # Restore original UUID type
    app.modules.tasks.models.task_status.UUID = original_uuid_type

    return engine


@pytest.fixture(scope="function")
def test_db_session(test_engine):
    """Create a test database session."""
    TestingSessionLocal = sessionmaker(
        autocommit=False, autoflush=False, bind=test_engine
    )
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.close()


@pytest.fixture(scope="function")
def task_service(test_db_session):
    """Create a TaskStatusService instance with test database session."""
    return TaskStatusService(test_db_session)


@pytest.fixture
def sample_task_data():
    """Sample task data for testing."""
    return {
        "celery_task_id": "test-task-12345",
        "task_name": "Test Google Drive Sync",
        "service_type": "GDRIVE",
        "task_type": "SYNC",
        "organisation_id": "1e95f0df-4a35-4fdd-bc28-6ac681bf6675",
        "user_id": "54c1059e-c891-4163-bbe6-1e5ad75601e1",
    }
