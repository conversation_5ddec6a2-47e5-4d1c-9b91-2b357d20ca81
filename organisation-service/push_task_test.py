#!/usr/bin/env python3
"""
Simple test script to push a Celery task and monitor its status.
"""

import os
import sys
import time

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))

from app.db.postgres import get_db
from app.modules.connectors.workers.tasks import sync_drive_task, sync_file_by_id_task
from app.modules.tasks.models.task_status import TaskExecutionStatus


def test_sync_drive_task():
    """Test the sync_drive_task with status monitoring."""
    print("🚀 Testing sync_drive_task with status tracking")
    print("=" * 50)

    # Queue the task
    task = sync_drive_task.delay(
        organisation_id="1e95f0df-4a35-4fdd-bc28-6ac681bf6675",
        user_id="54c1059e-c891-4163-bbe6-1e5ad75601e1",
        full_sync=False,
    )

    print(f"✅ Task queued with ID: {task.id}")

    # Monitor task status in database
    print("📊 Monitoring task status...")

    max_wait_time = 60  # seconds
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        try:
            # Get task status from database
            db = next(get_db())
            task_record = (
                db.query(TaskExecutionStatus)
                .filter(TaskExecutionStatus.celery_task_id == task.id)
                .first()
            )

            if task_record:
                print(f"   Status: {task_record.status}")
                if task_record.progress:
                    print(f"   Progress: {task_record.progress}%")
                if task_record.error_message:
                    print(f"   Error: {task_record.error_message}")

                # Check if task is complete
                if task_record.status.value in ["COMPLETED", "FAILED"]:
                    print(f"🏁 Task finished with status: {task_record.status}")
                    if task_record.error_message:
                        print(f"   Error details: {task_record.error_message}")
                    db.close()
                    return task_record.status.value == "COMPLETED"
            else:
                print("   Task record not found in database yet...")

            db.close()

        except Exception as e:
            print(f"   Error checking task status: {e}")

        time.sleep(3)

    print("⏰ Task monitoring timed out")
    return False


def test_sync_file_task():
    """Test the sync_file_by_id_task."""
    print("\n🚀 Testing sync_file_by_id_task")
    print("=" * 50)

    # Queue the task
    task = sync_file_by_id_task.delay(
        file_id="17Og737AUcJKn7Qvp7U3F5OYKXRN8fPbJaB12Bs91qo8",
        agent_id="04b41ff8-29aa-4907-96ef-91c48ba5e2b7",
        user_id="54c1059e-c891-4163-bbe6-1e5ad75601e1",
        organisation_id="1e95f0df-4a35-4fdd-bc28-6ac681bf6675",
    )

    print(f"✅ File sync task queued with ID: {task.id}")

    # This task doesn't have status tracking yet, so just wait for completion
    try:
        result = task.get(timeout=30)
        print(f"✅ File sync task completed: {result}")
        return True
    except Exception as e:
        print(f"❌ File sync task failed: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Starting Celery Task Tests")

    # Test 1: Drive sync task with status tracking
    drive_success = test_sync_drive_task()

    # Test 2: File sync task
    file_success = test_sync_file_task()

    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Drive sync task: {'✅ PASSED' if drive_success else '❌ FAILED'}")
    print(f"File sync task: {'✅ PASSED' if file_success else '❌ FAILED'}")

    if drive_success and file_success:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("💥 Some tests failed!")
        sys.exit(1)
