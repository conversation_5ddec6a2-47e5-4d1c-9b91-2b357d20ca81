-- SQL script to manually create the task_execution_status table and enum type

CREATE TYPE taskstatusenum AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED');

CREATE TABLE task_execution_status (
    id UUID PRIMARY KEY,
    task_name VA<PERSON>HAR NOT NULL,
    task_type <PERSON><PERSON><PERSON><PERSON>,
    service_type VA<PERSON>HAR,
    status taskstatusenum NOT NULL,
    progress INTEGER,
    error_message TEXT,
    celery_task_id VARCHAR,
    organisation_id UUID,
    user_id UUID,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);