syntax = "proto3";

package connector;

// Source type enum
enum SourceType {
  SOURCE_TYPE_UNSPECIFIED = 0; // Default value, should not be used
  GOOGLE_DRIVE = 1;
  SLACK = 2;
  JIRA = 3;
  // Add more as needed
}

// Connector type enum for factory pattern
enum ConnectorType {
  CONNECTOR_TYPE_UNSPECIFIED = 0; // Default value, should not be used
  STRUCTURED = 1;    // For connectors like Jira, databases
  UNSTRUCTURED = 2;  // For connectors like Google Drive, Slack
}

// The Connector service definition
service ConnectorService {
  // Add a new source with credentials
  rpc addSource (AddSourceRequest) returns (AddSourceResponse) {}

  // List sources for an organisation
  rpc listSources (ListSourcesRequest) returns (ListSourcesResponse) {}

  // Delete a source
  rpc deleteSource (DeleteSourceRequest) returns (DeleteSourceResponse) {}
  
  // Update source credentials
  rpc updateSourceCredentials (UpdateSourceCredentialsRequest) returns (UpdateSourceCredentialsResponse) {}
  
  // Validate source and get accessible folders
  rpc validateSource (ValidateSourceRequest) returns (ValidateSourceResponse) {}
  
  // Get connector information and capabilities
  rpc getConnectorInfo (GetConnectorInfoRequest) returns (GetConnectorInfoResponse) {}
  
  // List available connector types
  rpc listConnectorTypes (ListConnectorTypesRequest) returns (ListConnectorTypesResponse) {}
  
  // Test connector connection
  rpc testConnection (TestConnectionRequest) returns (TestConnectionResponse) {}
  
  // Sync data from a specific source
  rpc syncSource (SyncSourceRequest) returns (SyncSourceResponse) {}
  
  // Get sync status for a source
  rpc getSyncStatus (GetSyncStatusRequest) returns (GetSyncStatusResponse) {}
  
  // Google Drive specific operations
  rpc syncDrive (SyncDriveRequest) returns (SyncDriveResponse) {}
  rpc disconnectDrive (DisconnectDriveRequest) returns (DisconnectDriveResponse) {}
  rpc listFiles (ListFilesRequest) returns (ListFilesResponse) {}
  rpc getFileDetails (GetFileDetailsRequest) returns (GetFileDetailsResponse) {}
  rpc getFolderById (GetFolderByIdRequest) returns (GetFolderByIdResponse) {}
  rpc syncFolderByIds (SyncFolderByIdsRequest) returns (SyncFolderByIdsResponse) {}
  rpc checkFileAccess (CheckFileAccessRequest) returns (CheckFileAccessResponse) {}
  rpc syncFileByUrl (SyncFileByUrlRequest) returns (SyncFileByUrlResponse) {}
  rpc listTopLevelFolders (ListTopLevelFoldersRequest) returns (ListTopLevelFoldersResponse) {}
}

// Source model
message SourceModel {
  string id = 1;
  string organisation_id = 2;
  SourceType type = 3;
  string name = 4;
  string created_at = 5;
  string updated_at = 6;
  ConnectorType connector_type = 7;
  string status = 8;  // "active", "inactive", "error"
  string last_sync_at = 9;
  map<string, string> metadata = 10;  // Additional source-specific metadata
}

// File info model for responses
message FileInfo {
  string id = 1;
  string name = 2;
  string mime_type = 3;
  int64 size = 4;
  string created_at = 5;
  string modified_at = 6;
}

// Folder model for responses
message Folder {
  string id = 1;
  string name = 2;
  string parent_id = 3;
  int32 child_count = 4;
  string created_at = 5;
  string modified_at = 6;
}

// Request to add a source with service account
message AddSourceRequest {
  string organisation_id = 1;
  SourceType type = 2;
  string name = 3;
  string key = 4; // Service Account JSON or API key (required)
  repeated string file_ids = 5; // Optional list of specific file IDs to sync (for Google Drive)
  optional string jira_url = 6; // Optional: Jira URL (for Jira sources)
  optional string jira_email = 7; // Optional: Jira email (for Jira sources)
  map<string, string> config = 8; // Additional connector-specific configuration
}

// Response for add source operation
message AddSourceResponse {
  bool success = 1;
  string message = 2;
  SourceModel source = 3;
  repeated FileInfo synced_files = 4; // List of files that were synced
}

// Request to list sources
message ListSourcesRequest {
  string organisation_id = 1;
  optional SourceType type = 2; // Filter by source type
  optional ConnectorType connector_type = 3; // Filter by connector type
}

// Response for list sources operation
message ListSourcesResponse {
  bool success = 1;
  string message = 2;
  repeated SourceModel sources = 3;
  bool isInitialMapping = 4;  // True if at least one department other than general has access to at least one folder
}

// Request to delete a source
message DeleteSourceRequest {
  string source_id = 1;
  string user_id = 2; // Admin user ID
  string organisation_id = 3;
}

// Response for delete source operation
message DeleteSourceResponse {
  bool success = 1;
  string message = 2;
}

// Request to update source credentials
message UpdateSourceCredentialsRequest {
  string source_id = 1;
  string user_id = 2;
  string key = 3; // Service Account JSON or API key (required)
  optional string jira_url = 4; // Optional: Jira URL (for Jira sources)
  optional string jira_email = 5; // Optional: Jira email (for Jira sources)
  map<string, string> config = 6; // Additional connector-specific configuration
}

// Response for update source credentials operation
message UpdateSourceCredentialsResponse {
  bool success = 1;
  string message = 2;
  SourceModel source = 3;
}

// Request to validate a source
message ValidateSourceRequest {
  string source_id = 1;
  string organisation_id = 2;
}

// Response for source validation
message ValidateSourceResponse {
  bool success = 1;
  string message = 2;
  repeated Folder accessible_folders = 3;
  string connection_status = 4; // "connected", "disconnected", "error"
}

// Connector capability information
message ConnectorCapability {
  string name = 1;
  string description = 2;
  bool supported = 3;
}

// Connector information model
message ConnectorInfo {
  SourceType type = 1;
  ConnectorType connector_type = 2;
  string name = 3;
  string description = 4;
  string version = 5;
  repeated ConnectorCapability capabilities = 6;
  repeated string supported_file_types = 7;
  map<string, string> configuration_schema = 8;
}

// Request to get connector information
message GetConnectorInfoRequest {
  SourceType type = 1;
}

// Response for get connector information
message GetConnectorInfoResponse {
  bool success = 1;
  string message = 2;
  ConnectorInfo connector_info = 3;
}

// Request to list available connector types
message ListConnectorTypesRequest {
  optional ConnectorType connector_type = 1; // Filter by connector type
}

// Response for list connector types
message ListConnectorTypesResponse {
  bool success = 1;
  string message = 2;
  repeated ConnectorInfo available_connectors = 3;
}

// Request to test connector connection
message TestConnectionRequest {
  string source_id = 1;
  string organisation_id = 2;
}

// Response for test connection
message TestConnectionResponse {
  bool success = 1;
  string message = 2;
  string connection_status = 3; // "connected", "disconnected", "error"
  map<string, string> connection_details = 4; // Additional connection information
}

// Request to sync data from a source
message SyncSourceRequest {
  string source_id = 1;
  string organisation_id = 2;
  bool full_sync = 3; // Whether to perform full sync or incremental
  repeated string specific_items = 4; // Optional: specific files/folders to sync
  string user_id = 5; // User ID who initiated the sync
}

// Response for sync source operation
message SyncSourceResponse {
  bool success = 1;
  string message = 2;
  string sync_id = 3; // Unique identifier for this sync operation
  string sync_status = 4; // "started", "in_progress", "completed", "failed"
  int32 items_queued = 5; // Number of items queued for sync
}

// Request to get sync status
message GetSyncStatusRequest {
  string source_id = 1;
  string organisation_id = 2;
  optional string sync_id = 3; // Optional: specific sync operation ID
}

// Sync statistics model
message SyncStats {
  int32 total_items = 1;
  int32 processed_items = 2;
  int32 successful_items = 3;
  int32 failed_items = 4;
  string started_at = 5;
  string completed_at = 6;
  string estimated_completion = 7;
}

// Response for get sync status
message GetSyncStatusResponse {
  bool success = 1;
  string message = 2;
  string sync_status = 3; // "idle", "in_progress", "completed", "failed"
  SyncStats sync_stats = 4;
  repeated string error_messages = 5; // Any error messages from sync
  string last_sync_at = 6;
}

// Google Drive specific message definitions

// Simple folder info model
message FolderInfo {
  string id = 1;
  string name = 2;
}

// Request to disconnect Google Drive
message DisconnectDriveRequest {
  string organisation_id = 1;
}

// Response for disconnect operation
message DisconnectDriveResponse {
  bool success = 1;
  string message = 2;
}

// Request to sync Google Drive
message SyncDriveRequest {
  string user_id = 1;
  string organisation_id = 2;
  bool full_sync = 3; // Whether to perform full sync or incremental
}

// Response for sync Google Drive operation
message SyncDriveResponse {
  bool success = 1;
  string message = 2;
  int32 files_synced = 3;
  int32 folders_synced = 4;
  string sync_status = 5; // "queued", "in_progress", "completed", "failed"
  string sync_id = 6; // Unique identifier for this sync operation
}

// Request to list files
message ListFilesRequest {
  string user_id = 1;
  string folder_id = 2;  // Optional, to list files in a specific folder
  int32 page = 3;
  int32 page_size = 4;
  string organisation_id = 5;  // Organisation ID for source context
}

// Google Drive file model
message DriveFileModel {
  string id = 1;
  string name = 2;
  string mime_type = 3;
  string web_view_link = 4;
  string created_time = 5;
  string modified_time = 6;
  string parent_folder_id = 7;
  int64 size = 8;
  repeated string shared_with = 9;  // List of user emails with access
  bool is_folder = 10;
  int32 child_count = 11;  // Number of children (for folders)
}

// Response for list files operation
message ListFilesResponse {
  bool success = 1;
  string message = 2;
  repeated DriveFileModel files = 3;
  int32 total_count = 4;
  int32 page = 5;
  int32 page_size = 6;
}

// Request to get file details
message GetFileDetailsRequest {
  string user_id = 1;
  string file_id = 2;
  string organisation_id = 3;
}

// Response for get file details operation
message GetFileDetailsResponse {
  bool success = 1;
  string message = 2;
  DriveFileModel file = 3;
}

// Request to get folder by ID
message GetFolderByIdRequest {
  string organisation_id = 1;  // Organisation ID for service account
  string folder_id = 2;
}

// Response for get folder by ID operation
message GetFolderByIdResponse {
  bool success = 1;
  string message = 2;
  DriveFileModel folder = 3;
  repeated DriveFileModel children = 4;
}

// Request to sync specific folders by IDs
message SyncFolderByIdsRequest {
  string organisation_id = 1;  // Organisation ID for service account
  repeated string folder_ids = 2;  // List of folder IDs to sync
}

// Response for sync folders by IDs operation
message SyncFolderByIdsResponse {
  bool success = 1;
  string message = 2;
  int32 files_synced = 3;
  int32 folders_synced = 4;
  string sync_status = 5;
  repeated FolderInfo synced_folders = 6;  // List of synced folders
}

// Request for file access checking
message CheckFileAccessRequest {
  string user_id = 1;
  string file_id = 2;
  string organisation_id = 3;
}

// Response for file access checking
message CheckFileAccessResponse {
  bool success = 1;
  string message = 2;
  bool has_access = 3;
}

// Request to sync a specific Google Drive file by URL
message SyncFileByUrlRequest {
  repeated string drive_url = 1;       // Google Drive URLs
  string agent_id = 2;        // Agent ID
  string user_id = 3;         // Optional user ID
  string organisation_id = 4;  // Organisation ID
}

// Information about a synced file
message SyncedFileInfo {
  string file_id = 1;         // The ID of the synced file
  string file_name = 2;       // The name of the synced file
  string drive_url = 3;       // The original URL that was synced
  string sync_status = 4;     // "completed" or "failed"
  string error_message = 5;   // Error message if sync failed
}

// Response for sync file by URL operation
message SyncFileByUrlResponse {
  bool success = 1;
  string message = 2;
  repeated SyncedFileInfo synced_files = 3;  // Information about all synced files
  int32 total_files = 4;      // Total number of URLs processed
  int32 successful_syncs = 5; // Number of successful syncs
  int32 failed_syncs = 6;     // Number of failed syncs
}

// Request to list top-level folders using service account
message ListTopLevelFoldersRequest {
  string organisation_id = 1;  // Organisation ID for service account
}

// Response for list top-level folders operation
message ListTopLevelFoldersResponse {
  bool success = 1;
  string message = 2;
  repeated FolderInfo folders = 3;  // List of top-level folders
}