syntax = "proto3";

package authentication;

// OAuth Provider enumeration
enum OAuthProvider {
  OAUTH_PROVIDER_UNSPECIFIED = 0;
  OAUTH_PROVIDER_GOOGLE = 1;
  OAUTH_PROVIDER_MICROSOFT = 2;
  OAUTH_PROVIDER_GITHUB = 3;
  OAUTH_PROVIDER_SLACK = 4;
  OAUTH_PROVIDER_CUSTOM = 5;
  OAUTH_PROVIDER_JIRA = 6;
  OAUTH_PROVIDER_ZOHO = 7;
}

// OAuth Authorization Request
message OAuthAuthorizeRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
  repeated string scopes = 4;
  string redirect_uri = 5;
}

// OAuth Authorization Response
message OAuthAuthorizeResponse {
  bool success = 1;
  string message = 2;
  string authorization_url = 3;
  string state = 4;
}

// OAuth Callback Request
message OAuthCallbackRequest {
  string code = 1;
  string state = 2;
  string error = 3;
}

// OAuth Callback Response
message OAuthCallbackResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string tool_name = 4;  // Required field
  string provider = 5;
  string redirect_url = 6;  // Optional redirect URL from authorization request
}

// OAuth Credential Request
message OAuthCredentialRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
}

// OAuth Credential Response
message OAuthCredentialResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
  string tool_name = 4;  // Required field
  string provider = 5;
  string access_token = 6;
  string refresh_token = 7;
  string token_type = 8;
  int32 expires_in = 9;
  string scope = 10;
  repeated string scopes = 11;

  string bot_token = 12;
  string user_token = 13;
  string bot_user_id = 14;
  string user_id_slack = 15;
  string team_id = 16;
  string team_name = 17;
  string user_scope = 18;
}

// Server OAuth Credential Request (for server-to-server access)
message ServerOAuthCredentialRequest {
  string server_auth_key = 1;
  string user_id = 2;
  string tool_name = 3;  // Required field
  OAuthProvider provider = 4;
}

// OAuth Providers List Request
message OAuthProvidersListRequest {
  // Empty request - returns all available providers
}

// OAuth Provider Info
message OAuthProviderInfo {
  OAuthProvider provider = 1;
  string name = 2;
  string display_name = 3;
  repeated string supported_tools = 4;
  bool is_configured = 5;
}

// OAuth Providers List Response
message OAuthProvidersListResponse {
  bool success = 1;
  string message = 2;
  repeated OAuthProviderInfo providers = 3;
}

// Tool Scopes Request
message OAuthToolScopesRequest {
  string tool_name = 1;  // Required field
  OAuthProvider provider = 2;
}

// Tool Scopes Response
message OAuthToolScopesResponse {
  bool success = 1;
  string message = 2;
  string tool_name = 3;  // Required field
  OAuthProvider provider = 4;
  repeated string scopes = 5;
  string description = 6;
}

// Delete Credential Request
message DeleteOAuthCredentialRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
}

// Delete Credential Response
message DeleteOAuthCredentialResponse {
  bool success = 1;
  string message = 2;
}

// OAuth Refresh Request
message OAuthRefreshRequest {
  string user_id = 1;
  string tool_name = 2;  // Required field
  OAuthProvider provider = 3;
}

// OAuth Refresh Response
message OAuthRefreshResponse {
  bool success = 1;
  string message = 2;
  string access_token = 3;
  string token_type = 4;
  int32 expires_in = 5;
  string scope = 6;
}

// Health Check Request
message HealthCheckRequest {
  // Empty request
}

// Health Check Response
message HealthCheckResponse {
  bool healthy = 1;
  string status = 2;
  string version = 3;
  map<string, string> dependencies = 4;
}

// Authentication Service
service AuthenticationService {
  // OAuth Flow Methods
  rpc InitiateOAuth(OAuthAuthorizeRequest) returns (OAuthAuthorizeResponse);
  rpc HandleOAuthCallback(OAuthCallbackRequest) returns (OAuthCallbackResponse);
  rpc RefreshOAuthTokens(OAuthRefreshRequest) returns (OAuthRefreshResponse);

  // Credential Management
  rpc GetOAuthCredentials(OAuthCredentialRequest) returns (OAuthCredentialResponse);
  rpc GetServerOAuthCredentials(ServerOAuthCredentialRequest) returns (OAuthCredentialResponse);
  rpc DeleteOAuthCredentials(DeleteOAuthCredentialRequest) returns (DeleteOAuthCredentialResponse);

  // Provider Information
  rpc ListOAuthProviders(OAuthProvidersListRequest) returns (OAuthProvidersListResponse);
  rpc GetToolScopes(OAuthToolScopesRequest) returns (OAuthToolScopesResponse);

  // Health Check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}
