syntax = "proto3";

package search;

// The Search service definition
service SearchService {
  // Search for documents semantically similar to a query
  rpc searchSimilarDocuments(SearchSimilarDocumentsRequest) returns (SearchSimilarDocumentsResponse) {}
  
  // Batch search for documents semantically similar to multiple queries
  rpc batchSearchSimilarDocuments(BatchSearchSimilarDocumentsRequest) returns (BatchSearchSimilarDocumentsResponse) {}
  
  // Search with advanced filters and options
  rpc advancedSearch(AdvancedSearchRequest) returns (AdvancedSearchResponse) {}
  
  // Get search suggestions/autocomplete
  rpc getSearchSuggestions(SearchSuggestionsRequest) returns (SearchSuggestionsResponse) {}
  
  // Search within specific sources
  rpc searchBySource(SearchBySourceRequest) returns (SearchBySourceResponse) {}
  
  // Get search analytics and insights
  rpc getSearchAnalytics(SearchAnalyticsRequest) returns (SearchAnalyticsResponse) {}
}

// Search type enum
enum SearchType {
  SEMANTIC = 0;     // Semantic/vector search
  KEYWORD = 1;      // Traditional keyword search
  HYBRID = 2;       // Combination of semantic and keyword
  GRAPH = 3;        // Knowledge graph-based search
}

// Search scope enum
enum SearchScope {
  ALL = 0;          // Search across all accessible content
  DEPARTMENT = 1;   // Search within department-accessible content
  SOURCE = 2;       // Search within specific sources
  FILE_TYPE = 3;    // Search within specific file types
}

// Entity information from knowledge graph
message EntityInfo {
  string id = 1;
  string name = 2;
  string type = 3;
  map<string, string> properties = 4;  // Additional entity properties
  float relevance_score = 5;  // Relevance score for this entity
}

// Relationship information from knowledge graph
message RelationshipInfo {
  string id = 1;
  string type = 2;
  string source_entity_id = 3;
  string target_entity_id = 4;
  string source_entity_name = 5;  // Name of source entity
  string target_entity_name = 6;  // Name of target entity
  map<string, string> properties = 7;  // Additional relationship properties
  float confidence_score = 8;  // Confidence score for this relationship
  float relevance_score = 9;  // Relevance score for this relationship
  string context = 10;  // Context information about the relationship
}

// Graph context containing all knowledge graph discoveries
message GraphContext {
  // All entities discovered (chunk-based + graph-discovered)
  repeated EntityInfo all_entities = 1;
  repeated RelationshipInfo all_relationships = 2;
}

// Search result item
message SearchResultItem {
  string file_id = 1;
  string file_name = 2;
  string mime_type = 3;
  string web_view_link = 4;
  string created_time = 5;
  string modified_time = 6;
  float score = 7;  // Similarity score
  string vector_id = 8;  // ID of the vector in Pinecone
  string chunk_text = 9;  // The actual text content of the matched chunk
  string search_type = 10;  // Type of search performed (e.g., "hybrid", "semantic", "graph")
  string source_type = 11;  // Type of source (e.g., "GOOGLE_DRIVE", "SLACK")
  string source_id = 12;    // ID of the source
  map<string, string> metadata = 13;  // Additional metadata
}

// Request to search for semantically similar documents
message SearchSimilarDocumentsRequest {
  string user_id = 1;
  string query_text = 2;
  int32 top_k = 3;  // Number of results to return
  string agent_id = 4;  // Optional agent ID to filter results by department access
  string organisation_id = 5;  // Organization ID (mandatory for production use)
  repeated string file_ids = 6;  // Optional list of specific file IDs to search within
  float least_score = 7;  // Optional minimum score threshold for results
  SearchType search_type = 8;  // Type of search to perform
  SearchScope search_scope = 9;  // Scope of search
  repeated string source_ids = 10;  // Optional list of source IDs to search within
  repeated string file_types = 11;  // Optional list of file types to filter by
}

// Response for semantic search operation
message SearchSimilarDocumentsResponse {
  bool success = 1;
  string message = 2;
  repeated SearchResultItem results = 3;
  GraphContext graph_context = 4;  // Separate graph context with all discoveries
  int32 total_results = 5;  // Total number of results found
  float search_time_ms = 6;  // Time taken for search in milliseconds
  string search_id = 7;  // Unique identifier for this search
}

// Request to batch search for semantically similar documents
message BatchSearchSimilarDocumentsRequest {
  string user_id = 1;
  repeated string query_texts = 2;
  int32 top_k = 3;  // Number of results to return per query
  string agent_id = 4;  // Optional agent ID to filter results by department access
  string organisation_id = 5;  // Organization ID (mandatory for production use)
  repeated string file_ids = 6;  // Optional list of specific file IDs to search within
  float least_score = 7;  // Optional minimum score threshold for results
  SearchType search_type = 8;  // Type of search to perform
  SearchScope search_scope = 9;  // Scope of search
  repeated string source_ids = 10;  // Optional list of source IDs to search within
  repeated string file_types = 11;  // Optional list of file types to filter by
}

// Results for a single query in a batch
message QueryResults {
  string query_text = 1;  // The original query text
  repeated SearchResultItem results = 2;  // Search results for this query
  GraphContext graph_context = 3;  // Graph context for this query
  float search_time_ms = 4;  // Time taken for this query in milliseconds
}

// Response for batch semantic search operation
message BatchSearchSimilarDocumentsResponse {
  bool success = 1;
  string message = 2;
  repeated QueryResults query_results = 3;
  float total_search_time_ms = 4;  // Total time for all queries
  string batch_search_id = 5;  // Unique identifier for this batch search
}

// Advanced search filters
message SearchFilters {
  repeated string file_types = 1;  // Filter by file types
  repeated string source_ids = 2;  // Filter by source IDs
  repeated string department_ids = 3;  // Filter by department access
  string date_from = 4;  // Filter by creation/modification date (ISO format)
  string date_to = 5;    // Filter by creation/modification date (ISO format)
  int64 min_file_size = 6;  // Minimum file size in bytes
  int64 max_file_size = 7;  // Maximum file size in bytes
  repeated string tags = 8;  // Filter by tags/labels
  map<string, string> custom_filters = 9;  // Custom source-specific filters
}

// Request for advanced search
message AdvancedSearchRequest {
  string user_id = 1;
  string query_text = 2;
  string organisation_id = 3;
  SearchType search_type = 4;
  SearchScope search_scope = 5;
  SearchFilters filters = 6;
  int32 top_k = 7;  // Number of results to return
  float least_score = 8;  // Minimum score threshold
  string agent_id = 9;  // Optional agent ID
  bool include_graph_context = 10;  // Whether to include graph context
}

// Response for advanced search
message AdvancedSearchResponse {
  bool success = 1;
  string message = 2;
  repeated SearchResultItem results = 3;
  GraphContext graph_context = 4;
  int32 total_results = 5;
  float search_time_ms = 6;
  string search_id = 7;
  map<string, int32> facets = 8;  // Faceted search results (e.g., file type counts)
}

// Request for search suggestions
message SearchSuggestionsRequest {
  string user_id = 1;
  string organisation_id = 2;
  string partial_query = 3;  // Partial query text for autocomplete
  int32 max_suggestions = 4;  // Maximum number of suggestions to return
  SearchScope search_scope = 5;
  repeated string source_ids = 6;  // Optional source filtering
}

// Search suggestion item
message SearchSuggestion {
  string suggestion_text = 1;
  string suggestion_type = 2;  // "query", "entity", "file", "topic"
  float relevance_score = 3;
  map<string, string> metadata = 4;
}

// Response for search suggestions
message SearchSuggestionsResponse {
  bool success = 1;
  string message = 2;
  repeated SearchSuggestion suggestions = 3;
}

// Request to search by source
message SearchBySourceRequest {
  string user_id = 1;
  string organisation_id = 2;
  string query_text = 3;
  string source_id = 4;  // Specific source to search within
  SearchType search_type = 5;
  int32 top_k = 6;
  float least_score = 7;
  string agent_id = 8;
  SearchFilters filters = 9;
}

// Response for search by source
message SearchBySourceResponse {
  bool success = 1;
  string message = 2;
  repeated SearchResultItem results = 3;
  GraphContext graph_context = 4;
  int32 total_results = 5;
  float search_time_ms = 6;
  string search_id = 7;
  string source_name = 8;  // Name of the searched source
}

// Search analytics data
message SearchAnalytics {
  int32 total_searches = 1;
  int32 successful_searches = 2;
  int32 failed_searches = 3;
  float average_response_time_ms = 4;
  repeated string top_queries = 5;
  repeated string top_file_types = 6;
  repeated string top_sources = 7;
  map<string, int32> search_type_distribution = 8;
  map<string, int32> daily_search_counts = 9;  // Date -> count mapping
}

// Request for search analytics
message SearchAnalyticsRequest {
  string user_id = 1;
  string organisation_id = 2;
  string date_from = 3;  // Analytics date range start (ISO format)
  string date_to = 4;    // Analytics date range end (ISO format)
  repeated string source_ids = 5;  // Optional source filtering
  repeated string department_ids = 6;  // Optional department filtering
}

// Response for search analytics
message SearchAnalyticsResponse {
  bool success = 1;
  string message = 2;
  SearchAnalytics analytics = 3;
}