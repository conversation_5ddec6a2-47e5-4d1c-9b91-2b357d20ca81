"""
gRPC Authentication Service Implementation

This module implements the gRPC service for OAuth authentication
and credential management.
"""

import asyncio
import logging

import grpc
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.oauth_providers import OAuthProvider, oauth_provider_manager
from app.db.session import get_db
from app.grpc_ import authentication_pb2, authentication_pb2_grpc
from app.services.oauth_service import oauth_service
from app.utils.redis_service import redis_service
from app.utils.secret_manager import secret_manager
from app.services.integration_functions import IntegrationService
from app.services.user_functions import UserIntegrationsService

logger = logging.getLogger(__name__)


class AuthenticationServicer(authentication_pb2_grpc.AuthenticationServiceServicer):
    """gRPC Authentication Service implementation."""

    def __init__(self):
        """Initialize the authentication servicer."""
        self.oauth_service = oauth_service
        self.integration_service = IntegrationService()
        self.user_integration_service = UserIntegrationsService()

    def _get_db_session(self) -> Session:
        """Get database session."""
        return next(get_db())

    def _validate_server_auth_key(self, server_auth_key: str) -> bool:
        """Validate server authentication key."""
        expected_key = settings.SERVER_AUTH_KEY.get_secret_value()
        return server_auth_key == expected_key

    def InitiateOAuth(
        self, request: authentication_pb2.OAuthAuthorizeRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthAuthorizeResponse:
        """Initiate OAuth authorization flow."""
        try:
            # Convert provider enum to OAuthProvider
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_RAPID_HRMS: OAuthProvider.RAPID_HRMS,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider)
            if not provider:
                return authentication_pb2.OAuthAuthorizeResponse(
                    success=False, message="Invalid OAuth provider specified"
                )

            # Generate authorization URL
            auth_url, state_token = self.oauth_service.generate_authorization_url(
                user_id=request.user_id,
                tool_name=request.tool_name,
                provider=provider,
                custom_scopes=list(request.scopes) if request.scopes else None,
                custom_redirect_uri=request.redirect_uri if request.redirect_uri else None,
            )

            return authentication_pb2.OAuthAuthorizeResponse(
                success=True,
                message="OAuth authorization URL generated successfully",
                authorization_url=auth_url,
                state=state_token,
            )

        except ValueError as e:
            logger.error(f"OAuth initiation error: {e}")
            return authentication_pb2.OAuthAuthorizeResponse(success=False, message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error in OAuth initiation: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthAuthorizeResponse(
                success=False, message="Internal server error"
            )

    def HandleOAuthCallback(
        self, request: authentication_pb2.OAuthCallbackRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthCallbackResponse:
        """Handle OAuth callback and exchange code for tokens."""
        try:
            # Try to get state_data for redirect_url even in error cases
            redirect_url = ""
            if request.state:
                try:
                    # Get state data from Redis directly
                    from app.utils.redis_service import redis_service

                    redis_key = f"oauth_state:{request.state}"
                    state_data = redis_service.get_json_data_from_redis(redis_key)
                    if state_data:
                        redirect_url = state_data.get("custom_redirect_url", "")
                except Exception:
                    # If we can't get state_data, continue without redirect_url
                    pass

            # Handle OAuth errors
            if request.error:
                logger.error(f"OAuth error received in callback: {request.error}")
                return authentication_pb2.OAuthCallbackResponse(
                    success=False,
                    message=f"Authorization failed: {request.error}",
                    redirect_url=redirect_url,
                )

            if not request.code:
                return authentication_pb2.OAuthCallbackResponse(
                    success=False,
                    message="Authorization code not provided",
                    redirect_url=redirect_url,
                )

            # Exchange code for tokens
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                tokens, state_data = loop.run_until_complete(
                    self.oauth_service.exchange_code_for_tokens(request.code, request.state)
                )
            finally:
                loop.close()

            # Store credentials
            db = self._get_db_session()
            try:
                provider = OAuthProvider(state_data["provider"])

                result = self.oauth_service.store_oauth_credentials(
                    db=db,
                    user_id=state_data["user_id"],
                    tool_name=state_data["tool_name"],
                    provider=provider,
                    tokens=tokens,
                    scopes=state_data["scopes"],
                )

                if result["success"]:
                    return authentication_pb2.OAuthCallbackResponse(
                        success=True,
                        message="OAuth authorization completed successfully",
                        user_id=state_data["user_id"],
                        tool_name=state_data["tool_name"],
                        provider=state_data["provider"],
                        redirect_url=state_data.get("custom_redirect_url", ""),
                    )
                else:
                    return authentication_pb2.OAuthCallbackResponse(
                        success=False,
                        message=f"Failed to store credentials: {result['message']}",
                        redirect_url=state_data.get("custom_redirect_url", ""),
                    )

            finally:
                db.close()

        except ValueError as e:
            logger.error(f"OAuth callback error: {e}")
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message=str(e), redirect_url=""
            )
        except Exception as e:
            logger.error(f"Unexpected error in OAuth callback: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message="Internal server error", redirect_url=""
            )

    # TODO: Add RefreshOAuthTokens method once gRPC messages are properly generated
    # def RefreshOAuthTokens(self, request, context):
    #     """Refresh OAuth access tokens using refresh token."""
    #     context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    #     context.set_details('Method not implemented!')
    #     raise NotImplementedError('Method not implemented!')

    def GetOAuthCredentials(
        self, request: authentication_pb2.OAuthCredentialRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthCredentialResponse:
        """Retrieve OAuth credentials for a user."""
        try:
            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_RAPID_HRMS: OAuthProvider.RAPID_HRMS,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Retrieve credentials
            db = self._get_db_session()
            try:
                credentials = self.oauth_service.retrieve_oauth_credentials(
                    db=db,
                    user_id=request.user_id,
                    tool_name=request.tool_name,
                    provider=provider,
                )

                if credentials:
                    if oauth_provider_manager.is_slack_provider(provider):
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            **credentials,
                        )
                    else:
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            user_id=credentials["user_id"],
                            tool_name=credentials["tool_name"],
                            provider=credentials["provider"],
                            access_token=credentials["access_token"] or "",
                            refresh_token=credentials.get("refresh_token") or "",
                            token_type=credentials["token_type"] or "Bearer",
                            expires_in=credentials["expires_in"],
                            scope=credentials["scope"] or "",
                        )
                else:
                    return authentication_pb2.OAuthCredentialResponse(
                        success=False, message="OAuth credentials not found"
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error retrieving OAuth credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCredentialResponse(
                success=False, message="Internal server error"
            )

    def GetServerOAuthCredentials(
        self,
        request: authentication_pb2.ServerOAuthCredentialRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.OAuthCredentialResponse:
        """Retrieve OAuth credentials for server-to-server access."""
        try:
            # Validate server authentication key
            if not self._validate_server_auth_key(request.server_auth_key):
                context.set_code(grpc.StatusCode.UNAUTHENTICATED)
                context.set_details("Invalid server authentication key")
                return authentication_pb2.OAuthCredentialResponse(
                    success=False, message="Authentication failed"
                )

            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_RAPID_HRMS: OAuthProvider.RAPID_HRMS,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Retrieve credentials
            db = self._get_db_session()
            try:
                credentials = self.oauth_service.retrieve_oauth_credentials(
                    db=db,
                    user_id=request.user_id,
                    tool_name=request.tool_name,
                    provider=provider,
                )

                if credentials:
                    if oauth_provider_manager.is_slack_provider(provider):
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            **credentials,
                        )
                    else:
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            user_id=credentials["user_id"],
                            tool_name=credentials["tool_name"],
                            provider=credentials["provider"],
                            access_token=credentials["access_token"] or "",
                            refresh_token=credentials.get("refresh_token") or "",
                            token_type=credentials["token_type"] or "Bearer",
                            expires_in=credentials["expires_in"],
                            scope=credentials["scope"] or "",
                        )
                else:
                    return authentication_pb2.OAuthCredentialResponse(
                        success=False, message="OAuth credentials not found"
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error retrieving server OAuth credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCredentialResponse(
                success=False, message="Internal server error"
            )

    def DeleteOAuthCredentials(
        self,
        request: authentication_pb2.DeleteOAuthCredentialRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.DeleteOAuthCredentialResponse:
        """Delete OAuth credentials."""
        try:
            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_RAPID_HRMS: OAuthProvider.RAPID_HRMS,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Delete credentials
            db = self._get_db_session()
            try:
                success = self.oauth_service.delete_oauth_credentials(
                    db=db,
                    user_id=request.user_id,
                    tool_name=request.tool_name,
                    provider=provider,
                )

                if success:
                    return authentication_pb2.DeleteOAuthCredentialResponse(
                        success=True, message="OAuth credentials deleted successfully"
                    )
                else:
                    return authentication_pb2.DeleteOAuthCredentialResponse(
                        success=False, message="Failed to delete OAuth credentials"
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error deleting OAuth credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteOAuthCredentialResponse(
                success=False, message="Internal server error"
            )

    def ListOAuthProviders(
        self, request: authentication_pb2.OAuthProvidersListRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthProvidersListResponse:
        """List available OAuth providers."""
        try:
            providers = []
            all_providers = oauth_provider_manager.get_all_providers()

            for provider, config in all_providers.items():
                # Get supported tools for this provider
                all_tool_scopes = oauth_provider_manager.get_all_tool_scopes()
                supported_tools = [
                    tool_name
                    for tool_name, mapping in all_tool_scopes.items()
                    if provider in mapping.provider_scopes
                ]

                provider_info = authentication_pb2.OAuthProviderInfo(
                    provider=getattr(
                        authentication_pb2, f"OAUTH_PROVIDER_{provider.value.upper()}"
                    ),
                    name=provider.value,
                    display_name=config.provider.value.title(),
                    supported_tools=supported_tools,
                    is_configured=oauth_provider_manager.is_provider_configured(provider),
                )
                providers.append(provider_info)

            return authentication_pb2.OAuthProvidersListResponse(
                success=True, message="OAuth providers retrieved successfully", providers=providers
            )

        except Exception as e:
            logger.error(f"Error listing OAuth providers: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthProvidersListResponse(
                success=False, message="Internal server error", providers=[]
            )

    def GetToolScopes(
        self, request: authentication_pb2.OAuthToolScopesRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthToolScopesResponse:
        """Get required scopes for a tool and provider."""
        try:
            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_RAPID_HRMS: OAuthProvider.RAPID_HRMS,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Get scopes for the tool
            scopes = oauth_provider_manager.get_tool_scopes(request.tool_name, provider)

            # Get tool mapping for description
            all_tool_scopes = oauth_provider_manager.get_all_tool_scopes()
            tool_mapping = all_tool_scopes.get(request.tool_name)
            description = tool_mapping.description if tool_mapping else ""

            return authentication_pb2.OAuthToolScopesResponse(
                success=True,
                message="Tool scopes retrieved successfully",
                tool_name=request.tool_name,
                provider=getattr(authentication_pb2, f"OAUTH_PROVIDER_{provider.value.upper()}"),
                scopes=scopes,
                description=description,
            )

        except Exception as e:
            logger.error(f"Error getting tool scopes: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthToolScopesResponse(
                success=False,
                message="Internal server error",
                tool_name=request.tool_name,
                provider=request.provider,
                scopes=[],
                description="",
            )

    def HealthCheck(
        self, request: authentication_pb2.HealthCheckRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.HealthCheckResponse:
        """Perform health check."""
        try:
            dependencies = {}

            # Check Redis
            try:
                redis_healthy = redis_service.health_check()
                dependencies["redis"] = "healthy" if redis_healthy else "unhealthy"
            except Exception:
                dependencies["redis"] = "unhealthy"

            # Check Secret Manager
            try:
                sm_healthy = secret_manager.health_check()
                dependencies["secret_manager"] = "healthy" if sm_healthy else "unhealthy"
            except Exception:
                dependencies["secret_manager"] = "unhealthy"

            # Check database
            try:
                db = self._get_db_session()
                db.execute("SELECT 1")
                db.close()
                dependencies["database"] = "healthy"
            except Exception:
                dependencies["database"] = "unhealthy"

            # Overall health
            all_healthy = all(status == "healthy" for status in dependencies.values())

            return authentication_pb2.HealthCheckResponse(
                healthy=all_healthy,
                status="healthy" if all_healthy else "degraded",
                version=settings.APP_VERSION,
                dependencies=dependencies,
            )

        except Exception as e:
            logger.error(f"Error in health check: {e}")
            return authentication_pb2.HealthCheckResponse(
                healthy=False,
                status="unhealthy",
                version=settings.APP_VERSION,
                dependencies={"error": str(e)},
            )

    def CreateIntegration(
        self, request: authentication_pb2.CreateIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.CreateIntegrationResponse:
        """Create a new integration definition."""
        try:
            print(f"[DEBUG] CreateIntegration request received")
            return self.integration_service.CreateIntegration(request=request, context=context)
        except Exception as e:
            logger.error(f"Error creating integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.CreateIntegrationResponse(
                success=False, message="Internal server error"
            )

    def UpdateIntegration(
        self, request: authentication_pb2.UpdateIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.UpdateIntegrationResponse:
        """Update an existing integration definition."""
        try:
            print(f"[DEBUG] UpdateIntegration request received")
            return self.integration_service.UpdateIntegration(request=request, context=context)
        except Exception as e:
            logger.error(f"Error updating integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.UpdateIntegrationResponse(
                success=False, message="Internal server error"
            )

    def DeleteIntegration(
        self, request: authentication_pb2.DeleteIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.DeleteIntegrationResponse:
        """Delete an integration definition."""
        try:
            print(f"[DEBUG] DeleteIntegration request received")
            return self.integration_service.DeleteIntegration(request=request, context=context)
        except Exception as e:
            logger.error(f"Error deleting integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteIntegrationResponse(
                success=False, message="Internal server error"
            )

    def ListIntegrations(
        self, request: authentication_pb2.ListIntegrationsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.ListIntegrationsResponse:
        """List all integration definitions with pagination support."""
        try:
            print(f"[DEBUG] ListIntegrations request received")
            return self.integration_service.ListIntegrations(request=request, context=context)
        except Exception as e:
            logger.error(f"Error listing integrations: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.ListIntegrationsResponse(
                success=False, message="Internal server error"
            )

    def GetIntegration(
        self, request: authentication_pb2.GetIntegrationRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.GetIntegrationResponse:
        """Get a specific integration definition."""
        try:
            print(f"[DEBUG] GetIntegration request received")
            return self.integration_service.GetIntegration(request=request, context=context)
        except Exception as e:
            logger.error(f"Error getting integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.GetIntegrationResponse(
                success=False, message="Internal server error"
            )

    def ListUserIntegrations(
        self, request: authentication_pb2.ListUserIntegrationsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.ListUserIntegrationsResponse:
        """List user's connected integrations."""
        try:
            print(f"[DEBUG] ListUserIntegrations request received")
            return self.user_integration_service.ListUserIntegrations(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error listing user integrations: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.ListUserIntegrationsResponse(
                success=False, message="Internal server error"
            )

    def CheckIntegrationStatus(
        self, request: authentication_pb2.CheckIntegrationStatusRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.CheckIntegrationStatusResponse:
        """Check connection status for multiple integrations for a user."""
        try:
            logger.info(f"[DEBUG] CheckIntegrationStatus request received for user: {request.user_id}")

            if not request.user_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("User ID is required")
                return authentication_pb2.CheckIntegrationStatusResponse(
                    success=False, message="User ID is required"
                )

            if not request.integration_ids:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("At least one integration ID is required")
                return authentication_pb2.CheckIntegrationStatusResponse(
                    success=False, message="At least one integration ID is required"
                )

            db = self._get_db_session()

            try:
                # Import models here to avoid circular imports
                from app.models.integrations import OAuthCredential, IntegrationDefinition

                # Query for connected integrations in a single efficient query
                connected_integrations = db.query(OAuthCredential.integration_definition_id).filter(
                    OAuthCredential.user_id == request.user_id,
                    OAuthCredential.integration_definition_id.in_(request.integration_ids)
                ).all()

                # Create a set of connected integration IDs for fast lookup
                connected_ids = {row.integration_definition_id for row in connected_integrations}

                # Query for integration definitions to get connection types
                integration_definitions = db.query(IntegrationDefinition).filter(
                    IntegrationDefinition.id.in_(request.integration_ids)
                ).all()

                # Create a map of integration_id to connection_type
                integration_type_map = {
                    integration.id: integration.connection_type
                    for integration in integration_definitions
                }

                # Build response with status and type for each requested integration
                integration_statuses = []
                for integration_id in request.integration_ids:
                    # Get connection type from the map, default to UNSPECIFIED if not found
                    connection_type = integration_type_map.get(integration_id)
                    if connection_type is None:
                        proto_connection_type = authentication_pb2.CONNECTION_TYPE_UNSPECIFIED
                    else:
                        # Map database enum to proto enum
                        connection_type_map = {
                            "API_KEY": authentication_pb2.CONNECTION_TYPE_API_KEY,
                            "OAUTH": authentication_pb2.CONNECTION_TYPE_OAUTH,
                        }
                        proto_connection_type = connection_type_map.get(
                            connection_type.name, authentication_pb2.CONNECTION_TYPE_UNSPECIFIED
                        )

                    status_item = authentication_pb2.IntegrationStatusItem(
                        integration_id=integration_id,
                        is_connected=integration_id in connected_ids,
                        integration_type=proto_connection_type
                    )
                    integration_statuses.append(status_item)

                logger.info(f"[DEBUG] Found {len(connected_ids)} connected integrations out of {len(request.integration_ids)} requested")

                return authentication_pb2.CheckIntegrationStatusResponse(
                    success=True,
                    message="Integration statuses retrieved successfully",
                    integration_statuses=integration_statuses
                )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error checking integration status: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.CheckIntegrationStatusResponse(
                success=False, message="Internal server error"
            )

    def InitiateOAuthByIntegration(
        self,
        request: authentication_pb2.InitiateOAuthByIntegrationRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.InitiateOAuthByIntegrationResponse:
        """Initiate OAuth authorization flow for a specific integration."""
        try:
            print(f"[DEBUG] InitiateOAuthByIntegration request received")
            return self.user_integration_service.InitiateOAuthByIntegration(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error initiating OAuth by integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.InitiateOAuthByIntegrationResponse(
                success=False, message="Internal server error"
            )

    def GetOAuthCredentialsByIntegration(
        self,
        request: authentication_pb2.GetOAuthCredentialsByIntegrationRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.GetOAuthCredentialsByIntegrationResponse:
        """Retrieve OAuth credentials for a specific integration."""
        try:
            print(f"[DEBUG] GetOAuthCredentialsByIntegration request received")
            return self.user_integration_service.GetOAuthCredentialsByIntegration(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error getting OAuth credentials by integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.GetOAuthCredentialsByIntegrationResponse(
                success=False, message="Internal server error"
            )

    def DeleteOAuthCredentialsByIntegration(
        self,
        request: authentication_pb2.DeleteOAuthCredentialsByIntegrationRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse:
        """Delete OAuth credentials for a specific integration."""
        try:
            print(f"[DEBUG] DeleteOAuthCredentialsByIntegration request received")
            return self.user_integration_service.DeleteOAuthCredentialsByIntegration(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error deleting OAuth credentials by integration: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteOAuthCredentialsByIntegrationResponse(
                success=False, message="Internal server error"
            )

    def HandleOAuthCallbackNew(
        self, request: authentication_pb2.OAuthCallbackRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthCallbackResponse:
        """Handle OAuth callback and exchange code for tokens."""
        try:
            print(f"[DEBUG] HandleOAuthCallbackNew request received")
            return self.user_integration_service.HandleOAuthCallback(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error handling OAuth callback: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message="Internal server error"
            )
    
    def StoreAPIKeyCredentials(
        self, request: authentication_pb2.StoreAPIKeyCredentialsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.StoreAPIKeyCredentialsResponse:
        """Store API key credentials for a user integration."""
        try:
            print(f"[DEBUG] StoreAPIKeyCredentials request: {request}")
            return self.integration_service.StoreAPIKeyCredentials(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error storing API key credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.StoreAPIKeyCredentialsResponse(
                success=False, message="Internal server error"
            )
    
    def GetAPIKeyCredentials(
        self, request: authentication_pb2.GetAPIKeyCredentialsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.GetAPIKeyCredentialsResponse:
        """Retrieve API key credentials for a user integration."""
        try:
            print(f"[DEBUG] GetAPIKeyCredentials request: {request}")
            return self.integration_service.GetAPIKeyCredentials(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error retrieving API key credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.GetAPIKeyCredentialsResponse(
                success=False, message="Internal server error"
            )  
    
    def UpdateAPIKeyCredentials(
        self, request: authentication_pb2.UpdateAPIKeyCredentialsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.UpdateAPIKeyCredentialsResponse:
        """Update API key credentials for a user integration."""
        try:
            print(f"[DEBUG] UpdateAPIKeyCredentials request: {request}")
            return self.integration_service.UpdateAPIKeyCredentials(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error updating API key credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.UpdateAPIKeyCredentialsResponse(
                success=False, message="Internal server error"
            )
    
    def DeleteAPIKeyCredentials(
        self, request: authentication_pb2.DeleteAPIKeyCredentialsRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.DeleteAPIKeyCredentialsResponse:
        """Delete API key credentials for a user integration."""
        try:
            print(f"[DEBUG] DeleteAPIKeyCredentials request: {request}")
            return self.integration_service.DeleteAPIKeyCredentials(
                request=request, context=context
            )
        except Exception as e:
            logger.error(f"Error deleting API key credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteAPIKeyCredentialsResponse(
                success=False, message="Internal server error"
            )
