"""
API Key Service

This service provides API key credential management functionality
for integrations that use API key authentication.
"""

import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum, OAuthCredential
from app.utils.GSM.google_secret_manager import EncryptionManager

logger = logging.getLogger(__name__)


class APIKeyService:
    """Service for managing API key credentials."""

    def __init__(self):
        """Initialize API key service."""
        self.encryption_manager = EncryptionManager()

    def validate_api_key_schema(self, schema: Any) -> bool:
        """
        Validate API key schema definition.

        Args:
            schema: Schema definition (can be dict with 'configuration' key or list directly)

        Returns:
            True if valid

        Raises:
            ValueError: If schema is invalid
        """
        # Handle both formats: direct list or dict with 'configuration' key
        field_definitions = schema


        if not isinstance(field_definitions, list):
            raise ValueError("Field definitions must be a list")

        for field in field_definitions:
            if not isinstance(field, dict):
                raise ValueError("Each field definition must be a dictionary")

            # Check required fields
            required_fields = ["name", "required", "description"]
            for required_field in required_fields:
                if required_field not in field:
                    raise ValueError(f"Missing required field '{required_field}'")

            # Validate field types
            if not isinstance(field["name"], str) or not field["name"].strip():
                raise ValueError("Field 'name' must be a non-empty string")

            if not isinstance(field["required"], bool):
                raise ValueError("Field 'required' must be a boolean")

            if not isinstance(field["description"], str):
                raise ValueError("Field 'description' must be a string")

        return True

    def validate_credentials_against_schema(
        self, credentials: Dict[str, Any], schema: Any
    ) -> Dict[str, Any]:
        """
        Validate API key credentials against schema definition.

        Args:
            credentials: User-provided credentials
            schema: Schema definition from integration (can be dict or list)

        Returns:
            Dictionary with validation result
        """
        errors = []

        # Extract field definitions from schema
        if isinstance(schema, dict):
            if 'configuration' in schema:
                field_definitions = schema['configuration']
            else:
                # Convert dict format to list format for validation
                field_definitions = []
                for key, value in schema.items():
                    if isinstance(value, dict) and 'required' in value:
                        field_definitions.append({
                            'name': key,
                            'required': value.get('required', False),
                            'description': value.get('description', f'Field {key}')
                        })
                    else:
                        field_definitions.append({
                            'name': key,
                            'required': True,
                            'description': f'Field {key}'
                        })
        elif isinstance(schema, list):
            field_definitions = schema
        else:
            return {"valid": False, "errors": ["Invalid schema format"]}

        # Check required fields
        for field in field_definitions:
            field_name = field["name"]
            is_required = field["required"]

            if is_required and field_name not in credentials:
                errors.append(f"Required field '{field_name}' is missing")

        return {"valid": len(errors) == 0, "errors": errors}

    def store_api_key_credentials(
        self,
        db: Session,
        user_id: str,
        integration_id: str,
        credentials: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Store API key credentials using single user encryption key approach.

        Args:
            db: Database session
            user_id: User ID
            integration_id: Integration definition ID
            credentials: API key credentials

        Returns:
            Dictionary with operation status
        """
        try:
            # Get integration definition to validate schema
            integration = (
                db.query(IntegrationDefinition)
                .filter(IntegrationDefinition.id == integration_id)
                .first()
            )

            if not integration:
                return {"success": False, "message": "Integration not found"}

            if integration.connection_type != ConnectionTypeEnum.API_KEY:
                return {"success": False, "message": "Integration is not an API key type"}

            # Parse schema definition and extract API key configuration
            schema_definition = integration.schema_definition
            if isinstance(schema_definition, dict) and 'api_key_config' in schema_definition:
                # Use api_key_config if present
                api_key_schema = schema_definition['api_key_config']
            else:
                # Fall back to the entire schema_definition for backward compatibility
                api_key_schema = schema_definition

            # Validate credentials against schema
            validation_result = self.validate_credentials_against_schema(
                credentials, api_key_schema
            )

            if not validation_result["valid"]:
                return {
                    "success": False,
                    "message": "Credential validation failed",
                    "errors": validation_result["errors"],
                }

            # Check if credential already exists
            existing_credential = (
                db.query(OAuthCredential)
                .filter(
                    OAuthCredential.user_id == user_id,
                    OAuthCredential.integration_definition_id == integration_id,
                )
                .first()
            )

            # Create or get encryption key for user (following reference implementation pattern)
            secret_id = self.encryption_manager._get_secret_name(user_id)
            try:
                # Try to get existing key
                self.encryption_manager.get_user_encryption_key(user_id)
                logger.info(f"Using existing encryption key for user: {user_id}")
            except ValueError:
                # Create a new key if it doesn't exist
                logger.info(f"Creating new encryption key for user: {user_id}")
                self.encryption_manager.create_and_store_user_key(secret_id)
            
            # Encrypt credentials and store directly in database
            credentials_json = json.dumps(credentials)
            encrypted_credentials = self.encryption_manager.encrypt(credentials_json, user_id)

            if existing_credential:
                # Update existing credential with encrypted data
                existing_credential.secret_reference = encrypted_credentials
                existing_credential.is_connected = True
                existing_credential.updated_at = datetime.now(timezone.utc)
                existing_credential.last_used_at = datetime.now(timezone.utc)

                logger.info(f"Updated API key credential for user {user_id}, integration {integration_id}")
            else:
                # Create new credential with encrypted data stored directly
                new_credential = OAuthCredential(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    integration_definition_id=integration_id,
                    secret_reference=encrypted_credentials,  # Store encrypted credentials directly
                    is_connected=True,
                    scopes=None,  # API keys don't have scopes
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    last_used_at=datetime.now(timezone.utc),
                )

                db.add(new_credential)
                logger.info(f"Created new API key credential for user {user_id}, integration {integration_id}")

            db.commit()

            return {
                "success": True,
                "message": "API key credentials stored successfully",
                "integration_id": integration_id,
            }

        except IntegrityError as e:
            db.rollback()
            logger.error(f"Database integrity error storing API key credentials: {e}")
            return {"success": False, "message": "Database integrity error", "error": str(e)}
        except Exception as e:
            db.rollback()
            logger.error(f"Error storing API key credentials: {e}")
            return {
                "success": False,
                "message": "Failed to store API key credentials",
                "error": str(e),
            }

    def retrieve_api_key_credentials(
        self, db: Session, user_id: str, integration_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve API key credentials from database (following reference implementation pattern).

        Args:
            db: Database session
            user_id: User ID
            integration_id: Integration definition ID

        Returns:
            API key credentials dictionary if found, None otherwise
        """
        try:
            # Find credential in database
            credential = (
                db.query(OAuthCredential)
                .filter(
                    OAuthCredential.user_id == user_id,
                    OAuthCredential.integration_definition_id == integration_id,
                )
                .first()
            )

            if not credential:
                logger.warning(f"No API key credential found for user {user_id}, integration {integration_id}")
                return None

            # Fetch the integration definition to get schema_definition
            integration = (
                db.query(IntegrationDefinition)
                .filter(IntegrationDefinition.id == integration_id)
                .first()
            )

            if not integration:
                logger.error(f"Integration definition not found for integration_id: {integration_id}")
                return None

            # Decrypt credentials directly from database (no GSM access needed)
            try:
                # The secret_reference now contains the encrypted credentials directly
                encrypted_credentials = credential.secret_reference

                # Decrypt credentials using user's encryption key
                credentials_json = self.encryption_manager.decrypt(encrypted_credentials, user_id)
                credentials = json.loads(credentials_json)

            except Exception as e:
                logger.error(
                    f"Failed to decrypt credentials for user {user_id}, integration {integration_id}: {e}"
                )
                return None

            # Update last used timestamp
            credential.last_used_at = datetime.now(timezone.utc)
            db.commit()

            logger.info(f"Retrieved API key credentials for user {user_id}, integration {integration_id}")

            return {
                "user_id": user_id,
                "integration_id": integration_id,
                "credentials": credentials,
                "is_connected": credential.is_connected,
                "last_used_at": credential.last_used_at.isoformat() if credential.last_used_at else None,
                "schema_definition": integration.schema_definition,
            }

        except Exception as e:
            logger.error(f"Error retrieving API key credentials: {e}")
            return None

    def delete_api_key_credentials(
        self, db: Session, user_id: str, integration_id: str
    ) -> bool:
        """
        Delete API key credentials from database only (following reference implementation pattern).

        Args:
            db: Database session
            user_id: User ID
            integration_id: Integration definition ID

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find credential in database
            credential = (
                db.query(OAuthCredential)
                .filter(
                    OAuthCredential.user_id == user_id,
                    OAuthCredential.integration_definition_id == integration_id,
                )
                .first()
            )

            if not credential:
                logger.warning(f"No API key credential found for deletion: user {user_id}, integration {integration_id}")
                return True  # Consider it successful if already deleted

            # Delete from database only (encrypted credentials are stored in secret_reference field)
            # User's encryption key remains in GSM for potential future use
            db.delete(credential)
            db.commit()

            logger.info(f"Deleted API key credential for user {user_id}, integration {integration_id}")

            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting API key credentials: {e}")
            return False

    def list_user_api_key_integrations(self, db: Session, user_id: str) -> List[Dict[str, Any]]:
        """
        List user's API key integrations.

        Args:
            db: Database session
            user_id: User ID

        Returns:
            List of user's API key integrations
        """
        try:
            # Get user's API key credentials
            credentials = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.user_id == user_id)
                .all()
            )

            result = []
            for credential in credentials:
                # Get integration definition
                integration = (
                    db.query(IntegrationDefinition)
                    .filter(
                        IntegrationDefinition.id == credential.integration_definition_id,
                        IntegrationDefinition.connection_type == ConnectionTypeEnum.API_KEY,
                        IntegrationDefinition.is_active == True,
                    )
                    .first()
                )

                if integration:
                    result.append({
                        "integration_id": integration.id,
                        "integration_name": integration.name,
                        "integration_description": integration.description,
                        "is_connected": credential.is_connected,
                        "last_used_at": credential.last_used_at.isoformat() if credential.last_used_at else None,
                        "created_at": credential.created_at.isoformat() if credential.created_at else None,
                    })

            logger.info(f"Listed {len(result)} API key integrations for user {user_id}")
            return result

        except Exception as e:
            logger.error(f"Error listing user API key integrations: {e}")
            return []

    def update_api_key_credentials(
        self,
        db: Session,
        user_id: str,
        integration_id: str,
        credentials: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Update existing API key credentials using new approach.

        Args:
            db: Database session
            user_id: User ID
            integration_id: Integration definition ID
            credentials: Updated API key credentials

        Returns:
            Dictionary with operation status
        """
        try:
            # Find existing credential
            credential = (
                db.query(OAuthCredential)
                .filter(
                    OAuthCredential.user_id == user_id,
                    OAuthCredential.integration_definition_id == integration_id,
                )
                .first()
            )

            if not credential:
                return {"success": False, "message": "API key credential not found"}

            # Get integration definition to validate schema
            integration = (
                db.query(IntegrationDefinition)
                .filter(IntegrationDefinition.id == integration_id)
                .first()
            )

            if not integration:
                return {"success": False, "message": "Integration not found"}

            # Parse schema definition and extract API key configuration
            schema_definition = integration.schema_definition
            if isinstance(schema_definition, dict) and 'api_key_config' in schema_definition:
                # Use api_key_config if present
                api_key_schema = schema_definition['api_key_config']
            else:
                # Fall back to the entire schema_definition for backward compatibility
                api_key_schema = schema_definition

            # Validate credentials against schema
            validation_result = self.validate_credentials_against_schema(
                credentials, api_key_schema
            )

            if not validation_result["valid"]:
                return {
                    "success": False,
                    "message": "Credential validation failed",
                    "errors": validation_result["errors"],
                }

            # Encrypt new credentials and update database directly (no GSM update needed)
            try:
                # Encrypt new credentials
                credentials_json = json.dumps(credentials)
                encrypted_credentials = self.encryption_manager.encrypt(credentials_json, user_id)
                
                # Update database record with new encrypted credentials
                credential.secret_reference = encrypted_credentials
                credential.updated_at = datetime.now(timezone.utc)
                credential.last_used_at = datetime.now(timezone.utc)
                
            except Exception as e:
                logger.error(f"Failed to encrypt credentials: {e}")
                return {"success": False, "message": "Failed to encrypt credentials"}

            db.commit()

            logger.info(f"Updated API key credentials for user {user_id}, integration {integration_id}")

            return {
                "success": True,
                "message": "API key credentials updated successfully",
                "integration_id": integration_id,
            }

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating API key credentials: {e}")
            return {
                "success": False,
                "message": "Failed to update API key credentials",
                "error": str(e),
            }


# Global API key service instance
api_key_service = APIKeyService()