"""
Test Final Integration Schema Implementation

This module tests the final implementation where:
- OAuth integrations use schema_definition (existing approach)
- API key integrations use api_key_fields (new structured approach)
"""

import pytest
import json
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session

from app.services.integration_functions import IntegrationService
from app.services.api_key_service import api_key_service
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum
from app.grpc_ import authentication_pb2


class TestIntegrationSchemaFinal:
    """Test final integration schema implementation."""

    @pytest.fixture
    def integration_service(self):
        """Create integration service instance."""
        return IntegrationService()

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return MagicMock(spec=Session)

    @pytest.fixture
    def oauth_integration(self):
        """Sample OAuth integration with schema_definition."""
        return IntegrationDefinition(
            id="oauth-integration-123",
            name="Google Calendar",
            description="Google Calendar integration",
            connection_type=ConnectionTypeEnum.OAUTH,
            schema_definition={
                "provider": "google",
                "client_id": "google_client_id",
                "client_secret": "google_client_secret",
                "scopes": ["calendar.readonly", "calendar.events"]
            },
            is_active=True
        )

    @pytest.fixture
    def api_key_integration_list_format(self):
        """Sample API key integration with list format schema."""
        return IntegrationDefinition(
            id="api-key-integration-123",
            name="AWS S3",
            description="AWS S3 integration",
            connection_type=ConnectionTypeEnum.API_KEY,
            schema_definition=[
                {"name": "access_key_id", "required": True, "description": "AWS Access Key ID"},
                {"name": "secret_access_key", "required": True, "description": "AWS Secret Access Key"},
                {"name": "region", "required": False, "description": "AWS Region"}
            ],
            is_active=True
        )

    @pytest.fixture
    def api_key_integration_dict_format(self):
        """Sample API key integration with dict format schema."""
        return IntegrationDefinition(
            id="api-key-integration-456",
            name="OpenAI",
            description="OpenAI API integration",
            connection_type=ConnectionTypeEnum.API_KEY,
            schema_definition={
                "configuration": [
                    {"name": "api_key", "required": True, "description": "OpenAI API Key"},
                    {"name": "organization", "required": False, "description": "Organization ID"}
                ]
            },
            is_active=True
        )

    def test_oauth_integration_to_proto(self, integration_service, oauth_integration):
        """Test OAuth integration conversion to proto (uses schema_definition)."""
        proto_integration = integration_service._integration_to_proto(oauth_integration)
        
        # Verify basic fields
        assert proto_integration.id == "oauth-integration-123"
        assert proto_integration.name == "Google Calendar"
        assert proto_integration.connection_type == authentication_pb2.CONNECTION_TYPE_OAUTH
        
        # Verify schema_definition is preserved for OAuth
        schema_def = json.loads(proto_integration.schema_definition)
        assert schema_def["provider"] == "google"
        assert schema_def["client_id"] == "google_client_id"
        assert "scopes" in schema_def
        
        # Verify api_key_fields is empty for OAuth integrations
        assert len(proto_integration.api_key_fields) == 0

    def test_api_key_integration_list_format_to_proto(self, integration_service, api_key_integration_list_format):
        """Test API key integration (list format) conversion to proto."""
        proto_integration = integration_service._integration_to_proto(api_key_integration_list_format)
        
        # Verify basic fields
        assert proto_integration.id == "api-key-integration-123"
        assert proto_integration.name == "AWS S3"
        assert proto_integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY
        
        # Verify schema_definition is still present (for backward compatibility)
        schema_def = json.loads(proto_integration.schema_definition)
        assert isinstance(schema_def, list)
        assert len(schema_def) == 3
        
        # Verify api_key_fields is populated
        assert len(proto_integration.api_key_fields) == 3
        
        # Check first field
        field1 = proto_integration.api_key_fields[0]
        assert field1.name == "access_key_id"
        assert field1.required is True
        assert field1.description == "AWS Access Key ID"
        
        # Check optional field
        field3 = proto_integration.api_key_fields[2]
        assert field3.name == "region"
        assert field3.required is False
        assert field3.description == "AWS Region"

    def test_api_key_integration_dict_format_to_proto(self, integration_service, api_key_integration_dict_format):
        """Test API key integration (dict format) conversion to proto."""
        proto_integration = integration_service._integration_to_proto(api_key_integration_dict_format)
        
        # Verify basic fields
        assert proto_integration.id == "api-key-integration-456"
        assert proto_integration.name == "OpenAI"
        assert proto_integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY
        
        # Verify api_key_fields is populated from dict format
        assert len(proto_integration.api_key_fields) == 2
        
        # Check fields
        field1 = proto_integration.api_key_fields[0]
        assert field1.name == "api_key"
        assert field1.required is True
        assert field1.description == "OpenAI API Key"
        
        field2 = proto_integration.api_key_fields[1]
        assert field2.name == "organization"
        assert field2.required is False
        assert field2.description == "Organization ID"

    def test_api_key_schema_validation_list_format(self, api_key_integration_list_format):
        """Test API key schema validation with list format."""
        # Test valid credentials
        valid_credentials = {
            "access_key_id": "AKIAIOSFODNN7EXAMPLE",
            "secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
            "region": "us-east-1"
        }
        
        result = api_key_service.validate_credentials_against_schema(
            valid_credentials, api_key_integration_list_format.schema_definition
        )
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        
        # Test missing required field
        invalid_credentials = {
            "access_key_id": "AKIAIOSFODNN7EXAMPLE"
            # Missing required secret_access_key
        }
        
        result = api_key_service.validate_credentials_against_schema(
            invalid_credentials, api_key_integration_list_format.schema_definition
        )
        assert result["valid"] is False
        assert "Required field 'secret_access_key' is missing" in result["errors"]

    def test_api_key_schema_validation_dict_format(self, api_key_integration_dict_format):
        """Test API key schema validation with dict format."""
        # Test valid credentials
        valid_credentials = {
            "api_key": "sk-1234567890abcdef",
            "organization": "org-123"
        }
        
        result = api_key_service.validate_credentials_against_schema(
            valid_credentials, api_key_integration_dict_format.schema_definition
        )
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        
        # Test missing required field
        invalid_credentials = {
            "organization": "org-123"
            # Missing required api_key
        }
        
        result = api_key_service.validate_credentials_against_schema(
            invalid_credentials, api_key_integration_dict_format.schema_definition
        )
        assert result["valid"] is False
        assert "Required field 'api_key' is missing" in result["errors"]

    def test_backward_compatibility_legacy_dict_format(self, integration_service):
        """Test backward compatibility with legacy dictionary format."""
        legacy_integration = IntegrationDefinition(
            id="legacy-integration-789",
            name="Legacy Service",
            description="Legacy service integration",
            connection_type=ConnectionTypeEnum.API_KEY,
            schema_definition={
                "api_key": {"required": True, "description": "API Key"},
                "endpoint": {"required": False, "description": "Custom endpoint"}
            },
            is_active=True
        )
        
        proto_integration = integration_service._integration_to_proto(legacy_integration)
        
        # Verify api_key_fields is populated from legacy format
        assert len(proto_integration.api_key_fields) == 2
        
        # Check converted fields
        field1 = proto_integration.api_key_fields[0]
        assert field1.name == "api_key"
        assert field1.required is True
        assert field1.description == "API Key"
        
        field2 = proto_integration.api_key_fields[1]
        assert field2.name == "endpoint"
        assert field2.required is False
        assert field2.description == "Custom endpoint"

    def test_mixed_integration_types_handling(self, integration_service, oauth_integration, api_key_integration_list_format):
        """Test handling of mixed integration types."""
        # Test OAuth integration
        oauth_proto = integration_service._integration_to_proto(oauth_integration)
        assert oauth_proto.connection_type == authentication_pb2.CONNECTION_TYPE_OAUTH
        assert len(oauth_proto.api_key_fields) == 0  # No API key fields for OAuth
        
        # Test API key integration
        api_key_proto = integration_service._integration_to_proto(api_key_integration_list_format)
        assert api_key_proto.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY
        assert len(api_key_proto.api_key_fields) == 3  # Has API key fields
        
        # Verify schema_definition is preserved for both
        oauth_schema = json.loads(oauth_proto.schema_definition)
        assert "provider" in oauth_schema
        
        api_key_schema = json.loads(api_key_proto.schema_definition)
        assert isinstance(api_key_schema, list)

    def test_empty_api_key_schema(self, integration_service):
        """Test handling of empty API key schema."""
        empty_integration = IntegrationDefinition(
            id="empty-integration",
            name="Empty Integration",
            description="Integration with empty schema",
            connection_type=ConnectionTypeEnum.API_KEY,
            schema_definition=[],
            is_active=True
        )
        
        proto_integration = integration_service._integration_to_proto(empty_integration)
        
        # Should handle empty schema gracefully
        assert len(proto_integration.api_key_fields) == 0
        assert proto_integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY

    def test_complex_api_key_schema(self, integration_service):
        """Test complex API key schema with multiple fields."""
        complex_integration = IntegrationDefinition(
            id="complex-integration",
            name="Complex Service",
            description="Service with complex API key requirements",
            connection_type=ConnectionTypeEnum.API_KEY,
            schema_definition=[
                {"name": "primary_key", "required": True, "description": "Primary API key"},
                {"name": "secondary_key", "required": False, "description": "Secondary API key"},
                {"name": "region", "required": True, "description": "Service region"},
                {"name": "environment", "required": False, "description": "Environment (dev/prod)"},
                {"name": "timeout", "required": False, "description": "Request timeout in seconds"},
                {"name": "retry_count", "required": False, "description": "Number of retries"}
            ],
            is_active=True
        )
        
        proto_integration = integration_service._integration_to_proto(complex_integration)
        
        # Verify all fields are converted
        assert len(proto_integration.api_key_fields) == 6
        
        # Check required fields
        required_fields = [field for field in proto_integration.api_key_fields if field.required]
        assert len(required_fields) == 2
        assert required_fields[0].name == "primary_key"
        assert required_fields[1].name == "region"
        
        # Check optional fields
        optional_fields = [field for field in proto_integration.api_key_fields if not field.required]
        assert len(optional_fields) == 4


if __name__ == "__main__":
    pytest.main([__file__])