"""
Test API Key Schema Update

This module tests the updated API key schema functionality that supports
multiple API key field definitions in a list format.
"""

import pytest
import json
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session

from app.services.api_key_service import api_key_service
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum


class TestAPIKeySchemaUpdate:
    """Test updated API key schema functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return MagicMock(spec=Session)

    @pytest.fixture
    def sample_api_key_schema_list(self):
        """Sample API key schema in new list format."""
        return [
            {"name": "api_key", "required": True, "description": "Your API secret key"},
            {"name": "region", "required": False, "description": "AWS Region"},
            {"name": "endpoint", "required": False, "description": "Custom endpoint URL"}
        ]

    @pytest.fixture
    def sample_api_key_schema_dict(self):
        """Sample API key schema in old dict format for backward compatibility."""
        return {
            "configuration": [
                {"name": "api_key", "required": True, "description": "Your API secret key"},
                {"name": "region", "required": False, "description": "AWS Region"}
            ]
        }

    @pytest.fixture
    def sample_credentials_complete(self):
        """Sample credentials with all required fields."""
        return {
            "api_key": "sk-1234567890abcdef",
            "region": "us-east-1",
            "endpoint": "https://api.custom.com"
        }

    @pytest.fixture
    def sample_credentials_minimal(self):
        """Sample credentials with only required fields."""
        return {
            "api_key": "sk-1234567890abcdef"
        }

    def test_validate_api_key_schema_list_format(self, sample_api_key_schema_list):
        """Test validation of API key schema in list format."""
        # Should validate successfully
        result = api_key_service.validate_api_key_schema(sample_api_key_schema_list)
        assert result is True

    def test_validate_api_key_schema_dict_format(self, sample_api_key_schema_dict):
        """Test validation of API key schema in dict format."""
        # Should validate successfully
        result = api_key_service.validate_api_key_schema(sample_api_key_schema_dict)
        assert result is True

    def test_validate_api_key_schema_invalid_format(self):
        """Test validation with invalid schema format."""
        with pytest.raises(ValueError, match="Schema must be a list of field definitions or a dictionary"):
            api_key_service.validate_api_key_schema("invalid_format")

    def test_validate_api_key_schema_missing_required_fields(self):
        """Test validation with missing required fields in schema."""
        invalid_schema = [
            {"name": "api_key", "description": "API key"}  # Missing 'required' field
        ]
        
        with pytest.raises(ValueError, match="Missing required field 'required'"):
            api_key_service.validate_api_key_schema(invalid_schema)

    def test_validate_api_key_schema_invalid_field_types(self):
        """Test validation with invalid field types."""
        invalid_schema = [
            {"name": "", "required": True, "description": "Empty name"}  # Empty name
        ]
        
        with pytest.raises(ValueError, match="Field 'name' must be a non-empty string"):
            api_key_service.validate_api_key_schema(invalid_schema)

    def test_validate_credentials_against_schema_list_format_complete(
        self, sample_api_key_schema_list, sample_credentials_complete
    ):
        """Test credential validation against list format schema with complete credentials."""
        result = api_key_service.validate_credentials_against_schema(
            sample_credentials_complete, sample_api_key_schema_list
        )
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0

    def test_validate_credentials_against_schema_list_format_minimal(
        self, sample_api_key_schema_list, sample_credentials_minimal
    ):
        """Test credential validation against list format schema with minimal credentials."""
        result = api_key_service.validate_credentials_against_schema(
            sample_credentials_minimal, sample_api_key_schema_list
        )
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0

    def test_validate_credentials_against_schema_missing_required(
        self, sample_api_key_schema_list
    ):
        """Test credential validation with missing required field."""
        incomplete_credentials = {
            "region": "us-east-1"  # Missing required 'api_key'
        }
        
        result = api_key_service.validate_credentials_against_schema(
            incomplete_credentials, sample_api_key_schema_list
        )
        
        assert result["valid"] is False
        assert "Required field 'api_key' is missing" in result["errors"]

    def test_validate_credentials_against_schema_dict_format(
        self, sample_api_key_schema_dict, sample_credentials_minimal
    ):
        """Test credential validation against dict format schema."""
        result = api_key_service.validate_credentials_against_schema(
            sample_credentials_minimal, sample_api_key_schema_dict
        )
        
        assert result["valid"] is True
        assert len(result["errors"]) == 0

    def test_backward_compatibility_old_dict_schema(self):
        """Test backward compatibility with old dictionary schema format."""
        old_schema = {
            "api_key": {"required": True, "description": "API Key"},
            "secret": {"required": False, "description": "Secret"}
        }
        
        credentials = {"api_key": "test_key"}
        
        result = api_key_service.validate_credentials_against_schema(credentials, old_schema)
        assert result["valid"] is True

    @patch('app.services.api_key_service.api_key_service.encryption_manager')
    def test_store_api_key_credentials_with_new_schema(
        self, mock_encryption_manager, mock_db_session, sample_api_key_schema_list, sample_credentials_complete
    ):
        """Test storing API key credentials with new schema format."""
        # Mock integration
        mock_integration = MagicMock()
        mock_integration.connection_type = ConnectionTypeEnum.API_KEY
        mock_integration.schema_definition = sample_api_key_schema_list
        
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_integration
        
        # Mock encryption manager
        mock_encryption_manager.get_user_encryption_key.return_value = "test_key"
        mock_encryption_manager.encrypt.return_value = "encrypted_credentials"
        mock_encryption_manager.create_and_store_user_key.return_value = None
        mock_encryption_manager.project_id = "test_project"
        mock_encryption_manager.secret_client = MagicMock()
        
        # Test storing credentials
        result = api_key_service.store_api_key_credentials(
            mock_db_session, "user123", "integration123", sample_credentials_complete
        )
        
        assert result["success"] is True
        assert result["message"] == "API key credentials stored successfully"
        assert result["integration_id"] == "integration123"

    def test_complex_api_key_schema_validation(self):
        """Test validation with complex API key schema."""
        complex_schema = [
            {"name": "primary_key", "required": True, "description": "Primary API key"},
            {"name": "secondary_key", "required": False, "description": "Secondary API key"},
            {"name": "region", "required": True, "description": "Service region"},
            {"name": "environment", "required": False, "description": "Environment (dev/prod)"},
            {"name": "timeout", "required": False, "description": "Request timeout in seconds"}
        ]
        
        # Test with all fields
        complete_credentials = {
            "primary_key": "pk_123",
            "secondary_key": "sk_456", 
            "region": "us-west-2",
            "environment": "prod",
            "timeout": "30"
        }
        
        result = api_key_service.validate_credentials_against_schema(complete_credentials, complex_schema)
        assert result["valid"] is True
        
        # Test with only required fields
        minimal_credentials = {
            "primary_key": "pk_123",
            "region": "us-west-2"
        }
        
        result = api_key_service.validate_credentials_against_schema(minimal_credentials, complex_schema)
        assert result["valid"] is True
        
        # Test with missing required field
        incomplete_credentials = {
            "primary_key": "pk_123"
            # Missing required 'region'
        }
        
        result = api_key_service.validate_credentials_against_schema(incomplete_credentials, complex_schema)
        assert result["valid"] is False
        assert "Required field 'region' is missing" in result["errors"]

    def test_empty_schema_validation(self):
        """Test validation with empty schema."""
        empty_schema = []
        credentials = {"any_key": "any_value"}
        
        result = api_key_service.validate_credentials_against_schema(credentials, empty_schema)
        assert result["valid"] is True  # No required fields, so any credentials are valid

    def test_schema_with_special_characters(self):
        """Test schema validation with field names containing special characters."""
        special_schema = [
            {"name": "api-key", "required": True, "description": "API key with dash"},
            {"name": "client_id", "required": True, "description": "Client ID with underscore"},
            {"name": "oauth.token", "required": False, "description": "Token with dot"}
        ]
        
        credentials = {
            "api-key": "test_key",
            "client_id": "client123",
            "oauth.token": "token456"
        }
        
        result = api_key_service.validate_credentials_against_schema(credentials, special_schema)
        assert result["valid"] is True


if __name__ == "__main__":
    pytest.main([__file__])