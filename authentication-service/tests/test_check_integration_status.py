#!/usr/bin/env python3
"""
Test for the CheckIntegrationStatus functionality.
"""

import pytest
import uuid
import grpc
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from app.services.auth_service import AuthenticationServicer
from app.grpc_ import authentication_pb2
from app.models.integrations import OAuthCredential


class TestCheckIntegrationStatus:
    """Test cases for CheckIntegrationStatus method."""

    def setup_method(self):
        """Set up test fixtures."""
        self.auth_servicer = AuthenticationServicer()
        self.mock_context = Mock()
        
        # Sample test data
        self.test_user_id = "test_user_123"
        self.test_integration_ids = [
            "integration_1",
            "integration_2", 
            "integration_3"
        ]
        
        # Mock connected integrations (only integration_1 and integration_3 are connected)
        self.mock_connected_integrations = [
            Mock(integration_definition_id="integration_1"),
            Mock(integration_definition_id="integration_3")
        ]

    @patch('app.services.auth_service.AuthenticationServicer._get_db_session')
    def test_check_integration_status_success(self, mock_get_db):
        """Test successful integration status check."""
        # Arrange
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        
        # Mock the query chain
        mock_query = Mock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = self.mock_connected_integrations
        
        request = authentication_pb2.CheckIntegrationStatusRequest(
            user_id=self.test_user_id,
            integration_ids=self.test_integration_ids
        )

        # Act
        response = self.auth_servicer.CheckIntegrationStatus(request, self.mock_context)

        # Assert
        assert response.success is True
        assert response.message == "Integration statuses retrieved successfully"
        assert len(response.integration_statuses) == 3
        
        # Check individual statuses
        status_dict = {item.integration_id: item.is_connected for item in response.integration_statuses}
        assert status_dict["integration_1"] is True
        assert status_dict["integration_2"] is False  # Not connected
        assert status_dict["integration_3"] is True
        
        # Verify database was queried correctly
        mock_db.query.assert_called_once()
        mock_db.close.assert_called_once()

    @patch('app.services.auth_service.AuthenticationServicer._get_db_session')
    def test_check_integration_status_no_connected_integrations(self, mock_get_db):
        """Test when user has no connected integrations."""
        # Arrange
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        
        # Mock empty result
        mock_query = Mock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        
        request = authentication_pb2.CheckIntegrationStatusRequest(
            user_id=self.test_user_id,
            integration_ids=self.test_integration_ids
        )

        # Act
        response = self.auth_servicer.CheckIntegrationStatus(request, self.mock_context)

        # Assert
        assert response.success is True
        assert len(response.integration_statuses) == 3
        
        # All should be disconnected
        for status_item in response.integration_statuses:
            assert status_item.is_connected is False

    def test_check_integration_status_missing_user_id(self):
        """Test error handling when user_id is missing."""
        # Arrange
        request = authentication_pb2.CheckIntegrationStatusRequest(
            user_id="",  # Empty user_id
            integration_ids=self.test_integration_ids
        )

        # Act
        response = self.auth_servicer.CheckIntegrationStatus(request, self.mock_context)

        # Assert
        assert response.success is False
        assert response.message == "User ID is required"
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.INVALID_ARGUMENT)

    def test_check_integration_status_missing_integration_ids(self):
        """Test error handling when integration_ids is empty."""
        # Arrange
        request = authentication_pb2.CheckIntegrationStatusRequest(
            user_id=self.test_user_id,
            integration_ids=[]  # Empty list
        )

        # Act
        response = self.auth_servicer.CheckIntegrationStatus(request, self.mock_context)

        # Assert
        assert response.success is False
        assert response.message == "At least one integration ID is required"
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.INVALID_ARGUMENT)

    @patch('app.services.auth_service.AuthenticationServicer._get_db_session')
    def test_check_integration_status_database_error(self, mock_get_db):
        """Test error handling when database query fails."""
        # Arrange
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_db.query.side_effect = Exception("Database connection failed")
        
        request = authentication_pb2.CheckIntegrationStatusRequest(
            user_id=self.test_user_id,
            integration_ids=self.test_integration_ids
        )

        # Act
        response = self.auth_servicer.CheckIntegrationStatus(request, self.mock_context)

        # Assert
        assert response.success is False
        assert response.message == "Internal server error"
        self.mock_context.set_code.assert_called_with(grpc.StatusCode.INTERNAL)
        mock_db.close.assert_called_once()

    @patch('app.services.auth_service.AuthenticationServicer._get_db_session')
    def test_check_integration_status_single_integration(self, mock_get_db):
        """Test with single integration ID."""
        # Arrange
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        
        mock_query = Mock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [Mock(integration_definition_id="integration_1")]
        
        request = authentication_pb2.CheckIntegrationStatusRequest(
            user_id=self.test_user_id,
            integration_ids=["integration_1"]
        )

        # Act
        response = self.auth_servicer.CheckIntegrationStatus(request, self.mock_context)

        # Assert
        assert response.success is True
        assert len(response.integration_statuses) == 1
        assert response.integration_statuses[0].integration_id == "integration_1"
        assert response.integration_statuses[0].is_connected is True


if __name__ == "__main__":
    pytest.main([__file__])
