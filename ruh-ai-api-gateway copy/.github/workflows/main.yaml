name: Build and Deploy Application
on:
  push:
    branches:
      - dev
      - main
  workflow_dispatch:
jobs:
  build:
    uses: ruh-ai/reusable-workflows-and-charts/.github/workflows/reusable-build.yaml@workflow/v1.0.0
    with:
      gcp_repository: "ruh-ai"
      gcp_artifact_registry_host: "us-central1-docker.pkg.dev"
      slack_channel: "ruh-cicd"
    secrets: inherit
  deploy:
    needs: build
    uses: ruh-ai/reusable-workflows-and-charts/.github/workflows/reusable-deploy-gke.yaml@workflow/v1.0.0
    with:
      namespace: "ruh-ai-${{ github.ref_name }}"
      values_file: values.${{ github.ref_name }}.yaml
      chart_oci_path: "oci://us-central1-docker.pkg.dev/arcadia-2-450608/ruh-helm/ruh-ai"
      chart_version: "1.0.0"
      gcp_artifact_registry_host: "us-central1-docker.pkg.dev"
      slack_channel: "ruh-cicd"
    secrets: inherit
