from fastapi import APIRouter, Depends, HTTPException
from app.core.auth_guard import role_required
from app.services.user_service import UserServiceClient
from pydantic import BaseModel
from typing import Optional
from app.utils.parse_error import parse_error

otp_router = APIRouter(prefix="/otp", tags=["otp"])
user_service = UserServiceClient()


# Simple schemas for OTP endpoints
class SendOTPRequest(BaseModel):
    otp_type: Optional[str] = "verify_mobile"  # verify_mobile, forgot_password


class SendOTPResponse(BaseModel):
    success: bool
    message: str


class VerifyOTPRequest(BaseModel):
    otp: str


class VerifyOTPResponse(BaseModel):
    success: bool
    message: str
    verified: bool = False


@otp_router.post("/send", response_model=SendOTPResponse)
async def send_otp(
    request: SendOTPRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Send OTP to user's registered phone number.
    Only requires user_id (from auth) and optional otp_type.
    """
    try:
        # Call user service to send OTP
        response = await user_service.send_otp(
            user_id=current_user['user_id'],
            otp_type=request.otp_type
        )

        return SendOTPResponse(
            success=response.success,
            message=response.message
        )

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@otp_router.post("/verify", response_model=VerifyOTPResponse)
async def verify_otp(
    request: VerifyOTPRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Verify OTP code for phone number verification.
    Only requires OTP code - user_id comes from auth.
    """
    try:
        # Call user service to verify OTP
        response = await user_service.verify_otp(
            user_id=current_user['user_id'],
            otp=request.otp
        )

        return VerifyOTPResponse(
            success=response.success,
            message=response.message,
            verified=getattr(response, 'verified', False)
        )

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
