from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query

from app.services.mcp_service import MCPServiceClient
from app.services.authentication_service import get_auth_service_client
from app.schemas.mcp import (
    DeploymentStatus,
    DeploymentUpdatePayload,
    DeploymentUpdateResponse,

    MCPPatchPayload,
    MCPsByIdsRequest,
    MCPsByIdsResponse,
    McpComponentCategory,
    PaginatedMCPResponse,
    PaginationMetadata,
    StatusEnum,
    ToggleMcpVisibilityResponseAPI,
    ToolOutputSchemaUpdatePayload,
    UpdateMCPResponse,
    MCPResponse,
    MCPCreate,
    MCPInDB,
    CreateMCPResponse,
    DeleteMCPResponse,
    VisibilityEnum,
    McpCategory,
    MCPUrlsRequest,
    MCPUrlItem,
    MCPUrlsResponse,
    # Container Schemas
    CreateContainerRequest,
    CreateContainerResponse,
    StopContainerResponse,
    DeleteContainerResponse,
    EnvKeyValue,
    # New API Schemas
    GetMCPByRepoRequest,
    GetMCPByRepoResponse,
    GetMCPDeploymentStatusRequest,
    GetMCPDeploymentStatusResponse,
    MCPDeploymentInfo,
)
from enum import Enum
from typing import Optional

from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.core.security import (
    validate_server_auth_key,
    validate_agent_platform_auth_key,
    validate_workflow_service_auth_key,
    validate_mcp_proxy_server_auth_key,
)
from app.services.user_service import UserServiceClient
from app.utils.parse_error import parse_error
import json

mcp_router = APIRouter(prefix="/mcps", tags=["mcps"])
mcp_marketplace = APIRouter(prefix="/marketplace", tags=["mcps"])
mcp_service = MCPServiceClient()
user_service = UserServiceClient()


@mcp_router.post("", response_model=CreateMCPResponse)
async def create_mcp(
    mcp_data: MCPCreate, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Creates a new MCP.
    """
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]
        # Initialize github_access_token to None
        github_access_token = None

        # Handle Git repository creation workflow
        if mcp_data.hosted_url is None:
            github_access_token = await user_service.get_user_github_token(current_user["user_id"])
            if not github_access_token:
                raise HTTPException(
                    status_code=400,
                    detail="GitHub token not found. Please authenticate with GitHub first.",
                )
            mcp_data.github_access_token = github_access_token

        response = await mcp_service.createMCP(
            logo=mcp_data.logo,
            name=mcp_data.name,
            description=mcp_data.description,
            owner_details=user_details,
            owner_type=current_user["role"],
            git_url=mcp_data.git_url,
            hosted_url=mcp_data.hosted_url,
            mcp_type=mcp_data.mcp_type.value,
            github_access_token=github_access_token,
            git_branch=mcp_data.git_branch,
            category=mcp_data.category,
            visibility=mcp_data.visibility,
            tags=mcp_data.tags,
            status=mcp_data.status,
            user_ids=mcp_data.user_ids,
            component_category=(
                mcp_data.component_category.value if mcp_data.component_category else None
            ),
            repo_name=mcp_data.repo_name,
            git_user_name=mcp_data.git_user_name,
            integrations=mcp_data.integrations,
        )

        return CreateMCPResponse(
            success=response.success,
            message=response.message,
            mcp_id=response.mcp.id if hasattr(response, "mcp") and response.mcp else None,
        )
    except Exception as e:
        print(f"[DEBUG] Error in create_mcp: {str(e)}")
        error_details = parse_error(str(e))  # Your existing error parsing
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("/{mcp_id}", response_model=MCPResponse)
async def get_mcp(mcp_id: str, current_user: dict = Depends(role_required(["user", "admin"]))):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id, current_user["user_id"])

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)
        mcp_obj = MCPInDB(**mcp_dict)

        # Check integration connection status
        if mcp_obj.integrations and current_user.get("user_id"):
            try:
                user_id = current_user.get("user_id")

                # Call our integration status check endpoint
                auth_service = get_auth_service_client()
                status_response = await auth_service.check_integration_status(
                    user_id=user_id,
                    integration_ids=mcp_obj.integrations
                )

                if status_response.get("success"):
                    # Check if any of the MCP's integrations are connected and get integration type
                    integration_statuses = status_response.get("integration_statuses", [])
                    mcp_obj.is_connected = any(
                        status_item["is_connected"]
                        for status_item in integration_statuses
                    )

                    # Set integration_type from the first integration (assuming single integration per MCP)
                    if integration_statuses:
                        mcp_obj.integration_type = integration_statuses[0].get("integration_type")
                else:
                    mcp_obj.is_connected = False
                    mcp_obj.integration_type = None

            except Exception as e:
                print(f"[DEBUG] Error checking integration status for single MCP: {e}")
                mcp_obj.is_connected = False
                mcp_obj.integration_type = None
        else:
            # If no integrations or no user context, user is not connected and no integration type
            mcp_obj.is_connected = False
            mcp_obj.integration_type = None

        return MCPResponse(
            success=response.success,
            message=response.message,
            mcp=mcp_obj,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.patch("/{mcp_id}", response_model=UpdateMCPResponse)
async def update_mcp(
    mcp_id: str,
    payload: MCPPatchPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Partially updates an MCP by ID.
    Only the fields provided in the request body will be updated.
    """
    try:
        validate_response = await user_service.validate_user(
            current_user["user_id"]
        )  # Assuming user_service exists
        if not validate_response.get("success"):
            raise HTTPException(
                status_code=400, detail=validate_response.get("message", "User validation failed")
            )

        user_details = validate_response["user"]

        # Only add github_access_token if we're actually updating Git-related fields
        update_data = payload.model_dump(exclude_unset=True)

        # Check if this is a Git-based update (updating git_url, git_branch, or github_access_token)
        git_fields = {"git_url", "git_branch", "github_access_token"}
        is_git_update = any(field in update_data for field in git_fields)

        # If it's a Git-based update and hosted_url is not provided, we need github_access_token
        if is_git_update and not payload.hosted_url:
            github_access_token = await user_service.get_user_github_token(current_user["user_id"])
            if not github_access_token:
                raise HTTPException(
                    status_code=400,
                    detail="GitHub token not found. Please authenticate with GitHub first.",
                )
            update_data["github_access_token"] = github_access_token

        # if payload.env_keys is not None:
        #     update_data["env_keys"] = [env_key.model_dump() for env_key in payload.env_keys]

        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update.")

        response = await mcp_service.updateMCP(
            mcp_id=mcp_id, update_fields=update_data, current_owner_details=user_details
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)
        return UpdateMCPResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in patch_mcp: {str(e)}")
        error_details = parse_error(str(e))  # Your existing error parser
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected error occurred."),
        )


@mcp_router.delete("/{mcp_id}", response_model=DeleteMCPResponse)
async def delete_mcp(mcp_id: str, current_user: dict = Depends(role_required(["user", "admin"]))):
    """
    Deletes an MCP by ID.

    This endpoint deletes an MCP by its ID.
    """
    try:
        user_id = current_user["user_id"]
        response = await mcp_service.deleteMCP(mcp_id, user_id)

        return DeleteMCPResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("", response_model=PaginatedMCPResponse, description="get MCPs for admin & user")
async def list_mcps(
    current_user: dict = Depends(role_required(["user", "admin"])),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    category: Optional[McpCategory] = Query(None, description="Filter by category"),
    visibility: Optional[VisibilityEnum] = Query(None, description="Filter by visibility"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
    status: Optional[StatusEnum] = Query(None, description="Filter by status"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    deployment_status: Optional[DeploymentStatus] = Query(
        None, description="Filter by deployment status"
    ),
    component_category: Optional[McpComponentCategory] = Query(
        None, description="Filter by component category"
    ),
    quick_tools_only: Optional[bool] = Query(
        False, description="Filter to show only quick tools for the current user"
    ),
):
    """
    Retrieves a paginated list of MCPs, with optional filters.
    - Admins can see all MCPs (subject to filters).
    - Users can only see their own MCPs (subject to filters).
    """
    try:
        owner_id_filter: Optional[str] = None
        if current_user.get("role") == "user":
            owner_id_filter = current_user.get("user_id")
            if not owner_id_filter:
                raise HTTPException(status_code=403, detail="User ID not found for user role.")

        # The gRPC client expects Python enums (or their .value) which it maps to proto enums
        response = await mcp_service.listMCPS(
            page=page,
            page_size=page_size,
            owner_id=owner_id_filter,
            category=category.value if category else None,
            visibility=visibility,
            status=status,
            tags=tags,
            deployment_status=deployment_status,
            search=search,
            component_category=component_category.value if component_category else None,
            quick_tools_only=quick_tools_only,
        )

        mcps = []
        all_integration_ids = set()

        # Single pass: build MCPs and collect integration IDs
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            mcp_obj = MCPInDB.model_validate(mcp_dict)
            mcps.append(mcp_obj)

            # Collect integration IDs if they exist
            if mcp_obj.integrations:
                all_integration_ids.update(mcp_obj.integrations)

        # Check integration status if user context exists
        if current_user.get("user_id") and all_integration_ids:
            # Single gRPC call for all integrations
            try:
                user_id = current_user["user_id"]
                auth_service = get_auth_service_client()
                status_response = await auth_service.check_integration_status(
                    user_id=user_id,
                    integration_ids=list(all_integration_ids)
                )

                # Fast dict comprehension for status and type lookup
                integration_status_map = {}
                integration_type_map = {}
                if status_response.get("success"):
                    for item in status_response.get("integration_statuses", []):
                        integration_status_map[item["integration_id"]] = item["is_connected"]
                        integration_type_map[item["integration_id"]] = item.get("integration_type")

                # Update MCPs with connection status and integration types
                for mcp in mcps:
                    if mcp.integrations:
                        # Set integration_type from the first integration (assuming single integration per MCP)
                        first_integration_id = mcp.integrations[0]
                        mcp.integration_type = integration_type_map.get(first_integration_id)

                        # Check if any of the MCP's integrations are connected
                        mcp.is_connected = any(
                            integration_status_map.get(integration_id, False)
                            for integration_id in mcp.integrations
                        )
                    else:
                        # No integrations, user is not connected and no integration type
                        mcp.is_connected = False
                        mcp.integration_type = None

            except Exception as e:
                print(f"[DEBUG] Error checking integration status: {e}")
                # Graceful fallback - set all to False/None
                for mcp in mcps:
                    mcp.is_connected = False
                    mcp.integration_type = None
        else:
            # No user context or no integrations to check
            for mcp in mcps:
                mcp.is_connected = False
                mcp.integration_type = None

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=response.total,
            totalPages=response.total_pages,
            currentPage=response.page,
            pageSize=page_size,
            hasNextPage=response.page < response.total_pages,
            hasPreviousPage=response.page > 1,
        )

        return PaginatedMCPResponse(data=mcps, metadata=metadata)
    except Exception as e:
        print(f"[DEBUG] Error details: {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/agent-platform/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_mcp_for_agent_platform(
    mcp_id: str,
):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/orchestration/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_mcp_for_orchestration(mcp_id: str):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/workflow-service/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_workflow_service_auth_key)],
)
async def get_mcp_for_workflow_service(mcp_id: str):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post("/by-ids", response_model=MCPsByIdsResponse)
async def get_mcps_by_ids(
    request: MCPsByIdsRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Retrieves multiple MCPs by their IDs.

    This endpoint allows fetching multiple MCPs in a single request by providing a list of MCP IDs.

    ## Request Body
    - **ids**: List of MCP IDs to retrieve

    ## Response
    Returns a list of MCPs matching the provided IDs and the total count.

    ## Example
    ```
    POST /mcps/by-ids
    {
        "ids": ["mcp-id-1", "mcp-id-2", "mcp-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")

        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)

        mcps = []
        all_integration_ids = set()

        # Single pass: build MCPs and collect integration IDs
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            mcp_obj = MCPInDB.model_validate(mcp_dict)
            mcps.append(mcp_obj)

            # Collect integration IDs if they exist
            if mcp_obj.integrations:
                all_integration_ids.update(mcp_obj.integrations)

        # Check integration status if user context exists
        if current_user.get("user_id") and all_integration_ids:
            # Single gRPC call for all integrations
            try:
                user_id = current_user["user_id"]
                auth_service = get_auth_service_client()
                status_response = await auth_service.check_integration_status(
                    user_id=user_id,
                    integration_ids=list(all_integration_ids)
                )

                # Fast dict comprehension for status and type lookup
                integration_status_map = {}
                integration_type_map = {}
                if status_response.get("success"):
                    for item in status_response.get("integration_statuses", []):
                        integration_status_map[item["integration_id"]] = item["is_connected"]
                        integration_type_map[item["integration_id"]] = item.get("integration_type")

                # Update MCPs with connection status and integration types
                for mcp in mcps:
                    if mcp.integrations:
                        # Set integration_type from the first integration (assuming single integration per MCP)
                        first_integration_id = mcp.integrations[0]
                        mcp.integration_type = integration_type_map.get(first_integration_id)

                        # Check if any of the MCP's integrations are connected
                        mcp.is_connected = any(
                            integration_status_map.get(integration_id, False)
                            for integration_id in mcp.integrations
                        )
                    else:
                        # No integrations, user is not connected and no integration type
                        mcp.is_connected = False
                        mcp.integration_type = None

            except Exception as e:
                print(f"[DEBUG] Error checking integration status in get_mcps_by_ids: {e}")
                # Graceful fallback - set all to False/None
                for mcp in mcps:
                    mcp.is_connected = False
                    mcp.integration_type = None
        else:
            # No user context or no integrations to check
            for mcp in mcps:
                mcp.is_connected = False
                mcp.integration_type = None

        return MCPsByIdsResponse(
            success=True, message=f"Retrieved {len(mcps)} MCPs", mcps=mcps, total=len(mcps)
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_mcps_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post(
    "/agent-platform/by-ids",
    response_model=MCPsByIdsResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_mcps_by_ids_for_agent_platform(request: MCPsByIdsRequest):
    """
    Retrieves multiple MCPs by their IDs.

    This endpoint allows fetching multiple MCPs in a single request by providing a list of MCP IDs.

    ## Request Body
    - **ids**: List of MCP IDs to retrieve

    ## Response
    Returns a list of MCPs matching the provided IDs and the total count.

    ## Example
    ```
    POST /mcps/by-ids
    {
        "ids": ["mcp-id-1", "mcp-id-2", "mcp-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")

        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)

        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            mcps.append(MCPInDB.model_validate(mcp_dict))

        return MCPsByIdsResponse(
            success=True, message=f"Retrieved {len(mcps)} MCPs", mcps=mcps, total=len(mcps)
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_mcps_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post(
    "/orchestration/urls",
    response_model=MCPUrlsResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_mcps_urls_for_orchestration(request: MCPUrlsRequest):
    """
    Bulk fetches the `hosted_url` field for multiple MCPs by their IDs.
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")
        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)
        config = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            hosted_url = mcp_dict.get("hosted_url")

            config.append(MCPUrlItem(mcp_id=mcp_dict.get("id"), url=hosted_url))

        return MCPUrlsResponse(
            success=True, message=f"Retrieved {len(config)} MCP config", config=config
        )

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.put(
    "/deployment",
    response_model=DeploymentUpdateResponse,
)
async def update_mcp_deployment(
    payload: DeploymentUpdatePayload,
):
    """
    Updates the deployment status of an MCP.

    This endpoint is used to update the deployment status of an MCP.

    ## Request Body
    - **mcp_id**: The ID of the MCP to update
    - **deployment_status**: The new deployment status (pending/completed)
    - **type**: The type of the deployment (sse, http, stdio)
    - **image_name**: Optional image name
    - **error_message**: Optional error message
    - **url**: Optional URL

    ## Response
    Returns the updated MCP with the new deployment status.
    """
    try:
        response = await mcp_service.update_deployment_status(
            mcp_id=payload.mcp_id,
            deployment_status=payload.deployment_status,
            type=payload.type,
            image_name=payload.image_name,
            error_message=payload.error_message,
            url=payload.url,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        # Convert response to dict
        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return DeploymentUpdateResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in update_mcp_deployment: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected error occurred."),
        )


# Container Management Endpoints
@mcp_router.post(
    "/containers/create",
    response_model=CreateContainerResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def create_container_endpoint(payload: CreateContainerRequest):
    """
    Creates a new container for an MCP.
    """
    try:
        # Start with environment variables from the request
        combined_env_vars = list(payload.env) if payload.env else []

        # Retrieve API key credentials for each integration
        if payload.integrations:
            print(f"[DEBUG] Retrieving API key credentials for {len(payload.integrations)} integrations")

            for integration_id in payload.integrations:
                try:
                    # Get API key credentials for this integration
                    credentials_result = await get_auth_service_client().get_api_key_credentials(
                        user_id=payload.user_id,
                        integration_id=integration_id
                    )

                    if credentials_result.get("success") and credentials_result.get("credentials"):
                        # Parse the credentials and convert to EnvKeyValue objects
                        credentials = credentials_result.get("credentials")
                        if credentials:
                            try:
                                # Credentials might be a dict or JSON string
                                if isinstance(credentials, str):
                                    credentials_dict = json.loads(credentials)
                                else:
                                    credentials_dict = credentials

                                # Convert each credential to EnvKeyValue format
                                for key, value in credentials_dict.items():
                                    env_key_value = EnvKeyValue(key=key, value=str(value))
                                    combined_env_vars.append(env_key_value)
                                    print(f"[DEBUG] Added environment variable for integration {integration_id}: {key}")

                            except json.JSONDecodeError as e:
                                print(f"[WARNING] Failed to parse credentials JSON for integration {integration_id}: {e}")
                        else:
                            print(f"[DEBUG] No credentials found for integration {integration_id}")
                    else:
                        print(f"[WARNING] Failed to retrieve credentials for integration {integration_id}: {credentials_result.get('message', 'Unknown error')}")

                except Exception as e:
                    print(f"[ERROR] Error retrieving credentials for integration {integration_id}: {e}")
                    # Continue with other integrations even if one fails
                    continue

        print(f"[DEBUG] Total environment variables to pass: {len(combined_env_vars)}")

        response = await mcp_service.create_container(
            mcp_id=payload.mcp_id,
            user_id=payload.user_id,
            type=payload.type,
            env=combined_env_vars,
        )
        return CreateContainerResponse(
            success=response.success,
            message=response.message,
            container_id=getattr(response, "container_id", None),
        )
    except Exception as e:
        print(f"[DEBUG] Error in create_container_endpoint: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "Failed to create container."),
        )


@mcp_router.post("/containers/{container_id}/stop", response_model=StopContainerResponse)
async def stop_container_endpoint(
    container_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),  # Ensure user has rights
):
    """
    Stops a running container.
    """
    try:
        response = await mcp_service.stop_container(container_id=container_id)
        # Assuming gRPC response has 'success' and 'message'
        return StopContainerResponse(success=response.success, message=response.message)
    except Exception as e:
        print(f"[DEBUG] Error in stop_container_endpoint: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "Failed to stop container."),
        )


@mcp_router.delete(
    "/containers/{container_id}",
    response_model=DeleteContainerResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def delete_container_endpoint(container_id: str):
    """
    Deletes a container.
    This typically involves stopping it and removing its resources.
    """
    try:
        response = await mcp_service.delete_container(container_id=container_id)
        # Assuming gRPC response has 'success' and 'message'
        return DeleteContainerResponse(success=response.success, message=response.message)
    except Exception as e:
        print(f"[DEBUG] Error in delete_container_endpoint: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "Failed to delete container."),
        )


@mcp_router.put("/tool/output-schema", response_model=DeploymentUpdateResponse)
async def update_tool_output_schema(
    payload: ToolOutputSchemaUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Updates the output schema for a tool in an MCP configuration.

    ## Request Body
    - **mcp_id**: The ID of the MCP
    - **tool_name**: The tool's name whose output schema should be updated
    - **output_schema_json**: The new output schema as a JSON object

    ## Response
    Returns the updated MCP config.
    """
    try:
        response = await mcp_service.update_tool_output_schema(
            mcp_id=payload.mcp_id,
            tool_name=payload.tool_name,
            output_schema=payload.output_schema_json,  # 👇 we serialize this in the service layer
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return DeploymentUpdateResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in update_tool_output_schema: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@mcp_router.post("/{mcp_id}/toggle-visibility", response_model=ToggleMcpVisibilityResponseAPI)
async def toggle_mcp_visibility_api(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Toggles the visibility of an MCP between PUBLIC and PRIVATE.
    Only the owner of the MCP can perform this action.
    """
    try:
        grpc_proto_response = await mcp_service.toggle_mcp_visibility(
            mcp_id=mcp_id,
            user_id=current_user["user_id"],
        )

        # 2. Convert the protobuf object to a Python dictionary
        grpc_response_as_dict = MessageToDict(grpc_proto_response, preserving_proto_field_name=True)

        # 3. Check the 'success' field from the DICTIONARY
        if not grpc_response_as_dict.get("success"):
            detail_message = grpc_response_as_dict.get(
                "message", "Failed to toggle MCP visibility."
            )
            status_code = 400
            if "not found" in detail_message.lower():
                status_code = 404
            elif (
                "forbidden" in detail_message.lower() or "not authorized" in detail_message.lower()
            ):
                status_code = 403
            raise HTTPException(status_code=status_code, detail=detail_message)

        return ToggleMcpVisibilityResponseAPI(
            success=True, message=grpc_response_as_dict.get("message")
        )
    except HTTPException:
        raise
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}, Original error: {str(e)}")
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected server error occurred."),
        )


@mcp_router.post("/refresh/{mcp_id}", response_model=MCPResponse)
async def refresh_mcp(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Refreshes the MCP's tool configuration from its hosted URL.
    """
    try:
        response = await mcp_service.refresh_mcp(mcp_id)

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return MCPResponse(
            success=response.success,
            message=response.message,
            mcp=MCPInDB(**mcp_dict),
        )
    except Exception as e:
        print(f"[ERROR] Unexpected error in refresh_mcp: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post("/{mcp_id}/quick-tool")
async def make_quick_tool(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Makes an MCP a quick tool for the current user.

    Users can have a maximum of 4 quick tools at any time.
    """
    try:
        user_id = current_user["user_id"]
        response = await mcp_service.make_quick_tool(mcp_id=mcp_id, user_id=user_id)

        if not response.success:
            # Determine appropriate HTTP status code based on the error message
            status_code = 400
            if "not found" in response.message.lower():
                status_code = 404
            elif "does not have access" in response.message.lower():
                status_code = 403
            elif "maximum limit" in response.message.lower():
                status_code = 409  # Conflict - user has reached the limit

            raise HTTPException(status_code=status_code, detail=response.message)

        return {
            "success": True,
            "message": response.message,
            "quick_tools_count": response.quick_tools_count if response.HasField("quick_tools_count") else None
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in make_quick_tool: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.delete("/{mcp_id}/quick-tool")
async def remove_quick_tool(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Removes an MCP from quick tools for the current user.
    """
    try:
        user_id = current_user["user_id"]
        response = await mcp_service.remove_quick_tool(mcp_id=mcp_id, user_id=user_id)

        if not response.success:
            # Determine appropriate HTTP status code based on the error message
            status_code = 400
            if "not found" in response.message.lower():
                status_code = 404
            elif "does not have access" in response.message.lower():
                status_code = 403

            raise HTTPException(status_code=status_code, detail=response.message)

        return {
            "success": True,
            "message": response.message,
            "quick_tools_count": response.quick_tools_count if response.HasField("quick_tools_count") else None
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in remove_quick_tool: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])



@mcp_router.post("/repo/get", response_model=GetMCPByRepoResponse, dependencies=[Depends(validate_mcp_proxy_server_auth_key)])
async def get_mcp_by_repo(
    payload: GetMCPByRepoRequest,
):
    """
    Retrieves an MCP configuration by repository name and optionally git username.

    This endpoint allows finding MCPs based on their repository information,
    which is useful for integrations and automated workflows.

    Authentication: Requires MCP proxy server authentication with key name "X-MCP-Proxy-Server-Auth-Key"
    """
    try:
        response = await mcp_service.getMCPByRepo(
            repo_name=payload.repo_name,
            git_user_name=payload.git_user_name,
            user_id=None  # No user context needed for server-to-server calls
        )

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return GetMCPByRepoResponse(
            success=response.success,
            message=response.message,
            mcp=MCPInDB(**mcp_dict),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("/deployment-status/{container_name}", response_model=GetMCPDeploymentStatusResponse, dependencies=[Depends(validate_mcp_proxy_server_auth_key)])
async def get_mcp_deployment_status(
    container_name: str,
):
    """
    Retrieves the latest deployment status for a specific container.

    This endpoint provides the most recent deployment status for a container,
    including container status, user information, and timestamps.

    Authentication: Requires MCP proxy server authentication with key name "X-MCP-Proxy-Server-Auth-Key"
    """
    try:
        response = await mcp_service.getMCPDeploymentStatus(container_name=container_name)

        if not response.success:
            # Determine appropriate HTTP status code based on the error message
            status_code = 500
            if "not found" in response.message.lower():
                status_code = 404
            raise HTTPException(status_code=status_code, detail=response.message)

        # Convert protobuf deployment to Pydantic model
        deployment = None
        if response.deployment:
            deployment_dict = MessageToDict(response.deployment, preserving_proto_field_name=True)
            deployment = MCPDeploymentInfo(**deployment_dict)

        return GetMCPDeploymentStatusResponse(
            success=response.success,
            message=response.message,
            deployment=deployment,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
