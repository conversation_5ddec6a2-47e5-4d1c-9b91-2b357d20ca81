from fastapi import APIRouter, Depends, HTTPException, status
from typing import Optional
from app.core.security import validate_mcp_proxy_server_auth_key, validate_server_auth_key
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.user import (
    PublicKeyCreate,
    PublicKeyResponse,
    PublicKeyListResponse,
    PublicKeyValidateRequest,
    PublicKeyValidateResponse,
    PublicKeyDeleteResponse,
    PublicKeyDetailsResponse,
    PublicKeyInfo,
    PublicKeyUserDetailsRequest,
    PublicKeyUserDetailsResponse,
    # Keep old schemas for backward compatibility
    APIKeyCreate,
    APIKeyDetailsResponse,
    APIKeyInfo,
    APIKeyListResponse,
    APIKeyResponse,
)
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict

api_key_router = APIRouter(prefix="/api-keys", tags=["api keys"])
user_service = UserServiceClient()


@api_key_router.post("/create-key", response_model=PublicKeyResponse)
async def create_public_key(
    key_data: PublicKeyCreate, current_user: dict = Depends(role_required(["user"]))
):
    """
    Create a new public key for the current user using HMAC-based generation.

    Args:
        key_data (PublicKeyCreate): Data required to create the public key, including name and description.
        current_user (dict): Current user information.

    Returns:
        PublicKeyResponse: Response containing the generated public key details.

    Raises:
        HTTPException: If the public key creation fails or an internal error occurs.
    """

    try:
        response = await user_service.create_public_key(
            user_id=current_user["user_id"],
            name=key_data.name,
            organization_id=current_user["organisation_id"],
            key_type=key_data.type.value,
            description=key_data.description,
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        api_key = MessageToDict(response.api_key, preserving_proto_field_name=True)
        print(f"Response: {response}")
        return PublicKeyResponse(
            success=response.success, message=response.message, api_key=PublicKeyInfo(**api_key)
        )
    except Exception as e:
        print(f"Error creating public key: {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.post(
    "/validate",
    response_model=PublicKeyValidateResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def validate_public_key(validate_data: PublicKeyValidateRequest):
    """
    Validate a public key against the user's encryption key from GSM.

    Args:
        validate_data (PublicKeyValidateRequest): Data containing the public key to validate.
        current_user (dict): Current user information.

    Returns:
        PublicKeyValidateResponse: Response containing validation result.

    Raises:
        HTTPException: If validation fails or an internal error occurs.
    """
    try:
        response = await user_service.validate_public_key(public_key=validate_data.public_key)

        return PublicKeyValidateResponse(
            success=response.success, message=response.message, is_validated=response.is_validated
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.get("/list", response_model=PublicKeyListResponse)
async def list_public_keys(
    organization_id: Optional[str] = None,
    type: Optional[str] = None,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    List all public keys of the current user (without exposing the public key values).
    Optionally filter by organization_id and type.

    Args:
        organization_id (Optional[str]): Filter by organization ID
        type (Optional[str]): Filter by API key type (mcp, workflow, knowledge, voice)
        current_user (dict): Current user information

    Returns:
        PublicKeyListResponse: Response containing the list of public keys

    Raises:
        HTTPException: If the user is not found or an internal error occurs
    """
    try:
        response = await user_service.list_public_keys(
            user_id=current_user["user_id"], organization_id=organization_id, key_type=type
        )
        print(f"Response: {response}")
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        api_keys = [
            PublicKeyInfo(**MessageToDict(key, preserving_proto_field_name=True))
            for key in response.api_keys
        ]

        return PublicKeyListResponse(
            success=response.success, message=response.message, api_keys=api_keys
        )
    except Exception as e:
        print(f"Error listing public keys: {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.delete("/{key_id}", response_model=PublicKeyDeleteResponse)
async def delete_public_key(key_id: str, current_user: dict = Depends(role_required(["user"]))):
    """
    Delete a public key by its ID.

    Args:
        key_id (str): ID of the public key to delete
        current_user (dict): Current user information

    Returns:
        PublicKeyDeleteResponse: Response containing the deletion status
    """

    try:
        response = await user_service.delete_public_key(
            user_id=current_user["user_id"], key_id=key_id
        )

        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return PublicKeyDeleteResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.get("/get/{key_id}", response_model=PublicKeyDetailsResponse)
async def get_public_key(key_id: str, current_user: dict = Depends(role_required(["user"]))):
    """
    Get a public key by its ID.

    Args:
        key_id (str): ID of the public key to retrieve
        current_user (dict): Current user information

    Returns:
        PublicKeyDetailsResponse: Response containing the public key information

    Raises:
        HTTPException: If the public key is not found or an internal error occurs
    """
    try:
        response = await user_service.get_public_key(key_id=key_id, user_id=current_user["user_id"])
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        api_key = None
        if response.success and response.api_key:
            api_key = PublicKeyInfo(
                **MessageToDict(response.api_key, preserving_proto_field_name=True)
            )

        return PublicKeyDetailsResponse(
            success=response.success, message=response.message, api_key=api_key
        )
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@api_key_router.post(
    "/user-details",
    response_model=PublicKeyUserDetailsResponse,
    dependencies=[Depends(validate_mcp_proxy_server_auth_key)],
)
async def get_user_details_by_public_key(request_data: PublicKeyUserDetailsRequest):
    """
    Get user details (user_id and organisation_id) by validating a public key.

    Args:
        request_data (PublicKeyUserDetailsRequest): Data containing the public key to validate.

    Returns:
        PublicKeyUserDetailsResponse: Response containing validation result and user details.

    Raises:
        HTTPException: If validation fails or an internal error occurs.
    """
    try:
        response = await user_service.get_user_details_by_public_key(
            public_key=request_data.public_key
        )

        return PublicKeyUserDetailsResponse(
            success=response.success,
            message=response.message,
            is_validated=response.is_validated,
            user_id=response.user_id if response.is_validated else None,
            organisation_id=response.organisation_id if response.is_validated else None,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error getting user details by public key: {e}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
