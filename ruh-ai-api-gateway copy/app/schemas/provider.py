from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl, field_validator
from datetime import datetime


# Provider Schemas
class ProviderBase(BaseModel):
    provider: str = Field(..., description="Provider name (e.g., OpenAI)", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Optional description of the provider")
    base_url: HttpUrl = Field(..., description="Base API URL for the provider")
    is_active: Optional[bool] = Field(True, description="Whether the provider is active")
    is_default: Optional[bool] = Field(False, description="Whether this is the default provider")
    platform: str = Field(..., description="Platform type: either 'requesty' or 'openrouter'")


class ProviderCreate(ProviderBase):
    pass


class ProviderUpdate(BaseModel):
    provider: Optional[str] = Field(None, description="Provider name", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the provider")
    base_url: Optional[HttpUrl] = Field(None, description="Base API URL for the provider")
    is_active: Optional[bool] = Field(None, description="Whether the provider is active")
    is_default: Optional[bool] = Field(None, description="Whether this is the default provider")
    platform: Optional[str] = Field(None, description="Platform type: either 'requesty' or 'openrouter'")


class ProviderInfo(BaseModel):
    id: str
    provider: str
    description: Optional[str]
    base_url: str = Field(alias="baseUrl")
    is_active: bool = Field(alias="isActive")
    isDefault: Optional[bool] = False
    created_at: datetime = Field(alias="createdAt")
    updated_at: datetime = Field(alias="updatedAt")
    model_count: int = Field(0, description="Number of models for this provider", alias="modelCount")
    platform: str = Field(..., description="Platform type: either 'requesty' or 'openrouter'")

    @field_validator('created_at', 'updated_at', mode='before')
    @classmethod
    def parse_datetime(cls, v):
        if isinstance(v, str):
            # Handle ISO format with or without 'Z'
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v

    class Config:
        populate_by_name = True


class ProviderResponse(BaseModel):
    success: bool
    message: str
    provider: Optional[ProviderInfo] = None


class ProviderListResponse(BaseModel):
    success: bool
    message: str
    providers: List[ProviderInfo]
    pagination: "PaginationInfo"


class ProviderDeleteResponse(BaseModel):
    success: bool
    message: str


# Model Schemas
class ModelBase(BaseModel):
    provider_id: str = Field(..., description="ID of the provider this model belongs to")
    model: str = Field(..., description="Model name (e.g., gpt-4)", min_length=1, max_length=255)
    model_id: str = Field(..., description="API reference model ID", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Optional description of the model")
    input_price_per_token: Optional[float] = Field(None, description="Pricing per input token", ge=0)
    output_price_per_token: Optional[float] = Field(None, description="Pricing per output token", ge=0)
    max_tokens: Optional[int] = Field(None, description="Maximum token limit", ge=1)
    context_window: Optional[int] = Field(None, description="Context window size", ge=1)
    temperature: Optional[float] = Field(0.7, description="Model temperature", ge=0, le=2)
    provider_type: Optional[str] = Field("chat", description="Provider type (e.g., chat, completion)")
    is_active: Optional[bool] = Field(True, description="Whether the model is active")
    is_default: Optional[bool] = Field(False, description="Whether this is the default model for the provider")


class ModelCreate(ModelBase):
    pass


class ModelUpdate(BaseModel):
    provider_id: Optional[str] = Field(None, description="ID of the provider this model belongs to")
    model: Optional[str] = Field(None, description="Model name", min_length=1, max_length=255)
    model_id: Optional[str] = Field(None, description="API reference model ID", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the model")
    input_price_per_token: Optional[float] = Field(None, description="Pricing per input token", ge=0)
    output_price_per_token: Optional[float] = Field(None, description="Pricing per output token", ge=0)
    max_tokens: Optional[int] = Field(None, description="Maximum token limit", ge=1)
    context_window: Optional[int] = Field(None, description="Context window size", ge=1)
    temperature: Optional[float] = Field(None, description="Model temperature", ge=0, le=2)
    provider_type: Optional[str] = Field(None, description="Provider type")
    is_active: Optional[bool] = Field(None, description="Whether the model is active")
    is_default: Optional[bool] = Field(None, description="Whether this is the default model for the provider")


class ModelInfo(BaseModel):
    id: str
    provider_id: str = Field(alias="providerId")
    model: str
    model_id: str = Field(alias="modelId")
    description: Optional[str]
    input_price_per_token: Optional[float] = Field(alias="inputPricePerToken")
    output_price_per_token: Optional[float] = Field(alias="outputPricePerToken")
    max_tokens: Optional[int] = Field(alias="maxTokens")
    context_window: Optional[int] = Field(alias="contextWindow")
    temperature: float
    provider_type: str = Field(alias="providerType")
    is_active: bool = Field(alias="isActive")
    isDefault: Optional[bool] = False
    created_at: datetime = Field(alias="createdAt")
    updated_at: datetime = Field(alias="updatedAt")
    provider: ProviderInfo

    @field_validator('created_at', 'updated_at', mode='before')
    @classmethod
    def parse_datetime(cls, v):
        if isinstance(v, str):
            # Handle ISO format with or without 'Z'
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v

    class Config:
        populate_by_name = True


class ModelResponse(BaseModel):
    success: bool
    message: str
    model: Optional[ModelInfo] = None


class ModelListResponse(BaseModel):
    success: bool
    message: str
    models: List[ModelInfo]
    pagination: "PaginationInfo"


class ModelDeleteResponse(BaseModel):
    success: bool
    message: str


# Common Schemas
class PaginationInfo(BaseModel):
    current_page: int = Field(..., description="Current page number", alias="currentPage")
    total_pages: int = Field(..., description="Total number of pages", alias="totalPages")
    total_items: int = Field(..., description="Total number of items", alias="totalItems")
    page_size: int = Field(..., description="Number of items per page", alias="pageSize")

    class Config:
        populate_by_name = True


class ListRequest(BaseModel):
    page: int = Field(1, description="Page number", ge=1)
    page_size: int = Field(10, description="Number of items per page", ge=1, le=100)
    is_active: Optional[bool] = Field(None, description="Filter by active status")


# Sync Models Schemas
class SyncStats(BaseModel):
    providers_added: int = Field(0, description="Number of providers added", alias="providersAdded")
    providers_updated: int = Field(0, description="Number of providers updated", alias="providersUpdated")
    providers_removed: int = Field(0, description="Number of providers removed", alias="providersRemoved")
    models_added: int = Field(0, description="Number of models added", alias="modelsAdded")
    models_updated: int = Field(0, description="Number of models updated", alias="modelsUpdated")
    models_removed: int = Field(0, description="Number of models removed", alias="modelsRemoved")
    total_processed: int = Field(0, description="Total items processed", alias="totalProcessed")

    class Config:
        populate_by_name = True


class SyncModelsResponse(BaseModel):
    success: bool
    message: str
    stats: Optional[SyncStats] = None


# Update forward references
ProviderListResponse.model_rebuild()
ModelListResponse.model_rebuild()