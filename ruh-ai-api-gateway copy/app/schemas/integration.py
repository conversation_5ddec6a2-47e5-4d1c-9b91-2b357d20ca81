"""
Integration Management Schemas

This module defines Pydantic schemas for integration management operations.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class IntegrationStatus(str, Enum):
    """Integration status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ERROR = "error"


class IntegrationType(str, Enum):
    """Integration type enumeration."""
    OAUTH = "oauth"
    API_KEY = "api_key"
    CUSTOM = "custom"


class IntegrationBase(BaseModel):
    """Base integration schema."""
    name: str = Field(..., description="Integration name")
    display_name: Optional[str] = Field(None, description="Display name for the integration")
    description: Optional[str] = Field(None, description="Integration description")
    logo: Optional[str] = Field(None, description="Integration logo URL or path")
    integration_type: IntegrationType = Field(..., description="Type of integration")
    provider: Optional[str] = Field(None, description="OAuth provider or service name")
    is_enabled: bool = Field(True, description="Whether the integration is enabled")
    configuration: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Integration configuration")
    supported_scopes: List[str] = Field(default_factory=list, description="Supported OAuth scopes")
    api_key_config: Optional[List[Dict[str, Any]]] = Field(None, description="API key configuration for API key integrations")


class IntegrationCreate(IntegrationBase):
    """Schema for creating a new integration."""
    pass


class IntegrationUpdate(BaseModel):
    """Schema for updating an integration."""
    display_name: Optional[str] = Field(None, description="Display name for the integration")
    description: Optional[str] = Field(None, description="Integration description")
    is_enabled: Optional[bool] = Field(None, description="Whether the integration is enabled")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Integration configuration")
    supported_scopes: Optional[List[str]] = Field(None, description="Supported OAuth scopes")
    api_key_config: Optional[List[Dict[str, Any]]] = Field(None, description="API key configuration for API key integrations")


class IntegrationResponse(IntegrationBase):
    """Schema for integration response."""
    id: str = Field(..., description="Integration ID")
    status: IntegrationStatus = Field(..., description="Integration status")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")


class IntegrationListResponse(BaseModel):
    """Schema for integration list response with pagination."""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")
    integrations: List[IntegrationResponse] = Field(..., description="List of integrations")
    total: int = Field(..., description="Total number of integrations")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")


class IntegrationDeleteResponse(BaseModel):
    """Schema for integration deletion response."""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")

class UserIntegrationStatus(BaseModel):
    """
    Status of a user's integration connection.
    """

    user_id: str = Field(..., example="user_123")
    integration_id: str = Field(..., example="550e8400-e29b-41d4-a716-446655440000")
    integration_name: str = Field(..., example="Google Calendar")
    logo: Optional[str] = Field(None, example="https://example.com/google-logo.png", description="Integration logo URL or path")
    is_connected: bool = Field(..., example=True)
    last_used_at: Optional[str] = Field(None, example="2023-01-03T12:00:00")
    created_at: str = Field(..., example="2023-01-01T12:00:00")
    scopes: List[str] = Field(default_factory=list, example=["https://www.googleapis.com/auth/calendar"])
    connection_type: IntegrationType = Field(..., description="Type of integration")
    schema_definition: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Integration schema definition for API key fields (only for API key integrations)",
        example=[{"name": "api_key", "required": True, "description": "Your API secret key"}]
    )


class UserIntegrationsListResponse(BaseModel):
    """
    Response model for listing user's connected integrations.
    """
    
    success: bool = Field(..., example=True)
    message: str = Field(..., example="User integrations retrieved successfully")
    integrations: List[UserIntegrationStatus] = Field(..., description="List of user's integrations")


class OAuthCredentialResponse(BaseModel):
    """
    Response model for retrieving stored OAuth credentials.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials retrieved successfully")
    credentials: Optional[Dict[str, Any]] = Field(
        None,
        description="OAuth credentials as key-value pairs",
        example={
            "access_token": "ya29.a0AfH6SMB...",
            "refresh_token": "1//0eXxyz...",
            "token_type": "Bearer",
            "expires_in": 3599,
            "scope": "https://www.googleapis.com/auth/calendar"
        }
    )
    user_integration_status: Optional[UserIntegrationStatus] = Field(
        None,
        description="User integration status information"
    )


class OAuthCredentialDeleteResponse(BaseModel):
    """
    Response model for deleting an OAuth credential.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credential deleted successfully")


class UserIntegrationsListResponse(BaseModel):
    """
    Response model for listing user's connected integrations.
    """
    
    success: bool = Field(..., example=True)
    message: str = Field(..., example="User integrations retrieved successfully")
    integrations: List[UserIntegrationStatus] = Field(..., description="List of user's integrations")


# API Key Credential Schemas

class APIKeyCredentialRequest(BaseModel):
    """
    Request model for storing/updating API key credentials.
    """
    
    credentials: Dict[str, Any] = Field(
        ...,
        description="API key credentials as key-value pairs",
        example={"api_key": "sk-1234567890abcdef", "secret": "secret_value"}
    )


class APIKeyCredentialResponse(BaseModel):
    """
    Response model for retrieving API key credentials.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="API key credentials retrieved successfully")
    user_id: Optional[str] = Field(None, example="user_123")
    integration_id: Optional[str] = Field(None, example="550e8400-e29b-41d4-a716-446655440000")
    credentials: Optional[Dict[str, Any]] = Field(
        None,
        description="API key credentials as key-value pairs",
        example={"api_key": "sk-1234567890abcdef", "secret": "secret_value"}
    )
    is_connected: Optional[bool] = Field(False, example=True)
    last_used_at: Optional[str] = Field(None, example="2023-01-03T12:00:00")
    api_key_config: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Integration schema definition for API key fields",
        example=[{"name": "api_key", "required": True, "description": "Your API secret key"}]
    )


class APIKeyCredentialStoreResponse(BaseModel):
    """
    Response model for storing API key credentials.
    """
    
    success: bool = Field(..., example=True)
    message: str = Field(..., example="API key credentials stored successfully")
    integration_id: str = Field(..., example="550e8400-e29b-41d4-a716-446655440000")


class APIKeyCredentialUpdateResponse(BaseModel):
    """
    Response model for updating API key credentials.
    """
    
    success: bool = Field(..., example=True)
    message: str = Field(..., example="API key credentials updated successfully")
    integration_id: str = Field(..., example="550e8400-e29b-41d4-a716-446655440000")


class APIKeyCredentialDeleteResponse(BaseModel):
    """
    Response model for deleting API key credentials.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="API key credentials deleted successfully")


class IntegrationStatusItem(BaseModel):
    """
    Individual integration status item.
    """

    integration_id: str = Field(..., example="550e8400-e29b-41d4-a716-446655440000")
    is_connected: bool = Field(..., example=True)


class CheckIntegrationStatusRequest(BaseModel):
    """
    Request model for checking integration connection status.
    """
    integration_ids: List[str] = Field(
        ...,
        example=["550e8400-e29b-41d4-a716-446655440000", "660e8400-e29b-41d4-a716-446655440001"],
        description="List of integration IDs to check"
    )


class CheckIntegrationStatusResponse(BaseModel):
    """
    Response model for checking integration connection status.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="Integration statuses retrieved successfully")
    integration_statuses: List[IntegrationStatusItem] = Field(
        ...,
        description="List of integration statuses"
    )
