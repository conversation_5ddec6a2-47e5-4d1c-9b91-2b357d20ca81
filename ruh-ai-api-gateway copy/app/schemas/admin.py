from typing import Optional, List, Any
from enum import Enum
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime

class RoleInfo(BaseModel):
    role_id: str
    name: str
    description: str
    permissions: List[str]
    created_at: str
    updated_at: str

class AdminInfo(BaseModel):
    admin_id: str
    email: str
    full_name: str
    roles: List[RoleInfo]
    created_at: str
    updated_at: str

class AdminCreate(BaseModel):
    email: EmailStr
    password: str
    full_name: str
    role_ids: Optional[List[str]] = None

class AdminUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    role_ids: Optional[List[str]] = None

class RoleCreate(BaseModel):
    name: str
    description: str
    permissions: List[str]

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[List[str]] = None

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    roles: List[RoleInfo]

class AdminResponse(BaseModel):
    success: bool
    message: str
    admin: Optional[AdminInfo] = None

class RoleResponse(BaseModel):
    success: bool
    message: str
    role: Optional[RoleInfo] = None

class AdminList(BaseModel):
    admins: List[AdminInfo]
    total: int
    page: int
    total_pages: int

class RoleList(BaseModel):
    roles: List[RoleInfo]
    total: int
    page: int
    total_pages: int

class VisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class StatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"
    BENCH = "bench"  # Added to match proto definition


class CreatorRoleEnum(str, Enum):
    MEMBER = "member"
    CREATOR = "creator"
    VIEWER = "viewer"


# ============ Organisation-related schemas ==============
class OrganisationAdmin(BaseModel):
    admin_id: str
    email: str
    full_name: str
    phone: str
    role: str
    status: str
    last_login: str
    created_at: str
    updated_at: str

class OrganisationDepartment(BaseModel):
    id: str
    name: str
    description: str
    visibility: str
    created_at: str
    updated_at: str
    member_count: int

class OrganisationStatistics(BaseModel):
    total_users: int
    total_departments: int
    total_department_members: int
    avg_members_per_dept: float
    departments_with_members: int
    empty_departments: int

class OrganisationInfo(BaseModel):
    id: str
    name: str
    website_url: str
    logo: str
    industry: str
    description: str
    address: str
    phone: str
    email: str
    size: str
    type: str
    status: str
    created_by: str
    created_at: str
    updated_at: str
    mcp_key: str
    is_key_revoked: bool
    admin: OrganisationAdmin
    departments: List[OrganisationDepartment]
    statistics: OrganisationStatistics
    plan_type: Optional[str] = None
    available_tokens:Optional[float] = None

# Limited organisation data for getAllOrganisations
class OrganisationLimited(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    logo: Optional[str] = None
    industry: Optional[str] = None
    created_at: str
    status: Optional[str] = None
    total_departments: int
    total_users: int
    type: Optional[str] = None
    total_payments: int
    total_rcu: int
    rcu_used: int
    plan_type: Optional[str] = None
    available_tokens:Optional[float] = None

class GetAllOrganisationsResponse(BaseModel):
    success: bool
    message: str
    organisations: Optional[List[OrganisationLimited]] = None
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    status: Optional[str] = None

# Request model for getting organisation details
class GetOrganisationDetailsRequest(BaseModel):
    organisation_id: str

# Response model for organisation details
class GetOrganisationDetailsResponse(BaseModel):
    success: bool
    message: str
    organisation: Optional[OrganisationInfo] = None
    status: Optional[str] = None


# Source Management Schemas
class SourceModel(BaseModel):
    id: str
    organisation_id: str
    type: str  # This will be "GOOGLE_DRIVE" or "SLACK"
    name: str
    created_at: str
    updated_at: str

class ListSourcesRequest(BaseModel):
    organisation_id: str

class ListSourcesResponse(BaseModel):
    success: bool
    message: str
    sources: List[SourceModel]
    is_initial_mapping: bool  # True if at least one department other than general has access to at least one folder

class GetOrganisationActiveUsersRequest(BaseModel):
    organisation_id: str
    department_id: Optional[str] = None
    page_number: int = 1
    page_size: int = 10

class GetOrganisationActiveUsersResponse(BaseModel):
    success: bool
    message: str
    users: Optional[List[dict]] = None
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    dept_name: Optional[str] = None
    dept_desc: Optional[str] = None

# New schema that matches the Agent message from agent_graph.proto
class AgentGraphInDB(BaseModel):
    """
    Agent schema that matches the Agent message from agent_graph.proto.
    Used specifically for agent graph service responses.
    """
    id: str
    name: str
    description: str
    department: str
    owner_id: str
    owner_name: str
    user_ids: List[str] = Field(default_factory=list)
    created_at: str  # String format as per proto
    updated_at: str  # String format as per proto
    visibility: VisibilityEnum
    status: StatusEnum
    creator_role: CreatorRoleEnum



# User Management Schema
class GetUserRequest(BaseModel):
    page: Optional[int] = 1
    page_size: Optional[int] = 12
    company:Optional[str] = None
    department: Optional[str] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None
    is_email_verified: Optional[bool] = None
    search: Optional[str] = None

class UserInfo(BaseModel):
    id: str
    full_name: str
    email: str
    profile_image: str
    is_active: Optional[bool]
    is_email_verified: Optional[bool]
    default_organization: Optional[str] = None
    role: Optional[str] = None
    company: Optional[str] = None
    department: Optional[str] = None
    job_role: Optional[str] = None
    created_at: str
    updated_at: str

class GetUsersResponse(BaseModel):
    success: bool
    message: str
    users: Optional[List[UserInfo]] = None
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    status: Optional[str] = None


# Agent Management Schemas
class GetAgentsRequest(BaseModel):
    page: Optional[int] = 1
    page_size: Optional[int] = 12
    visibility: Optional[str] = None
    agent_category: Optional[str] = None
    department: Optional[str] = None
    is_bench_employee: Optional[bool] = None
    search: Optional[str] = None

class AgentInfo(BaseModel):
    id: str
    name: str
    description: str
    avatar: str
    is_imported: bool
    agent_category: str
    agent_topic_type: Optional[str] = None
    department: Optional[str] = None
    is_bench_employee: bool
    is_a2a: bool
    is_customizable: bool
    category: Optional[str] = None
    use_count: Optional[int] = None
    average_rating: Optional[float] = None
    visibility: Optional[str] = None
    tags: Optional[List[str]] = None
    status: str
    created_at: str
    updated_at: str

class GetAgentsResponse(BaseModel):
    success: bool
    message: str
    agents: Optional[List[AgentInfo]] = None
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    status: Optional[str] = None

# Workflow Management Schemas
class WorkflowInfo(BaseModel):
    id: str
    name: str
    description: Optional[str]
    image_url: Optional[str]
    workflow_url: str
    builder_url: str
    start_nodes: Any  # JSON type
    available_nodes: Any  # JSON type
    owner_id: str
    owner_type: str
    current_version_id: Optional[str]
    is_imported: Optional[bool]
    workflow_template_id: Optional[str]
    template_owner_id: Optional[str]
    is_customizable: Optional[bool]
    is_updated: Optional[bool]
    user_ids: Optional[List[str]]
    visibility: str
    status: str
    category: Optional[str]
    tags: Optional[List[str]]
    created_at: str
    updated_at: str
    deleted_at: Optional[str]
    source_version_id: Optional[str]

class GetWorkflowsResponse(BaseModel):
    success: bool
    message: str
    workflows: Optional[List[WorkflowInfo]] = None
    total_count: Optional[int] = None
    page: Optional[int] = None
    page_size: Optional[int] = None
    status: Optional[str] = None

# Organisation Active Users Schemas
class UserDepartmentDetail(BaseModel):
    name: str
    role: str
    permission: str

class DepartmentUser(BaseModel):
    id: str
    name: str
    email: str
    departments: List[UserDepartmentDetail]

