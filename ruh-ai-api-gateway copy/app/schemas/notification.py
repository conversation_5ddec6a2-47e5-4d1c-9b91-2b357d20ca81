from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

class NotificationInfo(BaseModel):
    id: str
    title: str
    user_id: str
    link: Optional[str] = None
    logo: Optional[str] = None
    seen: bool
    description: Optional[str] = None
    organisation_id: Optional[str] = None
    created_at: str

class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool

class PaginatedNotificationResponse(BaseModel):
    data: List[NotificationInfo]
    metadata: PaginationMetadata
    is_all_seen: bool

class MarkAsSeenResponse(BaseModel):
    success: bool
    message: str

class GetNotificationByIdResponse(BaseModel):
    notification: NotificationInfo
    success: bool
    message: str

class SendNotificationRequest(BaseModel):
    title: str
    body: str
    link: Optional[str] = None
    logo: Optional[str] = None
    data: Optional[str] = None

class SendNotificationResponse(BaseModel):
    success: bool
    message: str