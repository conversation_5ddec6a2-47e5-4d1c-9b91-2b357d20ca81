import async<PERSON>
from typing import List, Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.schemas.marketplace import UseMarketplaceItemResponse
from app.services.agent_service import AgentService<PERSON>lient
from app.services.workflow_service import WorkflowService<PERSON>lient
from app.services.mcp_service import <PERSON>PService<PERSON><PERSON>
from google.protobuf.json_format import MessageToDict

agent_service = AgentServiceClient()
workflow_service = WorkflowServiceClient()
mcp_service = MCPServiceClient()


async def add_agent_to_workspace(
    agent_id: str,
    user_id: str,
    workflow_ids: Optional[List[str]] = None,
    mcp_ids: Optional[List[str]] = None,
    department_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
):
    workflow_ids = workflow_ids or []
    mcp_ids = mcp_ids or []

    workflow_stats = {
        "total_count_to_add": len(workflow_ids),
        "successfully_added": 0,
        "failed_to_add": 0,
    }
    workflows_added = []

    for i in workflow_ids:
        try:
            response = await workflow_service.use_workflow(workflow_id=i, user_id=user_id)
            if response.success:
                workflows_added.append(response.workflow_id)
                workflow_stats["successfully_added"] += 1
            else:
                workflow_stats["failed_to_add"] += 1
        except Exception as e:
            print(f"[ERROR] Failed to add workflow {i}: {str(e)}")
            workflow_stats["failed_to_add"] += 1

    mcp_stats = {"total_count_to_add": len(mcp_ids), "successfully_added": 0, "failed_to_add": 0}
    mcps_added = []

    for i in mcp_ids:
        try:
            response = await mcp_service.use_mcp(mcp_id=i, user_id=user_id)
            if response.success:
                mcps_added.append(response.mcp_id)
                mcp_stats["successfully_added"] += 1
            else:
                mcp_stats["failed_to_add"] += 1
        except Exception as e:
            print(f"[ERROR] Failed to add MCP {i}: {str(e)}")
            mcp_stats["failed_to_add"] += 1

    try:
        response = await agent_service.use_agent(
            agent_id=agent_id,
            user_id=user_id,
            workflow_ids=workflows_added,
            mcp_ids=mcps_added,
            department_id=department_id,
            organisation_id=organisation_id
        )
        agent_success = response.success
        agent_message = response.message
        agent_use_count = getattr(response, "use_count", 1)
        agent_final_id = getattr(response, "agent_id", agent_id)
    except Exception as e:
        print(f"[ERROR] Failed to use agent: {str(e)}")
        agent_success = False
        agent_message = f"Agent partially added. Error: {str(e)}"
        agent_use_count = 0
        agent_final_id = agent_id

    return {
        "success": agent_success,
        "message": agent_message,
        "agent_id": agent_final_id,
        "agent": {"workflows": workflow_stats, "mcps": mcp_stats},
        "use_count": agent_use_count,
    }


# asyncio.run(
#     add_agent_to_workspace(
#         agent_id="15bcbea7-a611-4532-a0bb-94d82718c386",
#         user_id="fcefc564-081b-49c6-a7ae-381ce7c29401",
#     )
# )
