from enum import Enum

class AgentOwnerTypeEnum(str, Enum):
    USER = "user"
    ENTERPRISE = "enterprise"
    PLATFORM = "platform"

class AgentCategoryEnum(str, Enum):
    USER_PROXY = "user_proxy"
    ASSISTANT = "assistant"
    AI_AGENT = "ai_agent"

class AgentVisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"

class AgentStatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"

class AgentCategoryTypeEnum(str, Enum):
    MARKETING = "marketing"
    
GITHUB_AUTH_URL = "https://github.com/login/oauth/authorize"
GITHUB_TOKEN_URL = "https://github.com/login/oauth/access_token"
GITHUB_API_URL = "https://api.github.com"

GITHUB_INTEGRATION_ID = "c5e5b42b-3696-42ed-8c26-8aae3d16fffe"