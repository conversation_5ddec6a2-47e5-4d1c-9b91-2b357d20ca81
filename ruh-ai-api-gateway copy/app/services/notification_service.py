import grpc
from fastapi import HTTPException
from app.core.config import settings
from app.grpc_ import notification_pb2, notification_pb2_grpc


class NotificationsServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.NOTIFICATION_SERVICE_HOST}:{settings.NOTIFICATION_SERVICE_PORT}"
        )
        self.stub = notification_pb2_grpc.NotificationServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        print(f"[ERROR] gRPC error occurred - Status: {status_code}, Details: {details}")
        print(f"[ERROR] Full error: {str(e)}")

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")

    async def get_user_notifications(self, user_id: str, page: int = 1, page_size: int = 10):
        print(
            f"[INFO] Getting notifications for user_id={user_id}, page={page}, page_size={page_size}"
        )
        request = notification_pb2.GetUserNotificationsRequest(
            user_id=user_id, page=page, page_size=page_size
        )
        try:
            print(f"[DEBUG] Sending gRPC request: {request}")
            response = self.stub.GetUserNotifications(request)
            print(f"[INFO] Received response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] Failed to get user notifications: {str(e)}")
            raise self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in get_user_notifications: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

    async def mark_notification_as_seen(self, notification_id: str, user_id: str):
        print(
            f"[INFO] Marking notification as seen - notification_id={notification_id}, user_id={user_id}"
        )
        request = notification_pb2.MarkNotificationAsSeenRequest(
            notification_id=notification_id, user_id=user_id
        )
        try:
            print(f"[DEBUG] Sending gRPC request: {request}")
            response = self.stub.MarkNotificationAsSeen(request)
            print(f"[INFO] Received response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] Failed to mark notification as seen: {str(e)}")
            raise self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in mark_notification_as_seen: {str(e)}")
            raise self._handle_error(e)

    async def get_notification_by_id(self, notification_id: str, user_id: str):
        print(
            f"[INFO] Getting notification by ID - notification_id={notification_id}, user_id={user_id}"
        )
        request = notification_pb2.GetNotificationByIdRequest(
            notification_id=notification_id, user_id=user_id
        )
        try:
            print(f"[DEBUG] Sending gRPC request: {request}")
            response = self.stub.GetNotificationById(request)
            print(f"[INFO] Received response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] Failed to get notification by ID: {str(e)}")
            raise self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in get_notification_by_id: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

    async def mark_all_notifications_as_seen(self, user_id: str):
        print(f"[INFO] Marking all notifications as seen for user_id={user_id}")
        request = notification_pb2.MarkAllNotificationsAsSeenRequest(user_id=user_id)
        try:
            print(f"[DEBUG] Sending gRPC request: {request}")
            response = self.stub.MarkAllNotificationsAsSeen(request)
            print(f"[INFO] Received response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] Failed to mark all notifications as seen: {str(e)}")
            raise self._handle_error(e)
        except Exception as e:
            print(f"[ERROR] Unexpected error in mark_all_as_seen: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
