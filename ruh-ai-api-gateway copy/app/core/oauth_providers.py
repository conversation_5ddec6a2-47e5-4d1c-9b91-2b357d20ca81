"""
OAuth Provider Configuration System

This module defines OAuth provider configurations and scope mappings
for different OAuth providers and tools.
"""

import json
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class OAuthProvider(str, Enum):
    """Supported OAuth providers."""

    GOOGLE = "google"
    MICROSOFT = "microsoft"
    GITHUB = "github"
    SLACK = "slack"
    RAPID_HRMS = "rapid_hrms"
    CUSTOM = "custom"
    JIRA = "jira"
    ZOHO = "zoho"


class OAuthProviderConfig(BaseModel):
    """Configuration for an OAuth provider."""

    provider: OAuthProvider
    client_id: str
    client_secret: str
    redirect_uri: str
    auth_url: str
    token_url: str
    user_info_url: Optional[str] = None
    revoke_url: Optional[str] = None

    # Provider-specific parameters
    access_type: Optional[str] = None  # For Google: "offline"
    prompt: Optional[str] = None  # For Google: "consent"
    response_type: str = "code"

    # Additional parameters for the authorization URL
    extra_auth_params: Dict[str, str] = Field(default_factory=dict)


class ToolScopeMapping(BaseModel):
    """Mapping of tools to their required scopes for different providers."""

    tool_name: str
    provider_scopes: Dict[OAuthProvider, List[str]]
    description: Optional[str] = None


class OAuthProviderManager:
    """Manager for OAuth provider configurations and scope mappings."""

    def __init__(self):
        self._providers: Dict[OAuthProvider, OAuthProviderConfig] = {}
        self._tool_scopes: Dict[str, ToolScopeMapping] = {}
        self._initialize_default_providers()
        self._initialize_default_tool_scopes()

    def _initialize_default_providers(self):
        """Initialize default provider configurations."""

        # Google OAuth configuration
        self._providers[OAuthProvider.GOOGLE] = OAuthProviderConfig(
            provider=OAuthProvider.GOOGLE,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://accounts.google.com/o/oauth2/auth",
            token_url="https://oauth2.googleapis.com/token",
            user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
            revoke_url="https://oauth2.googleapis.com/revoke",
            access_type="offline",
            prompt="consent",
            extra_auth_params={},
        )

        # Microsoft OAuth configuration
        self._providers[OAuthProvider.MICROSOFT] = OAuthProviderConfig(
            provider=OAuthProvider.MICROSOFT,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
            token_url="https://login.microsoftonline.com/common/oauth2/v2.0/token",
            user_info_url="https://graph.microsoft.com/v1.0/me",
            revoke_url=None,
            extra_auth_params={},
        )

        # GitHub OAuth configuration
        self._providers[OAuthProvider.GITHUB] = OAuthProviderConfig(
            provider=OAuthProvider.GITHUB,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://github.com/login/oauth/authorize",
            token_url="https://github.com/login/oauth/access_token",
            user_info_url="https://api.github.com/user",
            revoke_url=None,
            extra_auth_params={},
        )

        # Zoho OAuth configuration
        self._providers[OAuthProvider.ZOHO] = OAuthProviderConfig(
            provider=OAuthProvider.ZOHO,
            client_id="",  # Will be set from environment
            client_secret="",  # Will be set from environment
            redirect_uri="",  # Will be set from environment
            auth_url="https://accounts.zoho.com/oauth/v2/auth",
            token_url="https://accounts.zoho.com/oauth/v2/token",
            user_info_url="https://www.zohoapis.com/crm/v6/users?type=CurrentUser",
            revoke_url="https://accounts.zoho.com/oauth/v2/token/revoke",
            extra_auth_params={"access_type": "offline", "prompt": "consent"},
        )

    def _initialize_default_tool_scopes(self):
        """Initialize default tool scope mappings."""

        # Google Calendar tool scopes
        self._tool_scopes["google_calendar"] = ToolScopeMapping(
            tool_name="google_calendar",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/calendar",
                    "openid",
                    "profile",
                    "email",
                ]
            },
            description="Google Calendar API access",
        )

        # Google Sheets tool scopes
        self._tool_scopes["google_sheets"] = ToolScopeMapping(
            tool_name="google_sheets",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/spreadsheets",
                    "openid",
                    "profile",
                    "email",
                ]
            },
            description="Google Sheets API access",
        )

        # Google Drive tool scopes
        self._tool_scopes["google_drive"] = ToolScopeMapping(
            tool_name="google_drive",
            provider_scopes={
                OAuthProvider.GOOGLE: [
                    "https://www.googleapis.com/auth/drive",
                    "openid",
                    "profile",
                    "email",
                ]
            },
            description="Google Drive API access",
        )

        # Microsoft Outlook Calendar tool scopes
        self._tool_scopes["microsoft_calendar"] = ToolScopeMapping(
            tool_name="microsoft_calendar",
            provider_scopes={
                OAuthProvider.MICROSOFT: [
                    "https://graph.microsoft.com/calendars.readwrite",
                    "openid",
                    "profile",
                    "email",
                ]
            },
            description="Microsoft Outlook Calendar API access",
        )

        # Microsoft OneDrive tool scopes
        self._tool_scopes["microsoft_onedrive"] = ToolScopeMapping(
            tool_name="microsoft_onedrive",
            provider_scopes={
                OAuthProvider.MICROSOFT: [
                    "https://graph.microsoft.com/files.readwrite",
                    "openid",
                    "profile",
                    "email",
                ]
            },
            description="Microsoft OneDrive API access",
        )

        # GitHub tool scopes
        self._tool_scopes["github_repos"] = ToolScopeMapping(
            tool_name="github_repos",
            provider_scopes={OAuthProvider.GITHUB: ["repo", "user:email"]},
            description="GitHub repository access",
        )

        # Jira tool scopes
        self._tool_scopes["jira"] = ToolScopeMapping(
            tool_name="jira",
            provider_scopes={
                OAuthProvider.JIRA: [
                    # --- Jira Platform API Scopes ---
                    "read:jira-user",
                    "read:jira-work",
                    "write:jira-work",
                    "manage:jira-user",
                    "manage:jira-project",
                    "manage:jira-configuration",
                    # --- Jira Software API Scopes (Full Management) ---
                    "read:board-scope:jira-software",  # Read board data and configuration
                    "write:board-scope:jira-software",  # Create and update boards
                    "read:sprint:jira-software",  # Read sprint information
                    "write:sprint:jira-software",  # Create and update sprints (e.g., change name/dates)
                    "delete:sprint:jira-software",  # Delete sprints
                    "manage:sprint:jira-software",  # Start/stop sprints, move issues between sprints/backlog
                    # # --- Confluence API Scopes ---
                    "read:page:confluence",
                    "read:space:confluence",
                    "write:page:confluence",
                    "read:content.all:confluence",
                    "read:content.permission:confluence",
                    "read:space.content:confluence",
                    "write:content:confluence",
                    "write:content.permission:confluence",
                    "write:space.content:confluence",
                    "search:confluence",
                    "read:comment:confluence",
                    "delete:page:confluence",
                    # --- Standard OAuth 2.0 Scope ---
                    "offline_access",
                ]
            },
            description="Jira access for issues and user management",
        )

        # Zoho tool scopes
        self._tool_scopes["zoho"] = ToolScopeMapping(
            tool_name="zoho",
            provider_scopes={
                OAuthProvider.ZOHO: [
                    "ZohoCRM.modules.ALL",
                    "ZohoCRM.users.READ",
                    "ZohoCRM.org.READ",
                    "ZohoCRM.settings.READ",
                ]
            },
            description="ZOHO API access for CRM management",
        )

    def get_provider_config(self, provider: OAuthProvider) -> Optional[OAuthProviderConfig]:
        """Get provider configuration by provider type."""
        return self._providers.get(provider)

    def get_tool_scopes(self, tool_name: str, provider: OAuthProvider) -> List[str]:
        """Get required scopes for a tool and provider."""
        tool_mapping = self._tool_scopes.get(tool_name)
        if not tool_mapping:
            return []

        return tool_mapping.provider_scopes.get(provider, [])

    def add_custom_provider(self, provider_config: OAuthProviderConfig):
        """Add a custom provider configuration."""
        self._providers[provider_config.provider] = provider_config

    def add_tool_scope_mapping(self, tool_mapping: ToolScopeMapping):
        """Add a custom tool scope mapping."""
        self._tool_scopes[tool_mapping.tool_name] = tool_mapping

    def get_supported_providers(self) -> List[OAuthProvider]:
        """Get list of supported providers."""
        return list(self._providers.keys())

    def get_supported_tools(self) -> List[str]:
        """Get list of supported tools."""
        return list(self._tool_scopes.keys())

    def update_provider_credentials(
        self, provider: OAuthProvider, client_id: str, client_secret: str, redirect_uri: str
    ):
        """Update provider credentials from environment variables."""
        if provider in self._providers:
            self._providers[provider].client_id = client_id
            self._providers[provider].client_secret = client_secret
            self._providers[provider].redirect_uri = redirect_uri

    def load_custom_providers_from_json(self, json_config: str):
        """Load custom provider configurations from JSON string."""
        try:
            custom_configs = json.loads(json_config)
            for config_data in custom_configs:
                provider_config = OAuthProviderConfig(**config_data)
                self.add_custom_provider(provider_config)
        except (json.JSONDecodeError, ValueError) as e:
            # Log error but don't fail initialization
            print(f"Error loading custom OAuth providers: {e}")


# Global instance
oauth_provider_manager = OAuthProviderManager()
