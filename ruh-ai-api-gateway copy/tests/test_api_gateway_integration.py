#!/usr/bin/env python3
"""
Integration test for the new API Gateway endpoints.
This test verifies that the API Gateway correctly communicates with the MCP service.
"""

import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock, patch

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.mcp_service import MCPServiceClient
from app.schemas.mcp import GetMCPByRepoRequest, GetMCPDeploymentStatusRequest
from app.grpc_ import mcp_pb2


class MockMCPResponse:
    """Mock MCP response for testing"""
    def __init__(self, success=True, message="Success", mcp_data=None):
        self.success = success
        self.message = message
        if mcp_data:
            self.mcp = Mock()
            for key, value in mcp_data.items():
                setattr(self.mcp, key, value)
        else:
            self.mcp = None


class MockDeploymentResponse:
    """Mock deployment response for testing"""
    def __init__(self, success=True, message="Success", deployments_data=None):
        self.success = success
        self.message = message
        self.deployments = []
        
        if deployments_data:
            for deployment_data in deployments_data:
                deployment = Mock()
                for key, value in deployment_data.items():
                    setattr(deployment, key, value)
                self.deployments.append(deployment)


async def test_mcp_service_client_get_mcp_by_repo():
    """Test the MCPServiceClient.getMCPByRepo method"""
    print("\n=== Testing MCPServiceClient.getMCPByRepo ===")
    
    # Mock the gRPC stub
    with patch('app.services.mcp_service.mcp_pb2_grpc.MCPServiceStub') as mock_stub_class:
        mock_stub = Mock()
        mock_stub_class.return_value = mock_stub
        
        # Create mock response
        mock_response = MockMCPResponse(
            success=True,
            message="MCP found successfully",
            mcp_data={
                'id': 'test-mcp-123',
                'name': 'Test MCP',
                'repo_name': 'test-repo',
                'git_user_name': 'test-user',
                'description': 'Test MCP for integration testing'
            }
        )
        
        mock_stub.getMCPByRepo.return_value = mock_response
        
        # Test the service client
        client = MCPServiceClient()
        
        try:
            response = await client.getMCPByRepo(
                repo_name="test-repo",
                git_user_name="test-user",
                user_id="test-user-123"
            )
            
            print(f"✅ Success: {response.success}")
            print(f"✅ Message: {response.message}")
            if hasattr(response, 'mcp') and response.mcp:
                print(f"✅ MCP ID: {response.mcp.id}")
                print(f"✅ MCP Name: {response.mcp.name}")
                print(f"✅ Repo Name: {response.mcp.repo_name}")
            
            # Verify the gRPC call was made correctly
            mock_stub.getMCPByRepo.assert_called_once()
            call_args = mock_stub.getMCPByRepo.call_args[0][0]
            assert call_args.repo_name == "test-repo"
            assert call_args.git_user_name == "test-user"
            assert call_args.user_id == "test-user-123"
            
            print("✅ gRPC call verification passed")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    return True


async def test_mcp_service_client_get_deployment_status():
    """Test the MCPServiceClient.getMCPDeploymentStatus method"""
    print("\n=== Testing MCPServiceClient.getMCPDeploymentStatus ===")
    
    # Mock the gRPC stub
    with patch('app.services.mcp_service.mcp_pb2_grpc.MCPServiceStub') as mock_stub_class:
        mock_stub = Mock()
        mock_stub_class.return_value = mock_stub
        
        # Create mock response with deployment data
        mock_response = MockDeploymentResponse(
            success=True,
            message="Retrieved 2 deployments",
            deployments_data=[
                {
                    'id': 'deployment-1',
                    'container_name': 'test-container-1',
                    'status': 'RUNNING',
                    'user_id': 'user-123',
                    'mcp_id': 'mcp-123',
                    'image_name': 'test-image:latest',
                    'created_at': '2024-01-01T00:00:00Z',
                    'updated_at': '2024-01-01T00:00:00Z'
                },
                {
                    'id': 'deployment-2',
                    'container_name': 'test-container-2',
                    'status': 'STOPPED',
                    'user_id': 'user-456',
                    'mcp_id': 'mcp-123',
                    'image_name': 'test-image:latest',
                    'created_at': '2024-01-01T00:00:00Z',
                    'updated_at': '2024-01-01T01:00:00Z'
                }
            ]
        )
        
        mock_stub.getMCPDeploymentStatus.return_value = mock_response
        
        # Test the service client
        client = MCPServiceClient()
        
        try:
            response = await client.getMCPDeploymentStatus(mcp_id="mcp-123")
            
            print(f"✅ Success: {response.success}")
            print(f"✅ Message: {response.message}")
            print(f"✅ Number of deployments: {len(response.deployments)}")
            
            for i, deployment in enumerate(response.deployments):
                print(f"✅ Deployment {i+1}:")
                print(f"   ID: {deployment.id}")
                print(f"   Container: {deployment.container_name}")
                print(f"   Status: {deployment.status}")
                print(f"   User: {deployment.user_id}")
            
            # Verify the gRPC call was made correctly
            mock_stub.getMCPDeploymentStatus.assert_called_once()
            call_args = mock_stub.getMCPDeploymentStatus.call_args[0][0]
            assert call_args.mcp_id == "mcp-123"
            
            print("✅ gRPC call verification passed")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    return True


def test_schema_validation():
    """Test the Pydantic schemas for the new endpoints"""
    print("\n=== Testing Schema Validation ===")
    
    try:
        # Test GetMCPByRepoRequest schema
        repo_request = GetMCPByRepoRequest(
            repo_name="test-repo",
            git_user_name="test-user"
        )
        print(f"✅ GetMCPByRepoRequest validation passed")
        print(f"   Repo Name: {repo_request.repo_name}")
        print(f"   Git User: {repo_request.git_user_name}")
        
        # Test GetMCPDeploymentStatusRequest schema
        deployment_request = GetMCPDeploymentStatusRequest(
            mcp_id="mcp-123"
        )
        print(f"✅ GetMCPDeploymentStatusRequest validation passed")
        print(f"   MCP ID: {deployment_request.mcp_id}")
        
        # Test optional fields
        repo_request_minimal = GetMCPByRepoRequest(repo_name="test-repo")
        print(f"✅ Optional field handling works")
        print(f"   Repo Name: {repo_request_minimal.repo_name}")
        print(f"   Git User: {repo_request_minimal.git_user_name}")
        
    except Exception as e:
        print(f"❌ Schema validation error: {e}")
        return False
    
    return True


async def main():
    """Main test function"""
    print("Starting API Gateway integration tests...")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Schema Validation", test_schema_validation),
        ("MCP Service Client - Get MCP by Repo", test_mcp_service_client_get_mcp_by_repo),
        ("MCP Service Client - Get Deployment Status", test_mcp_service_client_get_deployment_status),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The API Gateway integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print("\nNext steps:")
    print("1. Start the API Gateway server: poetry run uvicorn app.main:app --reload")
    print("2. Start the MCP service")
    print("3. Run the HTTP endpoint tests: python test_new_api_endpoints.py")


if __name__ == "__main__":
    asyncio.run(main())
