import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient

class TestAuthRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, mock_user_data, mock_grpc_stub):
        self.client = test_client
        self.user_data = mock_user_data
        self.grpc_stub = mock_grpc_stub

    @patch('app.services.user_service.UserServiceClient')
    def test_register_success(self, mock_user_service):
        # Arrange
        mock_user_service.return_value.register.return_value = Mock(
            success=True,
            message="User registered successfully"
        )

        # Act
        response = self.client.post(
            "/api/v1/auth/register",
            json=self.user_data
        )

        # Assert
        assert response.status_code == 200
        assert response.json()["success"] is True

    @patch('app.services.user_service.UserServiceClient')
    def test_login_success(self, mock_user_service):
        # Arrange
        mock_user_service.return_value.login.return_value = Mock(
            access_token="test_token",
            token_type="bearer"
        )

        # Act
        response = self.client.post(
            "/api/v1/auth/login",
            data={
                "username": self.user_data["email"],
                "password": self.user_data["password"]
            }
        )

        # Assert
        assert response.status_code == 200
        assert "access_token" in response.json()

    def test_login_invalid_credentials(self):
        response = self.client.post(
            "/api/v1/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "wrongpass"
            }
        )
        assert response.status_code == 401

    @patch('app.services.user_service.UserServiceClient')
    def test_verify_email_otp_success(self, mock_user_service):
        mock_user_service.return_value.verify_email_otp.return_value = Mock(
            success=True
        )
        
        response = self.client.post(
            "/api/v1/auth/verify-email-otp",
            json={
                "email": self.user_data["email"],
                "otp": "123456"
            }
        )
        assert response.status_code == 200

    @patch('app.services.user_service.UserServiceClient')
    def test_reset_password_request_success(self, mock_user_service):
        mock_user_service.return_value.reset_password_request.return_value = Mock(
            success=True
        )
        
        response = self.client.post(
            "/api/v1/auth/reset-password-request",
            json={"email": self.user_data["email"]}
        )
        assert response.status_code == 200
