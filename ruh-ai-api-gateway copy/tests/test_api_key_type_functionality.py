"""
Test API Key Type Functionality

This module tests the new API key type functionality including:
- Creating API keys with different types
- Listing API keys filtered by type
- Key generation with type prefix
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.schemas.user import APIKeyTypeEnum


class TestAPIKeyTypeFunctionality:
    """Test API key type functionality."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_user_service(self):
        """Mock user service client."""
        mock_service = AsyncMock()
        with patch('app.api.routers.api_key_routes.user_service', mock_service):
            yield mock_service

    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        return {
            "user_id": "test_user_123", 
            "role": "user",
            "organisation_id": "org_123"
        }

    @pytest.fixture
    def mock_role_required(self, mock_user):
        """Mock role_required dependency."""
        with patch('app.api.routers.api_key_routes.role_required') as mock_role:
            mock_role.return_value = lambda: mock_user
            yield mock_role

    def test_create_api_key_with_type_mcp(self, client, mock_user_service, mock_role_required):
        """Test creating an API key with MCP type."""
        # Mock the gRPC response
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Public key created successfully"
        mock_response.api_key.id = "key_123"
        mock_response.api_key.name = "Test MCP Key"
        mock_response.api_key.description = "Test description"
        mock_response.api_key.type = "mcp"
        mock_response.api_key.is_active = True
        mock_response.api_key.user_id = "test_user_123"
        mock_response.api_key.created_at = "2023-01-01T00:00:00"
        mock_response.api_key.public_key = "ruh-mcp-1-abcd1234567890abcdef1234567890ab-test_user_123"
        mock_response.api_key.organization_id = "org_123"
        mock_response.api_key.counter = 1

        mock_user_service.create_public_key.return_value = mock_response

        # Test data
        test_data = {
            "name": "Test MCP Key",
            "description": "Test description",
            "type": "mcp"
        }

        # Make request
        response = client.post("/api-keys/create-key", json=test_data)

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Public key created successfully"
        assert data["api_key"]["type"] == "mcp"
        assert "ruh-mcp-" in data["api_key"]["public_key"]

        # Verify service was called with correct parameters
        mock_user_service.create_public_key.assert_called_once_with(
            user_id="test_user_123",
            name="Test MCP Key",
            organization_id="org_123",
            key_type="mcp",
            description="Test description"
        )

    def test_create_api_key_with_type_workflow(self, client, mock_user_service, mock_role_required):
        """Test creating an API key with workflow type."""
        # Mock the gRPC response
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Public key created successfully"
        mock_response.api_key.id = "key_124"
        mock_response.api_key.name = "Test Workflow Key"
        mock_response.api_key.description = "Test workflow description"
        mock_response.api_key.type = "workflow"
        mock_response.api_key.is_active = True
        mock_response.api_key.user_id = "test_user_123"
        mock_response.api_key.created_at = "2023-01-01T00:00:00"
        mock_response.api_key.public_key = "ruh-workflow-2-efgh1234567890abcdef1234567890ab-test_user_123"
        mock_response.api_key.organization_id = "org_123"
        mock_response.api_key.counter = 2

        mock_user_service.create_public_key.return_value = mock_response

        # Test data
        test_data = {
            "name": "Test Workflow Key",
            "description": "Test workflow description",
            "type": "workflow"
        }

        # Make request
        response = client.post("/api-keys/create-key", json=test_data)

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["api_key"]["type"] == "workflow"
        assert "ruh-workflow-" in data["api_key"]["public_key"]

    def test_list_api_keys_filtered_by_type(self, client, mock_user_service, mock_role_required):
        """Test listing API keys filtered by type."""
        # Mock the gRPC response
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "API keys retrieved successfully"
        
        # Create mock API keys
        mock_key1 = MagicMock()
        mock_key1.id = "key_123"
        mock_key1.name = "MCP Key 1"
        mock_key1.description = "MCP description"
        mock_key1.type = "mcp"
        mock_key1.is_active = True
        mock_key1.user_id = "test_user_123"
        mock_key1.created_at = "2023-01-01T00:00:00"
        mock_key1.organization_id = "org_123"
        mock_key1.counter = 1

        mock_key2 = MagicMock()
        mock_key2.id = "key_124"
        mock_key2.name = "MCP Key 2"
        mock_key2.description = "Another MCP description"
        mock_key2.type = "mcp"
        mock_key2.is_active = True
        mock_key2.user_id = "test_user_123"
        mock_key2.created_at = "2023-01-01T01:00:00"
        mock_key2.organization_id = "org_123"
        mock_key2.counter = 2

        mock_response.api_keys = [mock_key1, mock_key2]
        mock_user_service.list_public_keys.return_value = mock_response

        # Make request with type filter
        response = client.get("/api-keys/list?type=mcp")

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert len(data["api_keys"]) == 2
        assert all(key["type"] == "mcp" for key in data["api_keys"])

        # Verify service was called with correct parameters
        mock_user_service.list_public_keys.assert_called_once_with(
            user_id="test_user_123",
            organization_id=None,
            key_type="mcp"
        )

    def test_api_key_type_enum_validation(self, client, mock_role_required):
        """Test that invalid API key types are rejected."""
        # Test data with invalid type
        test_data = {
            "name": "Test Key",
            "description": "Test description",
            "type": "invalid_type"
        }

        # Make request
        response = client.post("/api-keys/create-key", json=test_data)

        # Should return validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_all_valid_api_key_types(self):
        """Test that all expected API key types are valid."""
        expected_types = ["mcp", "workflow", "knowledge", "voice"]
        
        for type_value in expected_types:
            assert hasattr(APIKeyTypeEnum, type_value.upper())
            assert getattr(APIKeyTypeEnum, type_value.upper()).value == type_value
