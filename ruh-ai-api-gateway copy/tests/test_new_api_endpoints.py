#!/usr/bin/env python3
"""
Test script for the new API Gateway endpoints:
1. POST /mcps/repo/get - Get MCP by repo_name and optional git_user_name
2. GET /mcps/{mcp_id}/deployment-status - Get deployment status for an MCP
"""

import requests
import json
from typing import Optional


class APIGatewayTester:
    def __init__(self, base_url: str = "http://localhost:8000", mcp_proxy_auth_key: Optional[str] = None):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json"
        }
        if mcp_proxy_auth_key:
            self.headers["X-MCP-Proxy-Server-Auth-Key"] = mcp_proxy_auth_key

    def test_get_mcp_by_repo(self, repo_name: str, git_user_name: Optional[str] = None):
        """Test the POST /mcps/repo/get endpoint"""
        url = f"{self.base_url}/mcps/repo/get"
        
        payload = {
            "repo_name": repo_name
        }
        
        if git_user_name:
            payload["git_user_name"] = git_user_name
        
        print(f"\n=== Testing GET MCP by Repo ===")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = requests.post(url, json=payload, headers=self.headers)
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Success: {data.get('success')}")
                print(f"Message: {data.get('message')}")
                
                if data.get('mcp'):
                    mcp = data['mcp']
                    print(f"MCP Found:")
                    print(f"  ID: {mcp.get('id')}")
                    print(f"  Name: {mcp.get('name')}")
                    print(f"  Repo Name: {mcp.get('repo_name')}")
                    print(f"  Git User: {mcp.get('git_user_name')}")
                    print(f"  Description: {mcp.get('description')}")
                    print(f"  Status: {mcp.get('status')}")
                    print(f"  Visibility: {mcp.get('visibility')}")
            else:
                print(f"Error Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request Error: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON Decode Error: {e}")
            print(f"Raw Response: {response.text}")

    def test_get_mcp_deployment_status(self, container_name: str):
        """Test the GET /mcps/deployment-status/{container_name} endpoint"""
        url = f"{self.base_url}/mcps/deployment-status/{container_name}"

        print(f"\n=== Testing GET MCP Deployment Status ===")
        print(f"URL: {url}")
        print(f"Container Name: {container_name}")

        try:
            response = requests.get(url, headers=self.headers)

            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")

            if response.status_code == 200:
                data = response.json()
                print(f"Success: {data.get('success')}")
                print(f"Message: {data.get('message')}")

                deployment = data.get('deployment')
                if deployment:
                    print(f"Latest Deployment:")
                    print(f"  ID: {deployment.get('id')}")
                    print(f"  Container Name: {deployment.get('container_name')}")
                    print(f"  Status: {deployment.get('status')}")
                    print(f"  User ID: {deployment.get('user_id')}")
                    print(f"  MCP ID: {deployment.get('mcp_id')}")
                    print(f"  Image Name: {deployment.get('image_name')}")
                    print(f"  Created At: {deployment.get('created_at')}")
                    print(f"  Updated At: {deployment.get('updated_at')}")
                else:
                    print("No deployment found")
            else:
                print(f"Error Response: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"Request Error: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON Decode Error: {e}")
            print(f"Raw Response: {response.text}")

    def test_health_check(self):
        """Test if the API Gateway is running"""
        url = f"{self.base_url}/health"
        
        print(f"\n=== Testing Health Check ===")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ API Gateway is running")
                return True
            else:
                print("❌ API Gateway health check failed")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to API Gateway: {e}")
            return False


def main():
    """Main test function"""
    print("Testing new API Gateway endpoints...")
    print("=" * 60)
    
    # Initialize tester
    tester = APIGatewayTester()
    
    # Test health check first
    if not tester.test_health_check():
        print("Cannot proceed with tests - API Gateway is not accessible")
        return
    
    # Test cases for getMCPByRepo
    print("\n" + "=" * 60)
    print("TESTING GET MCP BY REPO ENDPOINT")
    print("=" * 60)
    
    # Test 1: Get MCP by repo name only
    tester.test_get_mcp_by_repo("test-repo-123")
    
    # Test 2: Get MCP by repo name and git username
    tester.test_get_mcp_by_repo("test-repo-123", "test-user")
    
    # Test 3: Try to get non-existent MCP
    tester.test_get_mcp_by_repo("non-existent-repo")
    
    # Test cases for getMCPDeploymentStatus
    print("\n" + "=" * 60)
    print("TESTING GET MCP DEPLOYMENT STATUS ENDPOINT")
    print("=" * 60)

    # Test 1: Get deployment status for existing container (replace with actual container name)
    tester.test_get_mcp_deployment_status("sample-container-name")

    # Test 2: Try to get deployment status for non-existent container
    tester.test_get_mcp_deployment_status("non-existent-container")
    
    print("\n" + "=" * 60)
    print("Testing completed!")
    print("=" * 60)
    
    print("\nNOTE: To test with real data:")
    print("1. Make sure the MCP service is running")
    print("2. Replace 'sample-mcp-id' with actual MCP IDs from your database")
    print("3. Replace 'test-repo-123' with actual repository names")
    print("4. Set the MCP_PROXY_SERVER_AUTH_KEY environment variable")
    print("5. Pass the auth key to the tester: APIGatewayTester(mcp_proxy_auth_key='your-key')")


if __name__ == "__main__":
    main()
