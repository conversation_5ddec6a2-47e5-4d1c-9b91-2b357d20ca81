#!/usr/bin/env python3
"""
Test script to verify that department_id and organisation_id are properly passed 
through the entire marketplace agent flow.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from app.schemas.marketplace import UseMarketplaceAgentRequest
from app.utils.add_agent_to_workspace import add_agent_to_workspace
from app.services.agent_service import AgentServiceClient


class TestMarketplaceDepartmentOrgFlow:
    """Test class for department_id and organisation_id flow in marketplace functionality."""

    @pytest.fixture
    def mock_agent_service_response(self):
        """Mock response from agent service."""
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Agent successfully added to workspace"
        mock_response.use_count = 1
        mock_response.agent_id = "new-agent-123"
        return mock_response

    @pytest.fixture
    def mock_workflow_service_response(self):
        """Mock response from workflow service."""
        mock_response = Mock()
        mock_response.success = True
        mock_response.workflow_id = "workflow-123"
        return mock_response

    @pytest.fixture
    def mock_mcp_service_response(self):
        """Mock response from MCP service."""
        mock_response = Mock()
        mock_response.success = True
        mock_response.mcp_id = "mcp-123"
        return mock_response

    @pytest.mark.asyncio
    async def test_add_agent_to_workspace_with_department_and_org(
        self, 
        mock_agent_service_response,
        mock_workflow_service_response,
        mock_mcp_service_response
    ):
        """Test that add_agent_to_workspace passes department_id and organisation_id to agent service."""
        
        # Test data
        agent_id = "marketplace-agent-123"
        user_id = "user-456"
        workflow_ids = ["workflow-789"]
        mcp_ids = ["mcp-101"]
        department_id = "sales"
        organisation_id = "org-202"

        # Mock the services
        with patch('app.utils.add_agent_to_workspace.agent_service') as mock_agent_service, \
             patch('app.utils.add_agent_to_workspace.workflow_service') as mock_workflow_service, \
             patch('app.utils.add_agent_to_workspace.mcp_service') as mock_mcp_service:
            
            # Setup mocks
            mock_workflow_service.use_workflow = AsyncMock(return_value=mock_workflow_service_response)
            mock_mcp_service.use_mcp = AsyncMock(return_value=mock_mcp_service_response)
            mock_agent_service.use_agent = AsyncMock(return_value=mock_agent_service_response)

            # Call the function
            result = await add_agent_to_workspace(
                agent_id=agent_id,
                user_id=user_id,
                workflow_ids=workflow_ids,
                mcp_ids=mcp_ids,
                department_id=department_id,
                organisation_id=organisation_id
            )

            # Verify agent_service.use_agent was called with correct parameters
            mock_agent_service.use_agent.assert_called_once_with(
                agent_id=agent_id,
                user_id=user_id,
                workflow_ids=["workflow-123"],  # Should be the processed workflow IDs
                mcp_ids=["mcp-123"],  # Should be the processed MCP IDs
                department_id=department_id,
                organisation_id=organisation_id
            )

            # Verify the result
            assert result["success"] is True
            assert result["agent_id"] == "new-agent-123"
            assert result["message"] == "Agent successfully added to workspace"

    @pytest.mark.asyncio
    async def test_add_agent_to_workspace_without_department_and_org(
        self, 
        mock_agent_service_response
    ):
        """Test that add_agent_to_workspace works when department_id and organisation_id are None."""
        
        # Test data
        agent_id = "marketplace-agent-123"
        user_id = "user-456"

        # Mock the services
        with patch('app.utils.add_agent_to_workspace.agent_service') as mock_agent_service, \
             patch('app.utils.add_agent_to_workspace.workflow_service'), \
             patch('app.utils.add_agent_to_workspace.mcp_service'):
            
            # Setup mocks
            mock_agent_service.use_agent = AsyncMock(return_value=mock_agent_service_response)

            # Call the function without department_id and organisation_id
            result = await add_agent_to_workspace(
                agent_id=agent_id,
                user_id=user_id,
                workflow_ids=[],
                mcp_ids=[],
                department_id=None,
                organisation_id=None
            )

            # Verify agent_service.use_agent was called with None values
            mock_agent_service.use_agent.assert_called_once_with(
                agent_id=agent_id,
                user_id=user_id,
                workflow_ids=[],
                mcp_ids=[],
                department_id=None,
                organisation_id=None
            )

            # Verify the result
            assert result["success"] is True

    def test_use_marketplace_agent_request_schema(self):
        """Test that UseMarketplaceAgentRequest schema includes department_id field."""
        
        # Test with department_id
        request_data = {
            "agent_id": "agent-123",
            "mcp_ids": ["mcp-1", "mcp-2"],
            "workflow_ids": ["workflow-1"],
            "department_id": "engineering"
        }
        
        request = UseMarketplaceAgentRequest(**request_data)
        
        assert request.agent_id == "agent-123"
        assert request.mcp_ids == ["mcp-1", "mcp-2"]
        assert request.workflow_ids == ["workflow-1"]
        assert request.department_id == "engineering"

    def test_use_marketplace_agent_request_schema_without_department(self):
        """Test that UseMarketplaceAgentRequest schema works without department_id."""
        
        # Test without department_id
        request_data = {
            "agent_id": "agent-123",
            "mcp_ids": ["mcp-1"],
            "workflow_ids": ["workflow-1"]
        }
        
        request = UseMarketplaceAgentRequest(**request_data)
        
        assert request.agent_id == "agent-123"
        assert request.mcp_ids == ["mcp-1"]
        assert request.workflow_ids == ["workflow-1"]
        assert request.department_id is None  # Should default to None


if __name__ == "__main__":
    print("🧪 Testing Department ID and Organisation ID Flow")
    print("=" * 60)
    
    # Run the tests
    pytest.main([__file__, "-v"])
