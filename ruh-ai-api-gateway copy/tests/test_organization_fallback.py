"""
Test for organization fallback mechanism in login endpoints.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from app.main import app
from app.grpc_ import user_pb2, organisation_pb2


class TestOrganizationFallback:
    """Test organization fallback functionality in login endpoints."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_user_service(self):
        """Mock user service."""
        with patch('app.api.routers.user_routes.user_service') as mock:
            yield mock

    @pytest.fixture
    def mock_org_service(self):
        """Mock organization service."""
        with patch('app.api.routers.user_routes.org_service') as mock:
            yield mock

    def test_login_with_null_organization_id_fallback_success(self, client, mock_user_service, mock_org_service):
        """Test login with null organizationId triggers fallback and succeeds."""
        # Mock login response with null organizationId
        mock_login_response = MagicMock()
        mock_login_response.success = True
        mock_login_response.message = "Login successful"
        mock_login_response.accessToken = "original_access_token"
        mock_login_response.refreshToken = "original_refresh_token"
        mock_login_response.accessTokenAge = 3600
        mock_login_response.refreshTokenAge = 604800

        # Mock user with null organizationId
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_user.organizationId = None
        mock_login_response.user = mock_user

        mock_user_service.login.return_value = mock_login_response

        # Mock organization response
        mock_org_response = MagicMock()
        mock_org_response.success = True
        mock_org_response.organisations = []

        # Create mock organization
        mock_organisation = MagicMock()
        mock_organisation.organisation.id = "org_123"
        mock_org_response.organisations = [mock_organisation]

        mock_org_service.get_user_organisations.return_value = mock_org_response

        # Mock organization tokens response
        mock_org_tokens_response = MagicMock()
        mock_org_tokens_response.success = True
        mock_org_tokens_response.accessToken = "new_org_access_token"
        mock_org_tokens_response.refreshToken = "new_org_refresh_token"

        mock_user_service.generate_organisation_tokens.return_value = mock_org_tokens_response

        # Test login request
        response = client.post("/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })

        # Debug: Print response details
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content}")

        # Assertions
        assert response.status_code == 200
        response_data = response.json()

        # Should use new organization tokens
        assert response_data["access_token"] == "new_org_access_token"
        assert response_data["refresh_token"] == "new_org_refresh_token"
        assert response_data["success"] is True

        # Verify service calls
        mock_user_service.login.assert_called_once()
        mock_org_service.get_user_organisations.assert_called_once_with("<EMAIL>")
        mock_user_service.generate_organisation_tokens.assert_called_once_with(
            organisation_id="org_123",
            user_email="<EMAIL>"
        )

    def test_login_with_organization_id_no_fallback(self, client, mock_user_service, mock_org_service):
        """Test login with existing organizationId does not trigger fallback."""
        # Mock login response with existing organizationId
        mock_login_response = MagicMock()
        mock_login_response.success = True
        mock_login_response.message = "Login successful"
        mock_login_response.accessToken = "original_access_token"
        mock_login_response.refreshToken = "original_refresh_token"
        mock_login_response.accessTokenAge = 3600
        mock_login_response.refreshTokenAge = 604800
        
        # Mock user with existing organizationId
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_user.organizationId = "existing_org_123"
        mock_login_response.user = mock_user
        
        mock_user_service.login.return_value = mock_login_response

        # Test login request
        response = client.post("/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })

        # Assertions
        assert response.status_code == 200
        response_data = response.json()
        
        # Should use original tokens
        assert response_data["access_token"] == "original_access_token"
        assert response_data["refresh_token"] == "original_refresh_token"
        assert response_data["success"] is True

        # Verify fallback was not triggered
        mock_org_service.get_user_organisations.assert_not_called()
        mock_user_service.generate_organisation_tokens.assert_not_called()

    def test_login_fallback_no_organizations_found(self, client, mock_user_service, mock_org_service):
        """Test login fallback when no organizations are found."""
        # Mock login response with null organizationId
        mock_login_response = MagicMock()
        mock_login_response.success = True
        mock_login_response.message = "Login successful"
        mock_login_response.accessToken = "original_access_token"
        mock_login_response.refreshToken = "original_refresh_token"
        mock_login_response.accessTokenAge = 3600
        mock_login_response.refreshTokenAge = 604800
        
        # Mock user with null organizationId
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_user.organizationId = None
        mock_login_response.user = mock_user
        
        mock_user_service.login.return_value = mock_login_response

        # Mock organization response with no organizations
        mock_org_response = MagicMock()
        mock_org_response.success = True
        mock_org_response.organisations = []
        
        mock_org_service.get_user_organisations.return_value = mock_org_response

        # Test login request
        response = client.post("/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })

        # Assertions
        assert response.status_code == 200
        response_data = response.json()
        
        # Should use original tokens since no organizations found
        assert response_data["access_token"] == "original_access_token"
        assert response_data["refresh_token"] == "original_refresh_token"
        assert response_data["success"] is True

        # Verify organization service was called but token generation was not
        mock_org_service.get_user_organisations.assert_called_once_with("<EMAIL>")
        mock_user_service.generate_organisation_tokens.assert_not_called()

    def test_login_fallback_organization_token_generation_fails(self, client, mock_user_service, mock_org_service):
        """Test login fallback when organization token generation fails."""
        # Mock login response with null organizationId
        mock_login_response = MagicMock()
        mock_login_response.success = True
        mock_login_response.message = "Login successful"
        mock_login_response.accessToken = "original_access_token"
        mock_login_response.refreshToken = "original_refresh_token"
        mock_login_response.accessTokenAge = 3600
        mock_login_response.refreshTokenAge = 604800
        
        # Mock user with null organizationId
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_user.organizationId = None
        mock_login_response.user = mock_user
        
        mock_user_service.login.return_value = mock_login_response

        # Mock organization response
        mock_org_response = MagicMock()
        mock_org_response.success = True
        mock_organisation = MagicMock()
        mock_organisation.organisation.id = "org_123"
        mock_org_response.organisations = [mock_organisation]
        
        mock_org_service.get_user_organisations.return_value = mock_org_response

        # Mock organization tokens response failure
        mock_org_tokens_response = MagicMock()
        mock_org_tokens_response.success = False
        mock_org_tokens_response.message = "Token generation failed"
        
        mock_user_service.generate_organisation_tokens.return_value = mock_org_tokens_response

        # Test login request
        response = client.post("/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })

        # Assertions
        assert response.status_code == 200
        response_data = response.json()
        
        # Should use original tokens since token generation failed
        assert response_data["access_token"] == "original_access_token"
        assert response_data["refresh_token"] == "original_refresh_token"
        assert response_data["success"] is True

        # Verify all services were called
        mock_org_service.get_user_organisations.assert_called_once_with("<EMAIL>")
        mock_user_service.generate_organisation_tokens.assert_called_once_with(
            organisation_id="org_123",
            user_email="<EMAIL>"
        )
