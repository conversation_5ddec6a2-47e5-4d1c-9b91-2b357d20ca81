#!/usr/bin/env python3
"""
Basic test script to verify route implementation without external dependencies.
This script tests the route structure and basic functionality.
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_route_imports():
    """Test that route modules can be imported and have correct structure."""
    print("🧪 Testing route imports...")

    try:
        # Test schema imports
        from app.schemas.analytics import (
            TrackEventRequest,
            ServiceMetricsResponse,
            DashboardOverviewResponse,
        )
        from app.schemas.application import (
            ApplicationCreate,
            ApplicationResponse,
            PaginatedApplicationResponse,
        )

        print("✅ All schemas imported successfully")

        # Test that we can create model instances
        event_request = TrackEventRequest(
            event_type="usage", service_type="agent", entity_id="test_123", user_id="user_123"
        )
        print(f"✅ Created TrackEventRequest: {event_request.event_type}")

        app_create = ApplicationCreate(
            user_id="user_123", name="Test App", description="Test application"
        )
        print(f"✅ Created ApplicationCreate: {app_create.name}")

    except Exception as e:
        print(f"❌ Schema import failed: {e}")
        return False

    return True


def test_route_structure():
    """Test route file structure without executing dependencies."""
    print("\n🧪 Testing route structure...")

    try:
        import ast

        # Parse analytics routes
        with open("app/api/routers/analytics_routes.py", "r") as f:
            analytics_code = f.read()

        analytics_tree = ast.parse(analytics_code)
        analytics_functions = [
            node.name
            for node in ast.walk(analytics_tree)
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))
        ]

        print(f"✅ Analytics routes has {len(analytics_functions)} functions")

        # Parse application routes
        with open("app/api/routers/application_routes.py", "r") as f:
            application_code = f.read()

        application_tree = ast.parse(application_code)
        application_functions = [
            node.name
            for node in ast.walk(application_tree)
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))
        ]

        print(f"✅ Application routes has {len(application_functions)} functions")

        # Check for expected route functions
        expected_analytics_routes = [
            "track_event",
            "get_service_metrics",
            "get_user_activity",
            "get_overview_analytics",
            "track_activation",
            "create_webhook",
        ]

        expected_application_routes = [
            "create_application",
            "get_applications",
            "get_application",
            "update_application",
            "delete_application",
        ]

        for route in expected_analytics_routes:
            if route in analytics_functions:
                print(f"✅ Found analytics route: {route}")
            else:
                print(f"⚠️  Missing analytics route: {route}")

        for route in expected_application_routes:
            if route in application_functions:
                print(f"✅ Found application route: {route}")
            else:
                print(f"⚠️  Missing application route: {route}")

    except Exception as e:
        print(f"❌ Route structure test failed: {e}")
        return False

    return True


def test_enum_values():
    """Test that enums have expected values."""
    print("\n🧪 Testing enum values...")

    try:
        from app.schemas.analytics import EventTypeEnum, ServiceTypeEnum
        from app.schemas.application import ApplicationStatusEnum

        # Test analytics enums
        assert "usage" in [e.value for e in EventTypeEnum]
        assert "agent" in [e.value for e in ServiceTypeEnum]
        print("✅ Analytics enums have expected values")

        # Test application enums
        assert "active" in [e.value for e in ApplicationStatusEnum]
        assert "inactive" in [e.value for e in ApplicationStatusEnum]
        print("✅ Application enums have expected values")

    except Exception as e:
        print(f"❌ Enum test failed: {e}")
        return False

    return True


def test_model_validation():
    """Test Pydantic model validation."""
    print("\n🧪 Testing model validation...")

    try:
        from app.schemas.analytics import TrackEventRequest
        from app.schemas.application import ApplicationCreate

        # Test valid models
        valid_event = TrackEventRequest(
            event_type="usage", service_type="agent", entity_id="test_123", user_id="user_123"
        )
        print("✅ Valid analytics model created")

        valid_app = ApplicationCreate(
            user_id="user_123", name="Test App", description="Test description"
        )
        print("✅ Valid application model created")

        # Test validation errors
        try:
            invalid_event = TrackEventRequest(
                event_type="invalid_type",  # This should fail
                service_type="agent",
                entity_id="test_123",
                user_id="user_123",
            )
            print("⚠️  Expected validation error for invalid event type")
        except Exception:
            print("✅ Validation correctly rejected invalid event type")

    except Exception as e:
        print(f"❌ Model validation test failed: {e}")
        return False

    return True


def main():
    """Run all tests."""
    print("🚀 Starting basic route tests...\n")

    tests = [test_route_imports, test_route_structure, test_enum_values, test_model_validation]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Routes are ready for deployment.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
