"""
Simple test to verify is_changes_marketplace service methods work correctly.
"""

import sys
import os
from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

# Add the current directory to the path so we can import the app modules
sys.path.append(os.getcwd())

from app.services.workflow_functions import WorkflowFunctions
from app.models.workflow import Workflow
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum


def test_pull_updates_from_source():
    """Test the pullUpdatesFromSource method"""
    
    workflow_service = WorkflowFunctions()
    
    with patch.object(workflow_service, 'get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Create mock cloned workflow
        cloned_workflow = Workflow(
            id="cloned-workflow-id",
            name="Cloned Workflow",
            description="Original description",
            workflow_url="http://example.com/workflow",
            builder_url="http://example.com/builder",
            owner_id="user-123",
            user_ids=["user-123"],
            owner_type="user",
            workflow_template_id="source-workflow-id",
            template_owner_id="source-owner-id",
            is_imported=True,
            visibility=WorkflowVisibilityEnum.PRIVATE,
            status=WorkflowStatusEnum.ACTIVE,
            is_customizable=True,
            is_changes_marketplace=True,  # Has updates available
            auto_version_on_update=False,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        # Create mock source workflow
        source_workflow = Workflow(
            id="source-workflow-id",
            name="Source Workflow",
            description="Updated description",  # This is the update
            workflow_url="http://example.com/workflow-updated",
            builder_url="http://example.com/builder-updated",
            owner_id="source-owner-id",
            user_ids=["source-owner-id"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PUBLIC,
            status=WorkflowStatusEnum.ACTIVE,
            is_customizable=True,
            is_changes_marketplace=False,
            auto_version_on_update=False,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        # Mock database queries
        def mock_query_side_effect(*args):
            query_mock = MagicMock()
            if args[0] == Workflow:
                if not hasattr(mock_query_side_effect, 'call_count'):
                    mock_query_side_effect.call_count = 0
                mock_query_side_effect.call_count += 1
                
                if mock_query_side_effect.call_count == 1:
                    query_mock.filter().first.return_value = cloned_workflow
                else:
                    query_mock.filter().first.return_value = source_workflow
            return query_mock
        
        mock_db.query.side_effect = mock_query_side_effect
        
        # Mock the _workflow_to_proto method
        with patch.object(workflow_service, '_workflow_to_proto') as mock_to_proto:
            mock_to_proto.return_value = workflow_pb2.Workflow(
                id="cloned-workflow-id",
                name="Cloned Workflow",
                description="Updated description"
            )
            
            # Create request
            request = workflow_pb2.PullUpdatesFromSourceRequest(
                workflow_id="cloned-workflow-id",
                user_id="user-123"
            )
            
            # Call the method
            response = workflow_service.pullUpdatesFromSource(request, MagicMock())
            
            # Verify response
            assert response.success == True
            assert "Successfully pulled updates" in response.message
            
            # Verify that the cloned workflow was updated
            assert cloned_workflow.description == "Updated description"
            assert cloned_workflow.workflow_url == "http://example.com/workflow-updated"
            assert cloned_workflow.builder_url == "http://example.com/builder-updated"
            assert cloned_workflow.is_changes_marketplace == False  # Reset to False
            
            print("✅ pullUpdatesFromSource method works correctly")


def test_check_for_updates():
    """Test the checkForUpdates method"""
    
    workflow_service = WorkflowFunctions()
    
    with patch.object(workflow_service, 'get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock cloned workflow with updates available
        cloned_workflow = Workflow(
            id="cloned-workflow-id",
            name="Cloned Workflow",
            description="Original description",
            owner_id="user-123",
            workflow_template_id="source-workflow-id",
            is_imported=True,
            is_changes_marketplace=True,  # Has updates available
            updated_at=datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        )
        
        # Create mock source workflow
        source_workflow = Workflow(
            id="source-workflow-id",
            name="Source Workflow",
            description="Updated description",
            updated_at=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc)
        )
        
        # Mock database queries
        def mock_query_side_effect(*args):
            query_mock = MagicMock()
            if args[0] == Workflow:
                if not hasattr(mock_query_side_effect, 'call_count'):
                    mock_query_side_effect.call_count = 0
                mock_query_side_effect.call_count += 1
                
                if mock_query_side_effect.call_count == 1:
                    query_mock.filter().first.return_value = cloned_workflow
                else:
                    query_mock.filter().first.return_value = source_workflow
            return query_mock
        
        mock_db.query.side_effect = mock_query_side_effect
        
        # Create request
        request = workflow_pb2.CheckForUpdatesRequest(
            workflow_id="cloned-workflow-id",
            user_id="user-123"
        )
        
        # Call the method
        response = workflow_service.checkForUpdates(request, MagicMock())
        
        # Verify response
        assert response.success == True
        assert response.has_updates == True
        assert response.source_workflow_id == "source-workflow-id"
        assert "Updates available" in response.message
        
        print("✅ checkForUpdates method works correctly")


def test_workflow_change_detection():
    """Test the _detect_workflow_changes method"""
    
    workflow_service = WorkflowFunctions()
    
    # Test 1: Timestamp-based change detection
    source_workflow = Workflow(
        id="source-id",
        description="Updated description",
        updated_at=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc)
    )
    
    derived_workflow = Workflow(
        id="derived-id", 
        description="Original description",
        updated_at=datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
    )
    
    # Should detect changes due to timestamp difference
    has_changes = workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
    assert has_changes == True
    print("✅ Change detection correctly identifies timestamp differences")
    
    # Test 2: Content-based change detection
    source_workflow.updated_at = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)  # Same timestamp
    source_workflow.description = "Different description"  # But different content
    
    has_changes = workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
    assert has_changes == True
    print("✅ Change detection correctly identifies content differences")
    
    # Test 3: No changes scenario
    source_workflow.description = "Original description"  # Same content
    
    has_changes = workflow_service._detect_workflow_changes(derived_workflow, source_workflow)
    assert has_changes == False
    print("✅ Change detection correctly identifies no changes")


def test_content_hash_generation():
    """Test the _generate_content_hash method"""
    
    workflow_service = WorkflowFunctions()
    
    # Create two identical workflows
    workflow1 = Workflow(
        id="workflow-1",
        description="Test description",
        workflow_url="http://example.com/workflow",
        builder_url="http://example.com/builder",
        start_nodes=["node1", "node2"],
        category=None,
        tags=["tag1", "tag2"]
    )
    
    workflow2 = Workflow(
        id="workflow-2",  # Different ID
        description="Test description",  # Same content
        workflow_url="http://example.com/workflow",
        builder_url="http://example.com/builder",
        start_nodes=["node1", "node2"],
        category=None,
        tags=["tag1", "tag2"]
    )
    
    # Generate hashes
    hash1 = workflow_service._generate_content_hash(workflow1)
    hash2 = workflow_service._generate_content_hash(workflow2)
    
    # Should be the same since content is identical
    assert hash1 == hash2
    print("✅ Content hash generation works correctly for identical content")
    
    # Change content and verify hash changes
    workflow2.description = "Different description"
    hash3 = workflow_service._generate_content_hash(workflow2)
    
    assert hash1 != hash3
    print("✅ Content hash changes when content changes")


if __name__ == "__main__":
    print("Testing is_changes_marketplace service methods...")
    test_pull_updates_from_source()
    test_check_for_updates()
    test_workflow_change_detection()
    test_content_hash_generation()
    print("🎉 All service method tests passed!")
