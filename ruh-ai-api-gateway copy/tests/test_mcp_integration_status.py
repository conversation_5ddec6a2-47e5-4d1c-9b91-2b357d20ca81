#!/usr/bin/env python3
"""
Test for MCP endpoints with integration status functionality.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.schemas.mcp import MCPInDB


class TestMCPIntegrationStatus:
    """Test cases for MCP endpoints with integration status."""

    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        self.test_user_id = "test_user_123"
        
        # Mock JWT token payload
        self.mock_user_context = {
            "user_id": self.test_user_id,
            "email": "<EMAIL>",
            "role": "user",
            "name": "Test User"
        }
        
        # Mock MCP data with integrations
        self.mock_mcp_data = {
            "id": "mcp_123",
            "name": "Test MCP",
            "description": "Test MCP Description",
            "owner_id": self.test_user_id,
            "owner_type": "user",
            "visibility": "private",
            "status": "active",
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-01T00:00:00Z",
            "integrations": ["integration_1", "integration_2"]
        }

    @patch('app.api.routers.mcp_routes.get_auth_service_client')
    @patch('app.api.routers.mcp_routes.mcp_service')
    @patch('app.api.routers.mcp_routes.role_required')
    def test_list_mcps_with_integration_status(self, mock_role_required, mock_mcp_service, mock_get_auth_service):
        """Test list MCPs endpoint includes integration status."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_user_context
        
        # Mock MCP service response
        mock_mcp_response = Mock()
        mock_mcp_response.mcps = [Mock()]
        mock_mcp_response.mcps[0].__dict__ = self.mock_mcp_data
        mock_mcp_response.total = 1
        mock_mcp_response.total_pages = 1
        mock_mcp_response.page = 1
        
        mock_mcp_service.listMCPS = AsyncMock(return_value=mock_mcp_response)
        
        # Mock authentication service response
        mock_auth_service = AsyncMock()
        mock_get_auth_service.return_value = mock_auth_service
        
        mock_auth_service.check_integration_status.return_value = {
            "success": True,
            "message": "Integration statuses retrieved successfully",
            "integration_statuses": [
                {"integration_id": "integration_1", "is_connected": True},
                {"integration_id": "integration_2", "is_connected": False}
            ]
        }
        
        # Mock MessageToDict to return our test data
        with patch('app.api.routers.mcp_routes.MessageToDict', return_value=self.mock_mcp_data):
            # Act
            response = self.client.get(
                "/api/v1/mcps",
                headers={"Authorization": "Bearer fake_token"}
            )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["data"]
        assert len(data["data"]) == 1
        
        mcp = data["data"][0]
        assert mcp["id"] == "mcp_123"
        assert mcp["integrations"] == ["integration_1", "integration_2"]
        assert mcp["is_connected"] is True  # Should be True because integration_1 is connected
        
        # Verify auth service was called correctly
        mock_auth_service.check_integration_status.assert_called_once_with(
            user_id=self.test_user_id,
            integration_ids=["integration_1", "integration_2"]
        )

    @patch('app.api.routers.mcp_routes.get_auth_service_client')
    @patch('app.api.routers.mcp_routes.mcp_service')
    @patch('app.api.routers.mcp_routes.role_required')
    def test_get_mcp_with_integration_status(self, mock_role_required, mock_mcp_service, mock_get_auth_service):
        """Test get single MCP endpoint includes integration status."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_user_context
        
        # Mock MCP service response
        mock_mcp_response = Mock()
        mock_mcp_response.success = True
        mock_mcp_response.message = "MCP retrieved successfully"
        mock_mcp_response.mcp = Mock()
        mock_mcp_response.mcp.__dict__ = self.mock_mcp_data
        
        mock_mcp_service.getMCPById = AsyncMock(return_value=mock_mcp_response)
        
        # Mock authentication service response
        mock_auth_service = AsyncMock()
        mock_get_auth_service.return_value = mock_auth_service
        
        mock_auth_service.check_integration_status.return_value = {
            "success": True,
            "message": "Integration statuses retrieved successfully",
            "integration_statuses": [
                {"integration_id": "integration_1", "is_connected": False},
                {"integration_id": "integration_2", "is_connected": False}
            ]
        }
        
        # Mock MessageToDict to return our test data
        with patch('app.api.routers.mcp_routes.MessageToDict', return_value=self.mock_mcp_data):
            # Act
            response = self.client.get(
                "/api/v1/mcps/mcp_123",
                headers={"Authorization": "Bearer fake_token"}
            )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["mcp"]["id"] == "mcp_123"
        assert data["mcp"]["integrations"] == ["integration_1", "integration_2"]
        assert data["mcp"]["is_connected"] is False  # Should be False because no integrations are connected
        
        # Verify auth service was called correctly
        mock_auth_service.check_integration_status.assert_called_once_with(
            user_id=self.test_user_id,
            integration_ids=["integration_1", "integration_2"]
        )

    @patch('app.api.routers.mcp_routes.mcp_service')
    @patch('app.api.routers.mcp_routes.role_required')
    def test_mcp_without_integrations(self, mock_role_required, mock_mcp_service):
        """Test MCP without integrations has is_connected as None."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_user_context
        
        mcp_data_no_integrations = self.mock_mcp_data.copy()
        mcp_data_no_integrations["integrations"] = None
        
        # Mock MCP service response
        mock_mcp_response = Mock()
        mock_mcp_response.success = True
        mock_mcp_response.message = "MCP retrieved successfully"
        mock_mcp_response.mcp = Mock()
        mock_mcp_response.mcp.__dict__ = mcp_data_no_integrations
        
        mock_mcp_service.getMCPById = AsyncMock(return_value=mock_mcp_response)
        
        # Mock MessageToDict to return our test data
        with patch('app.api.routers.mcp_routes.MessageToDict', return_value=mcp_data_no_integrations):
            # Act
            response = self.client.get(
                "/api/v1/mcps/mcp_123",
                headers={"Authorization": "Bearer fake_token"}
            )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["mcp"]["id"] == "mcp_123"
        assert data["mcp"]["integrations"] is None
        assert data["mcp"]["is_connected"] is None  # Should be None for MCPs without integrations


if __name__ == "__main__":
    pytest.main([__file__])
