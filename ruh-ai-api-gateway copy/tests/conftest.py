import pytest
import asyncio
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import Mock, patch
from app.main import app
from app.core.config import settings

@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_user_data():
    return {
        "email": "<EMAIL>",
        "password": "Test123!",
        "full_name": "Test User"
    }


@pytest.fixture
def user_token():
    return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0X3VzZXIiLCJleHAiOjE3MDg2NTY0MDB9"


@pytest.fixture
def user_headers(user_token):
    return {"Authorization": f"Bearer {user_token}"}


@pytest.fixture
def mock_grpc_stub():
    return Mock()


