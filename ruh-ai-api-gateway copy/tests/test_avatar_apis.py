#!/usr/bin/env python3
"""
Test Agent Avatar APIs

This script tests all the agent avatar APIs:
1. Create agent avatar
2. Get agent avatar by ID
3. List all agent avatars
4. Delete agent avatar

Usage:
    python test_avatar_apis.py
"""

import os
import sys
import requests
import json
from pprint import pprint
import time

# Add the current directory to the path so we can import the GCSUtility
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.utils.GCSUtility.gcs import GCSUtility

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"  # Update with your API gateway URL

# Replace with your actual auth token or login credentials
AUTH_TOKEN = "your_auth_token_here"  # Replace with your actual token
# Or use these credentials to get a token
EMAIL = "<EMAIL>"  # Replace with your admin email
PASSWORD = "Nikhil@123#" 

# Test data
TEST_AVATAR_URL = "https://storage.googleapis.com/ruh-ai/agent-avatars/test-avatar.svg"

def generate_presigned_url(file_name, file_type):
    """Generate a presigned URL using GCSUtility directly."""
    try:
        # Initialize the GCSUtility class
        gcs = GCSUtility()

        # Generate the presigned URL
        result = gcs.generate_presigned_url(
            file_name=file_name,
            file_type=file_type,
            file_path="agent-avatars"
        )

        if not result["success"]:
            print(f"Failed to generate presigned URL: {result.get('message', 'Unknown error')}")
            return None

        return result["url"]
    except Exception as e:
        print(f"Failed to generate presigned URL: {e}")
        return None

def get_auth_token():
    """Get authentication token by logging in."""
    login_url = f"{API_BASE_URL}/auth/login"
    login_data = {
        "email": EMAIL,
        "password": PASSWORD
    }

    try:
        response = requests.post(login_url, json=login_data)
        response.raise_for_status()
        return response.json().get("access_token")
    except requests.exceptions.RequestException as e:
        print(f"Login failed: {e}")
        if hasattr(response, 'text'):
            print(f"Response: {response.text}")
        return None

def get_headers(token):
    """Get headers with authentication token."""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def create_agent_avatar(token, url):
    """Create an agent avatar with the given URL."""
    api_url = f"{API_BASE_URL}/agent-avatars"
    payload = {"url": url}

    try:
        response = requests.post(api_url, json=payload, headers=get_headers(token))
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Failed to create agent avatar: {e}")
        if hasattr(response, 'text'):
            print(f"Response: {response.text}")
        return None

def get_agent_avatar(token, avatar_id):
    """Get an agent avatar by ID."""
    api_url = f"{API_BASE_URL}/agent-avatars/{avatar_id}"

    try:
        response = requests.get(api_url, headers=get_headers(token))
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Failed to get agent avatar: {e}")
        if hasattr(response, 'text'):
            print(f"Response: {response.text}")
        return None

def list_agent_avatars(token, page=1, limit=10):
    """List agent avatars with pagination."""
    api_url = f"{API_BASE_URL}/agent-avatars?page={page}&limit={limit}"

    try:
        response = requests.get(api_url, headers=get_headers(token))
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Failed to list agent avatars: {e}")
        if hasattr(response, 'text'):
            print(f"Response: {response.text}")
        return None

def delete_agent_avatar(token, avatar_id):
    """Delete an agent avatar by ID."""
    api_url = f"{API_BASE_URL}/agent-avatars/{avatar_id}"

    try:
        response = requests.delete(api_url, headers=get_headers(token))
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Failed to delete agent avatar: {e}")
        if hasattr(response, 'text'):
            print(f"Response: {response.text}")
        return None

def test_create_avatar(token):
    """Test creating an agent avatar."""
    print("\n=== Testing Create Agent Avatar API ===")
    response = create_agent_avatar(token, TEST_AVATAR_URL)
    if response and response.get("success"):
        print("✅ Create agent avatar test passed")
        pprint(response)
        return response.get("avatar", {}).get("id")
    else:
        print("❌ Create agent avatar test failed")
        return None

def test_get_avatar(token, avatar_id):
    """Test getting an agent avatar by ID."""
    print(f"\n=== Testing Get Agent Avatar API (ID: {avatar_id}) ===")
    response = get_agent_avatar(token, avatar_id)
    if response and response.get("success"):
        print("✅ Get agent avatar test passed")
        pprint(response)
        return True
    else:
        print("❌ Get agent avatar test failed")
        return False

def test_list_avatars(token):
    """Test listing agent avatars."""
    print("\n=== Testing List Agent Avatars API ===")
    response = list_agent_avatars(token)
    if response:
        print("✅ List agent avatars test passed")
        print(f"Total avatars: {response.get('total', 0)}")
        print(f"Current page: {response.get('page', 1)}")
        print(f"Total pages: {response.get('total_pages', 1)}")

        avatars = response.get("avatars", [])
        if avatars:
            print(f"First few avatars ({min(3, len(avatars))}):")
            for avatar in avatars[:3]:
                print(f"  - ID: {avatar.get('id')}, URL: {avatar.get('url')}")
        return True
    else:
        print("❌ List agent avatars test failed")
        return False

def test_delete_avatar(token, avatar_id):
    """Test deleting an agent avatar."""
    print(f"\n=== Testing Delete Agent Avatar API (ID: {avatar_id}) ===")
    response = delete_agent_avatar(token, avatar_id)
    if response and response.get("success"):
        print("✅ Delete agent avatar test passed")
        pprint(response)

        # Verify deletion by trying to get the avatar
        get_response = get_agent_avatar(token, avatar_id)
        if get_response and not get_response.get("success"):
            print("✅ Deletion verification passed - Avatar no longer exists")
        else:
            print("❌ Deletion verification failed - Avatar still exists")

        return True
    else:
        print("❌ Delete agent avatar test failed")
        return False

def main():
    """Main function to test agent avatar APIs."""
    # Get authentication token
    token = AUTH_TOKEN
    if not token or token == "your_auth_token_here":
        token = get_auth_token()
        if not token:
            print("Authentication failed. Please check your credentials.")
            return

    print("Authentication successful.")

    # Test create avatar API
    # avatar_id = test_create_avatar(token)
    avatar_id = "b9a0f8f9-625a-4563-82ca-29b4346cd603"
    if not avatar_id:
        print("Cannot proceed with further tests without a valid avatar ID.")
        return

    # Test get avatar API
    # test_get_avatar(token, avatar_id)

    # Test list avatars API
    test_list_avatars(token)

    # Test delete avatar API
    # test_delete_avatar(token, avatar_id)

    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
