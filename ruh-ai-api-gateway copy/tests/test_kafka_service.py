import pytest
from unittest.mock import patch, Mock, AsyncMock
from app.services.kafka_service import KafkaService


class TestKafkaService:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.kafka_service = KafkaService()

    @pytest.mark.asyncio
    @patch('aiokafka.AIOKafkaProducer')
    async def test_send_message_success(self, mock_producer):
        # Arrange
        mock_producer.return_value.start = AsyncMock()
        mock_producer.return_value.send_and_wait = AsyncMock()
        mock_producer.return_value.stop = AsyncMock()
        
        topic = "test-topic"
        message = {"key": "value"}

        # Act
        await self.kafka_service.send_message(topic, message)

        # Assert
        mock_producer.return_value.start.assert_called_once()
        mock_producer.return_value.send_and_wait.assert_called_once()
        mock_producer.return_value.stop.assert_called_once()

    @pytest.mark.asyncio
    @patch('aiokafka.AIOKafkaConsumer')
    async def test_consume_messages_success(self, mock_consumer):
        # Arrange
        mock_consumer.return_value.start = AsyncMock()
        mock_consumer.return_value.stop = AsyncMock()
        
        topic = "test-topic"
        message = {"key": "value"}
        
        # Mock the consumer to yield one message
        mock_consumer.return_value.__aiter__.return_value = AsyncMock()
        mock_consumer.return_value.__aiter__.return_value.__anext__.return_value = Mock(
            value=bytes(str(message), 'utf-8')
        )

        # Act
        async for msg in self.kafka_service.consume_messages(topic):
            # Assert
            assert msg == message
            break

        mock_consumer.return_value.start.assert_called_once()
