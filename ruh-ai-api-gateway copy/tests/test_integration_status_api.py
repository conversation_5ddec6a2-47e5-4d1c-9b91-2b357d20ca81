#!/usr/bin/env python3
"""
Test for the integration status check API endpoint.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.schemas.integration import CheckIntegrationStatusRequest


class TestIntegrationStatusAPI:
    """Test cases for integration status check API."""

    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        self.test_user_id = "test_user_123"
        self.test_integration_ids = [
            "integration_1",
            "integration_2", 
            "integration_3"
        ]
        
        # Mock JWT token payload
        self.mock_user_context = {
            "user_id": self.test_user_id,
            "email": "<EMAIL>",
            "role": "user",
            "name": "Test User"
        }
        
        self.mock_admin_context = {
            "user_id": "admin_123",
            "email": "<EMAIL>", 
            "role": "admin",
            "name": "Admin User"
        }

    @patch('app.api.routers.integration_routes.get_auth_service_client')
    @patch('app.api.routers.integration_routes.role_required')
    def test_check_integration_status_success(self, mock_role_required, mock_get_auth_service):
        """Test successful integration status check."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_user_context
        
        mock_auth_service = AsyncMock()
        mock_get_auth_service.return_value = mock_auth_service
        
        # Mock successful response from auth service
        mock_auth_service.check_integration_status.return_value = {
            "success": True,
            "message": "Integration statuses retrieved successfully",
            "integration_statuses": [
                {"integration_id": "integration_1", "is_connected": True},
                {"integration_id": "integration_2", "is_connected": False},
                {"integration_id": "integration_3", "is_connected": True}
            ]
        }
        
        request_data = {
            "user_id": self.test_user_id,
            "integration_ids": self.test_integration_ids
        }

        # Act
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json=request_data,
            headers={"Authorization": "Bearer fake_token"}
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Integration statuses retrieved successfully"
        assert len(data["integration_statuses"]) == 3
        
        # Verify auth service was called correctly
        mock_auth_service.check_integration_status.assert_called_once_with(
            user_id=self.test_user_id,
            integration_ids=self.test_integration_ids
        )

    @patch('app.api.routers.integration_routes.get_auth_service_client')
    @patch('app.api.routers.integration_routes.role_required')
    def test_check_integration_status_admin_can_check_any_user(self, mock_role_required, mock_get_auth_service):
        """Test that admin users can check any user's integration status."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_admin_context
        
        mock_auth_service = AsyncMock()
        mock_get_auth_service.return_value = mock_auth_service
        
        mock_auth_service.check_integration_status.return_value = {
            "success": True,
            "message": "Integration statuses retrieved successfully",
            "integration_statuses": []
        }
        
        request_data = {
            "user_id": "different_user_123",  # Different from admin's user_id
            "integration_ids": self.test_integration_ids
        }

        # Act
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json=request_data,
            headers={"Authorization": "Bearer fake_token"}
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        mock_auth_service.check_integration_status.assert_called_once_with(
            user_id="different_user_123",
            integration_ids=self.test_integration_ids
        )

    @patch('app.api.routers.integration_routes.role_required')
    def test_check_integration_status_user_cannot_check_other_users(self, mock_role_required):
        """Test that regular users cannot check other users' integration status."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_user_context
        
        request_data = {
            "user_id": "different_user_123",  # Different from user's user_id
            "integration_ids": self.test_integration_ids
        }

        # Act
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json=request_data,
            headers={"Authorization": "Bearer fake_token"}
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN
        data = response.json()
        assert "You can only check your own integration status" in data["detail"]

    @patch('app.api.routers.integration_routes.role_required')
    def test_check_integration_status_missing_user_context(self, mock_role_required):
        """Test error handling when user context is missing user_id."""
        # Arrange
        mock_user_context_no_id = {
            "email": "<EMAIL>",
            "role": "user",
            "name": "Test User"
            # Missing user_id
        }
        mock_role_required.return_value = lambda: mock_user_context_no_id
        
        request_data = {
            "user_id": self.test_user_id,
            "integration_ids": self.test_integration_ids
        }

        # Act
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json=request_data,
            headers={"Authorization": "Bearer fake_token"}
        )

        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "User context missing ID" in data["detail"]

    def test_check_integration_status_invalid_request_data(self):
        """Test validation of request data."""
        # Test missing user_id
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json={"integration_ids": self.test_integration_ids},
            headers={"Authorization": "Bearer fake_token"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test missing integration_ids
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json={"user_id": self.test_user_id},
            headers={"Authorization": "Bearer fake_token"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Test empty integration_ids
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json={"user_id": self.test_user_id, "integration_ids": []},
            headers={"Authorization": "Bearer fake_token"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @patch('app.api.routers.integration_routes.get_auth_service_client')
    @patch('app.api.routers.integration_routes.role_required')
    def test_check_integration_status_auth_service_error(self, mock_role_required, mock_get_auth_service):
        """Test error handling when auth service returns error."""
        # Arrange
        mock_role_required.return_value = lambda: self.mock_user_context
        
        mock_auth_service = AsyncMock()
        mock_get_auth_service.return_value = mock_auth_service
        
        # Mock error response from auth service
        mock_auth_service.check_integration_status.return_value = {
            "success": False,
            "message": "Database connection failed",
            "integration_statuses": []
        }
        
        request_data = {
            "user_id": self.test_user_id,
            "integration_ids": self.test_integration_ids
        }

        # Act
        response = self.client.post(
            "/api/v1/integrations/status/check",
            json=request_data,
            headers={"Authorization": "Bearer fake_token"}
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert data["success"] is False
        assert data["message"] == "Database connection failed"


if __name__ == "__main__":
    pytest.main([__file__])
