#!/usr/bin/env python3
"""
Test script to verify the protobuf changes for MCP schema updates.
This test verifies that the config field has been replaced with hosted_url and mcp_type.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_protobuf_mcp_updates():
    """Test that protobuf definitions have been updated correctly."""
    print("🧪 Testing protobuf MCP updates...")
    
    try:
        from app.grpc_ import mcp_pb2
        print("✅ Successfully imported mcp_pb2")
        
        # Test CreateMCPRequest has hosted_url and mcp_type instead of config
        create_request = mcp_pb2.CreateMCPRequest()
        
        # Check that hosted_url field exists
        assert hasattr(create_request, 'hosted_url'), "CreateMCPRequest should have hosted_url field"
        print("✅ CreateMCPRequest has hosted_url field")
        
        # Check that mcp_type field exists
        assert hasattr(create_request, 'mcp_type'), "CreateMCPRequest should have mcp_type field"
        print("✅ CreateMCPRequest has mcp_type field")
        
        # Check that config field does not exist
        assert not hasattr(create_request, 'config'), "CreateMCPRequest should not have config field"
        print("✅ CreateMCPRequest does not have config field")
        
        # Test UpdateMCPRequest has hosted_url and mcp_type instead of config
        update_request = mcp_pb2.UpdateMCPRequest()
        
        # Check that hosted_url field exists
        assert hasattr(update_request, 'hosted_url'), "UpdateMCPRequest should have hosted_url field"
        print("✅ UpdateMCPRequest has hosted_url field")
        
        # Check that mcp_type field exists
        assert hasattr(update_request, 'mcp_type'), "UpdateMCPRequest should have mcp_type field"
        print("✅ UpdateMCPRequest has mcp_type field")
        
        # Check that config field does not exist
        assert not hasattr(update_request, 'config'), "UpdateMCPRequest should not have config field"
        print("✅ UpdateMCPRequest does not have config field")
        
        # Test MCP message has hosted_url and mcp_type instead of config
        mcp_message = mcp_pb2.MCP()
        
        # Check that hosted_url field exists
        assert hasattr(mcp_message, 'hosted_url'), "MCP should have hosted_url field"
        print("✅ MCP has hosted_url field")
        
        # Check that mcp_type field exists
        assert hasattr(mcp_message, 'mcp_type'), "MCP should have mcp_type field"
        print("✅ MCP has mcp_type field")
        
        # Check that config field does not exist
        assert not hasattr(mcp_message, 'config'), "MCP should not have config field"
        print("✅ MCP does not have config field")
        
        # Test MarketplaceMCP message has hosted_url and mcp_type instead of config
        marketplace_mcp = mcp_pb2.MarketplaceMCP()
        
        # Check that hosted_url field exists
        assert hasattr(marketplace_mcp, 'hosted_url'), "MarketplaceMCP should have hosted_url field"
        print("✅ MarketplaceMCP has hosted_url field")
        
        # Check that mcp_type field exists
        assert hasattr(marketplace_mcp, 'mcp_type'), "MarketplaceMCP should have mcp_type field"
        print("✅ MarketplaceMCP has mcp_type field")
        
        # Check that config field does not exist
        assert not hasattr(marketplace_mcp, 'config'), "MarketplaceMCP should not have config field"
        print("✅ MarketplaceMCP does not have config field")
        
        # Test that we can set the new fields
        create_request.hosted_url = "https://api.test.com/mcp"
        create_request.mcp_type = "sse"
        print("✅ Can set hosted_url and mcp_type on CreateMCPRequest")
        
        update_request.hosted_url = "https://api.updated.com/mcp"
        update_request.mcp_type = "stdio"
        print("✅ Can set hosted_url and mcp_type on UpdateMCPRequest")
        
        mcp_message.hosted_url = "https://api.mcp.com"
        mcp_message.mcp_type = "streamable-http"
        print("✅ Can set hosted_url and mcp_type on MCP")
        
        marketplace_mcp.hosted_url = "https://marketplace.api.com"
        marketplace_mcp.mcp_type = "sse"
        print("✅ Can set hosted_url and mcp_type on MarketplaceMCP")
        
        print("\n🎉 All protobuf MCP update tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_mcp_url_removal():
    """Test that MCPUrl message has been removed."""
    print("\n🧪 Testing MCPUrl removal...")
    
    try:
        from app.grpc_ import mcp_pb2
        
        # Check that MCPUrl does not exist
        assert not hasattr(mcp_pb2, 'MCPUrl'), "MCPUrl should have been removed"
        print("✅ MCPUrl message successfully removed")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 Testing MCP Protobuf Schema Updates")
    print("=" * 60)
    
    success = True
    
    # Run tests
    success &= test_protobuf_mcp_updates()
    success &= test_mcp_url_removal()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All protobuf tests passed! The MCP schema has been successfully updated.")
        print("✅ config field replaced with hosted_url and mcp_type")
        print("✅ MCPUrl message removed")
        print("✅ All protobuf messages updated correctly")
    else:
        print("❌ Some tests failed. Please check the protobuf definitions.")
        sys.exit(1)
    print("=" * 60)
