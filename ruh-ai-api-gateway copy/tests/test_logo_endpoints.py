#!/usr/bin/env python3
"""
Test script to verify logo field is included in API gateway integration endpoints.
This script tests the schema definitions and response structures.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.schemas.integration import IntegrationResponse, UserIntegrationStatus, IntegrationBase


def test_integration_schemas():
    """Test that integration schemas include logo field."""
    print("Testing Integration Schemas for Logo Field...")
    
    # Test IntegrationBase schema
    print("\n1. Testing IntegrationBase schema:")
    try:
        integration_base = IntegrationBase(
            name="Test Integration",
            logo="https://example.com/logo.png",
            integration_type="oauth",
            is_enabled=True
        )
        print(f"   ✓ IntegrationBase with logo: {integration_base.logo}")
    except Exception as e:
        print(f"   ✗ IntegrationBase failed: {e}")
        return False
    
    # Test IntegrationResponse schema
    print("\n2. Testing IntegrationResponse schema:")
    try:
        integration_response = IntegrationResponse(
            id="integration_123",
            name="Test Integration",
            description="Test description",
            logo="https://example.com/logo.png",
            integration_type="oauth",
            is_enabled=True,
            status="active",
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z"
        )
        print(f"   ✓ IntegrationResponse with logo: {integration_response.logo}")
    except Exception as e:
        print(f"   ✗ IntegrationResponse failed: {e}")
        return False
    
    # Test UserIntegrationStatus schema
    print("\n3. Testing UserIntegrationStatus schema:")
    try:
        user_integration = UserIntegrationStatus(
            user_id="user_123",
            integration_id="integration_123",
            integration_name="Test Integration",
            logo="https://example.com/logo.png",
            is_connected=True,
            created_at="2023-01-01T00:00:00Z",
            connection_type="oauth"
        )
        print(f"   ✓ UserIntegrationStatus with logo: {user_integration.logo}")
    except Exception as e:
        print(f"   ✗ UserIntegrationStatus failed: {e}")
        return False
    
    # Test with None/empty logo
    print("\n4. Testing schemas with None/empty logo:")
    try:
        integration_no_logo = IntegrationResponse(
            id="integration_456",
            name="Test Integration No Logo",
            description="Test description",
            logo=None,  # No logo
            integration_type="oauth",
            is_enabled=True,
            status="active",
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z"
        )
        print(f"   ✓ IntegrationResponse with None logo: {integration_no_logo.logo}")
        
        user_integration_no_logo = UserIntegrationStatus(
            user_id="user_123",
            integration_id="integration_456",
            integration_name="Test Integration No Logo",
            logo=None,  # No logo
            is_connected=True,
            created_at="2023-01-01T00:00:00Z",
            connection_type="oauth"
        )
        print(f"   ✓ UserIntegrationStatus with None logo: {user_integration_no_logo.logo}")
    except Exception as e:
        print(f"   ✗ Schemas with None logo failed: {e}")
        return False
    
    return True


def test_schema_serialization():
    """Test that schemas properly serialize logo field to JSON."""
    print("\n\nTesting Schema Serialization...")
    
    try:
        # Test IntegrationResponse serialization
        integration = IntegrationResponse(
            id="integration_123",
            name="Test Integration",
            description="Test description",
            logo="https://example.com/logo.png",
            integration_type="oauth",
            is_enabled=True,
            status="active",
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z"
        )
        
        json_data = integration.model_dump()
        print(f"   ✓ IntegrationResponse JSON includes logo: {'logo' in json_data}")
        print(f"   ✓ Logo value in JSON: {json_data.get('logo')}")
        
        # Test UserIntegrationStatus serialization
        user_integration = UserIntegrationStatus(
            user_id="user_123",
            integration_id="integration_123",
            integration_name="Test Integration",
            logo="https://example.com/logo.png",
            is_connected=True,
            created_at="2023-01-01T00:00:00Z",
            connection_type="oauth"
        )
        
        user_json_data = user_integration.model_dump()
        print(f"   ✓ UserIntegrationStatus JSON includes logo: {'logo' in user_json_data}")
        print(f"   ✓ Logo value in JSON: {user_json_data.get('logo')}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Serialization test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("LOGO FIELD INTEGRATION TEST")
    print("=" * 60)
    
    success = True
    
    # Test schemas
    if not test_integration_schemas():
        success = False
    
    # Test serialization
    if not test_schema_serialization():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ ALL TESTS PASSED - Logo field is properly integrated!")
        print("\nThe following schemas now include logo field:")
        print("  • IntegrationBase")
        print("  • IntegrationResponse") 
        print("  • UserIntegrationStatus")
        print("\nLogo field features:")
        print("  • Optional field (can be None)")
        print("  • Supports URL strings")
        print("  • Properly serializes to JSON")
        print("  • Included in all API responses")
    else:
        print("❌ SOME TESTS FAILED - Logo field integration needs attention!")
        return 1
    
    print("=" * 60)
    return 0


if __name__ == "__main__":
    exit(main())
