"""
Logo Field Integration Test

This module tests that the logo field is properly included in all integration API responses:
1. Integration creation with logo
2. Integration listing with logo
3. User integration status with logo
4. OAuth and API key credential responses with logo
"""

import json
import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock

from app.services.integration_functions import IntegrationService
from app.services.user_functions import UserIntegrationsService
from app.grpc_ import authentication_pb2
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum, OAuthCredential


class TestLogoFieldIntegration:
    """Test logo field inclusion in all integration responses."""

    @pytest.fixture
    def integration_service(self):
        """Integration service instance."""
        return IntegrationService()

    @pytest.fixture
    def user_service(self):
        """User integrations service instance."""
        return UserIntegrationsService()

    @pytest.fixture
    def sample_logo_url(self):
        """Sample logo URL for testing."""
        return "https://example.com/logos/google-calendar.png"

    @pytest.fixture
    def oauth_integration_schema(self):
        """OAuth integration schema with logo."""
        return {
            "client_id_env_var": "GOOGLE_CLIENT_ID",
            "client_secret_env_var": "GOOGLE_CLIENT_SECRET",
            "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
            "token_url": "https://oauth2.googleapis.com/token",
            "scopes": ["https://www.googleapis.com/auth/calendar.readonly"],
            "redirect_uri_path": "/oauth/google/callback"
        }

    @pytest.fixture
    def api_key_integration_schema(self):
        """API key integration schema."""
        return [
            {"name": "api_key", "required": True, "description": "Your API secret key"},
            {"name": "region", "required": False, "description": "AWS Region"}
        ]

    def test_create_oauth_integration_with_logo(self, integration_service, oauth_integration_schema, sample_logo_url):
        """Test creating OAuth integration with logo field."""
        with patch.object(integration_service, '_get_db_session') as mock_db:
            # Mock database session and query
            mock_session = Mock()
            mock_db.return_value = mock_session
            mock_session.query.return_value.filter.return_value.first.return_value = None
            
            # Create request with logo
            request = authentication_pb2.CreateIntegrationRequest(
                admin_user_id="admin_123",
                logo=sample_logo_url,
                name="Google Calendar",
                description="Google Calendar integration",
                connection_type=authentication_pb2.CONNECTION_TYPE_OAUTH,
                schema_definition=json.dumps(oauth_integration_schema)
            )
            
            # Mock context
            context = Mock()
            
            # Call the service
            response = integration_service.CreateIntegration(request, context)
            
            # Verify response
            assert response.success is True
            assert response.integration.logo == sample_logo_url
            assert response.integration.name == "Google Calendar"
            
            # Verify database call
            mock_session.add.assert_called_once()
            added_integration = mock_session.add.call_args[0][0]
            assert added_integration.logo == sample_logo_url

    def test_create_api_key_integration_with_logo(self, integration_service, api_key_integration_schema, sample_logo_url):
        """Test creating API key integration with logo field."""
        with patch.object(integration_service, '_get_db_session') as mock_db:
            # Mock database session and query
            mock_session = Mock()
            mock_db.return_value = mock_session
            mock_session.query.return_value.filter.return_value.first.return_value = None
            
            # Create request with logo
            request = authentication_pb2.CreateIntegrationRequest(
                admin_user_id="admin_123",
                logo=sample_logo_url,
                name="AWS Bedrock",
                description="AWS Bedrock API integration",
                connection_type=authentication_pb2.CONNECTION_TYPE_API_KEY,
                schema_definition=json.dumps(api_key_integration_schema)
            )
            
            # Mock context
            context = Mock()
            
            # Call the service
            response = integration_service.CreateIntegration(request, context)
            
            # Verify response
            assert response.success is True
            assert response.integration.logo == sample_logo_url
            assert response.integration.name == "AWS Bedrock"

    def test_list_integrations_includes_logo(self, integration_service, sample_logo_url):
        """Test that list integrations includes logo field."""
        with patch.object(integration_service, '_get_db_session') as mock_db:
            # Mock database session and query
            mock_session = Mock()
            mock_db.return_value = mock_session
            
            # Create mock integration with logo
            mock_integration = IntegrationDefinition(
                id="integration_123",
                logo=sample_logo_url,
                name="Test Integration",
                description="Test integration with logo",
                connection_type=ConnectionTypeEnum.OAUTH,
                schema_definition={"test": "schema"},
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
            # Mock query results
            mock_query = Mock()
            mock_session.query.return_value = mock_query
            mock_query.filter.return_value = mock_query
            mock_query.order_by.return_value = mock_query
            mock_query.offset.return_value = mock_query
            mock_query.limit.return_value = mock_query
            mock_query.all.return_value = [mock_integration]
            mock_query.count.return_value = 1
            
            # Create request
            request = authentication_pb2.ListIntegrationsRequest(
                page=1,
                page_size=10
            )
            
            # Mock context
            context = Mock()
            
            # Call the service
            response = integration_service.ListIntegrations(request, context)
            
            # Verify response
            assert response.success is True
            assert len(response.integrations) == 1
            assert response.integrations[0].logo == sample_logo_url
            assert response.integrations[0].name == "Test Integration"

    def test_get_integration_includes_logo(self, integration_service, sample_logo_url):
        """Test that get integration includes logo field."""
        with patch.object(integration_service, '_get_db_session') as mock_db:
            # Mock database session and query
            mock_session = Mock()
            mock_db.return_value = mock_session
            
            # Create mock integration with logo
            mock_integration = IntegrationDefinition(
                id="integration_123",
                logo=sample_logo_url,
                name="Test Integration",
                description="Test integration with logo",
                connection_type=ConnectionTypeEnum.OAUTH,
                schema_definition={"test": "schema"},
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
            # Mock query results
            mock_session.query.return_value.filter.return_value.first.return_value = mock_integration
            
            # Create request
            request = authentication_pb2.GetIntegrationRequest(
                integration_id="integration_123"
            )
            
            # Mock context
            context = Mock()
            
            # Call the service
            response = integration_service.GetIntegration(request, context)
            
            # Verify response
            assert response.success is True
            assert response.integration.logo == sample_logo_url
            assert response.integration.name == "Test Integration"

    def test_list_user_integrations_includes_logo(self, user_service, sample_logo_url):
        """Test that list user integrations includes logo field."""
        with patch.object(user_service, '_get_db_session') as mock_db:
            # Mock database session and query
            mock_session = Mock()
            mock_db.return_value = mock_session
            
            # Create mock integration and credential
            mock_integration = IntegrationDefinition(
                id="integration_123",
                logo=sample_logo_url,
                name="Test Integration",
                description="Test integration with logo",
                connection_type=ConnectionTypeEnum.OAUTH,
                schema_definition={"test": "schema"},
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
            mock_credential = OAuthCredential(
                user_id="user_123",
                integration_definition_id="integration_123",
                secret_reference="encrypted_secret",
                is_connected=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
            # Mock query results
            mock_query = Mock()
            mock_session.query.return_value = mock_query
            mock_query.join.return_value = mock_query
            mock_query.filter.return_value = mock_query
            mock_query.order_by.return_value = mock_query
            mock_query.all.return_value = [(mock_credential, mock_integration)]
            
            # Create request
            request = authentication_pb2.ListUserIntegrationsRequest(
                user_id="user_123"
            )
            
            # Mock context
            context = Mock()
            
            # Call the service
            response = user_service.ListUserIntegrations(request, context)
            
            # Verify response
            assert response.success is True
            assert len(response.integrations) == 1
            assert response.integrations[0].logo == sample_logo_url
            assert response.integrations[0].integration_name == "Test Integration"

    def test_integration_without_logo_returns_empty_string(self, integration_service):
        """Test that integration without logo returns empty string."""
        with patch.object(integration_service, '_get_db_session') as mock_db:
            # Mock database session and query
            mock_session = Mock()
            mock_db.return_value = mock_session
            
            # Create mock integration without logo
            mock_integration = IntegrationDefinition(
                id="integration_123",
                logo=None,  # No logo
                name="Test Integration",
                description="Test integration without logo",
                connection_type=ConnectionTypeEnum.OAUTH,
                schema_definition={"test": "schema"},
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
            # Mock query results
            mock_session.query.return_value.filter.return_value.first.return_value = mock_integration
            
            # Create request
            request = authentication_pb2.GetIntegrationRequest(
                integration_id="integration_123"
            )
            
            # Mock context
            context = Mock()
            
            # Call the service
            response = integration_service.GetIntegration(request, context)
            
            # Verify response
            assert response.success is True
            assert response.integration.logo == ""  # Should be empty string, not None
            assert response.integration.name == "Test Integration"


