import pytest
from app.utils.parse_user_details import extract_user_details_from_jwt


class TestExtractUserDetailsFromJWT:
    """Test cases for the extract_user_details_from_jwt function."""

    def test_extract_user_details_complete_data(self):
        """Test extracting user details with all required fields present."""
        # Arrange
        jwt_user = {
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "role": "admin",
            "name": "<PERSON>",
            "organisation_id": "org123",
            "fcm_token": "fcm_token_123"
        }

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        expected = {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "full_name": "<PERSON>",
            "fcm_token": "fcm_token_123"
        }
        assert result == expected

    def test_extract_user_details_without_fcm_token(self):
        """Test extracting user details when fcm_token is not present."""
        # Arrange
        jwt_user = {
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "role": "user",
            "name": "Jane Smith",
            "organisation_id": "org456"
        }

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        expected = {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "full_name": "Jane Smith",
            "fcm_token": None
        }
        assert result == expected

    def test_extract_user_details_missing_fields(self):
        """Test extracting user details when some required fields are missing."""
        # Arrange
        jwt_user = {
            "email": "<EMAIL>",
            "role": "user"
        }

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        expected = {
            "id": None,
            "email": "<EMAIL>",
            "full_name": None,
            "fcm_token": None
        }
        assert result == expected

    def test_extract_user_details_empty_dict(self):
        """Test extracting user details from an empty dictionary."""
        # Arrange
        jwt_user = {}

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        expected = {
            "id": None,
            "email": None,
            "full_name": None,
            "fcm_token": None
        }
        assert result == expected

    def test_extract_user_details_with_none_values(self):
        """Test extracting user details when fields have None values."""
        # Arrange
        jwt_user = {
            "user_id": None,
            "email": None,
            "role": "user",
            "name": None,
            "organisation_id": "org123",
            "fcm_token": None
        }

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        expected = {
            "id": None,
            "email": None,
            "full_name": None,
            "fcm_token": None
        }
        assert result == expected

    def test_extract_user_details_field_mapping(self):
        """Test that the field mapping is correct."""
        # Arrange
        jwt_user = {
            "user_id": "user123",
            "email": "<EMAIL>",
            "role": "admin",
            "name": "Test User",
            "organisation_id": "org789",
            "fcm_token": "token456"
        }

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        # Verify that JWT fields are correctly mapped to expected fields
        assert result["id"] == jwt_user["user_id"]
        assert result["email"] == jwt_user["email"]
        assert result["full_name"] == jwt_user["name"]
        assert result["fcm_token"] == jwt_user["fcm_token"]

    def test_extract_user_details_extra_fields_ignored(self):
        """Test that extra fields in JWT are ignored."""
        # Arrange
        jwt_user = {
            "user_id": "user123",
            "email": "<EMAIL>",
            "role": "admin",
            "name": "Test User",
            "organisation_id": "org789",
            "fcm_token": "token456",
            "extra_field": "should_be_ignored",
            "another_field": 12345
        }

        # Act
        result = extract_user_details_from_jwt(jwt_user)

        # Assert
        expected = {
            "id": "user123",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "fcm_token": "token456"
        }
        assert result == expected
        assert "extra_field" not in result
        assert "another_field" not in result
