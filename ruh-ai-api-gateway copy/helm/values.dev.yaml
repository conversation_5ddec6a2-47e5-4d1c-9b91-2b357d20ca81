autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 4
  targetCPUUtilizationPercentage: 70
  # targetMemoryUtilizationPercentage: 70
resources:
  requests:
    memory: "64Mi"
    cpu: "50m"
  limits:
    memory: "1Gi"
    cpu: "250m"
ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
  hosts:
    - host: app-dev.rapidinnovation.dev
      paths:
        - path: /
          pathType: Prefix
  tls: []
