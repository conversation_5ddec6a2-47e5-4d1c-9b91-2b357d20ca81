# Application Settings
ENV=
APP_NAME=api-gateway
DEBUG=true
API_V1_STR=/api/v1

# Service endpoints
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=50052
ADMIN_SERVICE_HOST=localhost
ADMIN_SERVICE_PORT=50053
COMMUNICATION_SERVICE_HOST=localhost
COMMUNICATION_SERVICE_PORT=50055
WORKFLOW_SERVICE_HOST=localhost
WORKFLOW_SERVICE_PORT=50056
AGENT_SERVICE_HOST=localhost
AGENT_SERVICE_PORT=50057
MCP_SERVICE_HOST=localhost
MCP_SERVICE_PORT=50058
NOTIFICATION_SERVICE_HOST=localhost
NOTIFICATION_SERVICE_PORT=50060
ORGANISATION_SERVICE_HOST=localhost
ORGANISATION_SERVICE_PORT=50070
AUTH_SERVICE_HOST=localhost
AUTH_SERVICE_PORT=50054
ANALYTICS_SERVICE_HOST=localhost
ANALYTICS_SERVICE_PORT=50059

# Redis settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# JWT settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS settings
CORS_ORIGINS=["http://localhost", "http://localhost:3000", "http://localhost:8000"]
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# Service discovery settings
SERVICE_DISCOVERY_ENABLED=false
SERVICE_DISCOVERY_HOST=consul
SERVICE_DISCOVERY_PORT=8500

# Proto
REPO_URL=
GIT_TOKEN=

# OAuth Provider Configurations

# Google OAuth
GOOGLE_CLIENT_ID="google-client-id"
GOOGLE_CLIENT_SECRET="google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Microsoft OAuth
MICROSOFT_CLIENT_ID="microsoft-client-id"
MICROSOFT_CLIENT_SECRET="microsoft-client-secret"
MICROSOFT_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# GitHub OAuth
GITHUB_CLIENT_ID="github-client-id"
GITHUB_CLIENT_SECRET="github-client-secret"
GITHUB_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Slack OAuth
SLACK_CLIENT_ID=
SLACK_CLIENT_SECRET=
SLACK_REDIRECT_URI=

# Custom OAuth Providers (JSON format)
# Example: [{"provider": "custom", "client_id": "...", "client_secret": "...", "auth_url": "...", "token_url": "..."}]
CUSTOM_OAUTH_PROVIDERS="{}"

# Google Secret Manager
GOOGLE_APPLICATION_CREDENTIALS="base64-encoded-service-account-key"
GOOGLE_PROJECT_ID="your-google-project-id"

# PostgreSQL settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=api_gateway

# Kafka Settings
KAFKA_BROKER_PORT=9092
KAFKA_BROKER_HOST=localhost

# API Key For get_workflow_orchestration route
ORCHESTRATION_SERVER_AUTH_KEY=

# Auth Key for Authentication Service
AUTH_SERVICE_SERVER_AUTH_KEY=

# LiveKit Configuration
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=
LIVEKIT_URL=

AGENT_PLATFORM_AUTH_KEY=
WORKFLOW_SERVICE_AUTH_KEY=

GCS_CRED=
BUCKET_NAME=



OPENAI_API_KEY=
OPENAI_MODEL=gpt-4o
REQUESTY_API_KEY=
REQUESTY_BASE_URL=https://router.requesty.ai/v1

DEPLOYMENT_WORKER_AUTH_KEY=


# Jira OAuth
JIRA_CLIENT_ID="jira-client-id"
JIRA_CLIENT_SECRET="jira-client-secret"
JIRA_REDIRECT_URI="http://localhost:5001/callback"

