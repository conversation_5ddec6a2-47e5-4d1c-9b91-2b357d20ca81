import os
import base64
import tempfile
import logging
import json
from app.core.config import settings
from cryptography.fernet import Fe<PERSON><PERSON>
from google.cloud import secretmanager
from google.api_core.exceptions import NotFound, AlreadyExists


class SecretManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._load_credentials_from_base64()
        self.project_id = settings.GOOGLE_PROJECT_ID
        self.secret_client = secretmanager.SecretManagerServiceClient()

    def _load_credentials_from_base64(self):
        """
        Decodes the base64 service account key and sets GOOGLE_APPLICATION_CREDENTIALS.
        """
        creds_b64 = settings.GOOGLE_APPLICATION_CREDENTIALS

        if not creds_b64:
            raise EnvironmentError("GOOGLE_APPLICATION_CREDENTIALS is not set")

        decoded = base64.b64decode(creds_b64)

        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        temp_file.write(decoded)
        temp_file.close()

        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temp_file.name
        self.logger.info("Decoded GCP credentials and set GOOGLE_APPLICATION_CREDENTIALS")

    def _get_secret_name(self, secret_id: str) -> str:
        return f"projects/{self.project_id}/secrets/{secret_id}"

    def create_secret(self, secret_id: str, secret_data: str) -> str:
        """
        Creates a new secret in Google Secret Manager.

        Args:
            secret_id: The ID for the secret
            secret_data: The data to store in the secret

        Returns:
            The name of the created secret
        """
        parent = f"projects/{self.project_id}"

        try:
            # Create the secret (if it doesn't already exist)
            self.secret_client.create_secret(
                request={
                    "parent": parent,
                    "secret_id": secret_id,
                    "secret": {"replication": {"automatic": {}}},
                }
            )
            self.logger.info(f"Secret {secret_id} created successfully.")
        except AlreadyExists:
            self.logger.warning(f"Secret {secret_id} already exists.")

        # Add the data as a new version
        response = self.secret_client.add_secret_version(
            request={
                "parent": f"{parent}/secrets/{secret_id}",
                "payload": {"data": secret_data.encode("UTF-8")},
            }
        )

        self.logger.info(f"Added new version to secret {secret_id}")
        return secret_id

    def get_secret(self, secret_id: str) -> str:
        """
        Retrieves a secret from Google Secret Manager.

        Args:
            secret_id: The ID of the secret to retrieve

        Returns:
            The secret data as a string
        """
        name = f"projects/{self.project_id}/secrets/{secret_id}/versions/latest"

        try:
            response = self.secret_client.access_secret_version(request={"name": name})
            self.logger.info(f"Retrieved secret {secret_id}")
            return response.payload.data.decode("UTF-8")
        except NotFound:
            self.logger.error(f"Secret {secret_id} not found")
            raise ValueError(f"Secret {secret_id} not found")

    def store_oauth_tokens(self, user_id: str, mcp_id: str, tool_name: str, tokens: dict) -> str:
        """
        Stores OAuth tokens in Google Secret Manager.

        Args:
            user_id: The ID of the user
            mcp_id: The ID of the MCP module
            tool_name: The name of the tool (e.g., "google_calendar")
            tokens: Dictionary containing the OAuth tokens

        Returns:
            The ID of the created secret
        """
        # Create a unique secret ID
        secret_id = f"oauth-{user_id}-{mcp_id}-{tool_name}"

        # Convert tokens to JSON string
        tokens_json = json.dumps(tokens)

        # Store in Secret Manager
        return self.create_secret(secret_id, tokens_json)

    def get_oauth_tokens(self, secret_id: str) -> dict:
        """
        Retrieves OAuth tokens from Google Secret Manager.

        Args:
            secret_id: The ID of the secret

        Returns:
            Dictionary containing the OAuth tokens
        """
        # Get the secret
        tokens_json = self.get_secret(secret_id)

        # Parse JSON
        return json.loads(tokens_json)

    def delete_secret(self, secret_id: str) -> bool:
        """
        Deletes a secret from Google Secret Manager.

        Args:
            secret_id: The ID of the secret to delete

        Returns:
            True if the secret was deleted successfully, False otherwise
        """
        name = f"projects/{self.project_id}/secrets/{secret_id}"

        try:
            self.secret_client.delete_secret(request={"name": name})
            self.logger.info(f"Deleted secret {secret_id}")
            return True
        except NotFound:
            self.logger.warning(f"Secret {secret_id} not found, nothing to delete")
            return True  # Consider it a success if the secret doesn't exist
        except Exception as e:
            self.logger.error(f"Error deleting secret {secret_id}: {str(e)}")
            raise
