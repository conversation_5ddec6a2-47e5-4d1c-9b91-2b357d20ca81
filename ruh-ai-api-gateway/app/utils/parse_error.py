def parse_error(input_str):
    """
    Args:
        input_str (str): The input string to parse, e.g., "412: Account inactive. Verification email resent."

    Returns:
        dict: A dictionary with keys 'code' (int) and 'message' (str).
    """
    try:
        # Split the string on the first colon
        parts = input_str.split(":", 1)

        # Ensure the input format is valid
        if len(parts) != 2:
            raise ValueError("Invalid format")

        # Clean and extract code and message
        code_str = parts[0].strip()
        message = parts[1].strip()

        # Convert code to integer
        code = int(code_str)

        # Return structured data
        return {"code": code, "message": message}

    except (ValueError, IndexError):
        # Return a default 500 error for any parsing issue
        return {"code": 500, "message": "Internal server error"}
