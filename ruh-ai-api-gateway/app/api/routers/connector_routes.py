from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from rsa import verify

from app.core.auth_guard import role_required
from app.core.security import validate_org_auth_key
from app.schemas.connector import (  # Source management schemas; Google Drive specific schemas; Common models
    AddSourceRequest,
    AddSourceResponse,
    CheckFileAccessRequest,
    CheckFileAccessResponse,
    DeleteSourceRequest,
    DeleteSourceResponse,
    DisconnectDriveRequest,
    DisconnectDriveResponse,
    DriveFileModel,
    FolderInfo,
    GetFileDetailsRequest,
    GetFileDetailsResponse,
    GetFolderByIdRequest,
    GetFolderByIdResponse,
    GetSyncStatusRequest,
    GetSyncStatusResponse,
    ListFilesRequest,
    ListFilesResponse,
    ListSourcesRequest,
    ListSourcesResponse,
    ListTopLevelFoldersRequest,
    ListTopLevelFoldersResponse,
    SourceModel,
    SyncedFileInfo,
    SyncFileByUrlRequest,
    SyncFileByUrlResponse,
    SyncFolderByIdsRequest,
    SyncFolderByIdsResponse,
    SyncSourceRequest,
    SyncSourceResponse,
    TestConnectionRequest,
    TestConnectionResponse,
    UpdateSourceCredentialsRequest,
    UpdateSourceCredentialsResponse,
    ValidateSourceRequest,
    ValidateSourceResponse,
)
from app.services.connector_service import ConnectorServiceClient
from app.utils.parse_error import parse_error

connector_router = APIRouter(prefix="/connector", tags=["connector"])
connector_service = ConnectorServiceClient()


# Source Management Endpoints


@connector_router.post(
    "/sources",
    response_model=AddSourceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Add a new source",
)
async def add_source(
    request: AddSourceRequest, current_user: dict = Depends(role_required(["user"]))
):
    """Add a new source to the organization."""
    print(f"Received request to add source: {request}")

    try:
        print(
            f"Adding source: {request.name} of type {request.type} for organization {request.organisation_id}"
        )
        # Call the connector service
        response = await connector_service.add_source(
            organisation_id=request.organisation_id,
            source_type=request.type,
            name=request.name,
            key=request.key,
            file_ids=request.file_ids,
            jira_url=request.jira_url,
            jira_email=request.jira_email,
            config=request.config,
        )
        print(f"Response from add_source: {response}")
        # Map response to schema
        source_info = None
        if response.source:
            source_info = SourceModel(
                id=response.source.id,
                organisation_id=response.source.organisation_id,
                type=response.source.type,
                name=response.source.name,
                created_at=response.source.created_at,
                updated_at=response.source.updated_at,
                connector_type=response.source.connector_type,
                status=response.source.status,
                last_sync_at=response.source.last_sync_at,
                metadata=dict(response.source.metadata),
            )

        return AddSourceResponse(
            success=response.success, message=response.message, source=source_info
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        print(f"Error adding source: {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.get(
    "/sources",
    response_model=ListSourcesResponse,
    status_code=status.HTTP_200_OK,
    summary="List all sources for organization",
)
async def list_sources(
    organisation_id: str = Query(..., description="Organization ID"),
    current_user: dict = Depends(role_required(["user"])),
):
    """List all sources for the organization."""
    try:
        # Call the connector service
        response = await connector_service.list_sources(organisation_id=organisation_id)

        # Map response to schema
        sources = []
        for source in response.sources:
            sources.append(
                SourceModel(
                    id=source.id,
                    organisation_id=source.organisation_id,
                    type=source.type,
                    name=source.name,
                    created_at=source.created_at,
                    updated_at=source.updated_at,
                    connector_type=source.connector_type,
                    status=source.status,
                    last_sync_at=source.last_sync_at,
                    metadata=dict(source.metadata),
                )
            )

        return ListSourcesResponse(
            success=response.success, message=response.message, sources=sources
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.delete(
    "/sources/{source_id}",
    response_model=DeleteSourceResponse,
    status_code=status.HTTP_200_OK,
    summary="Delete a source",
)
async def delete_source(
    source_id: str,
    organisation_id: str = Query(..., description="Organization ID"),
    current_user: dict = Depends(role_required(["user"])),
):
    """Delete a source from the organization."""
    try:
        # Call the connector service
        response = await connector_service.delete_source(
            source_id=source_id, user_id=current_user["user_id"], organisation_id=organisation_id
        )

        return DeleteSourceResponse(success=response.success, message=response.message)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Google Drive Specific Endpoints


@connector_router.post(
    "/google-drive/disconnect",
    response_model=DisconnectDriveResponse,
    status_code=status.HTTP_200_OK,
    summary="Disconnect Google Drive account",
)
async def disconnect_drive(
    request: DisconnectDriveRequest, current_user: dict = Depends(role_required(["user"]))
):
    """Disconnect Google Drive account."""
    try:
        # Call the connector service
        response = await connector_service.disconnect_drive(organisation_id=request.organisation_id)

        # Map response to schema
        return DisconnectDriveResponse(success=response.success, message=response.message)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/sync",
    response_model=SyncSourceResponse,
    status_code=status.HTTP_200_OK,
    summary="Sync Google Drive files and folders",
)
async def sync_drive(
    request: SyncSourceRequest, current_user: dict = Depends(role_required(["user"]))
):
    """Sync Google Drive files and folders."""
    try:

        # # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")

        # Call the connector service
        response = await connector_service.sync_source(
            source_id=request.source_id,
            organisation_id=request.organisation_id,
            user_id=request.user_id,
            full_sync=request.full_sync,
            specific_items=request.specific_items,
        )

        # Map response to schema
        return SyncSourceResponse(
            success=response.success,
            message=response.message,
            sync_id=response.sync_id,
            sync_status=response.sync_status,
            items_queued=response.items_queued,
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/list-files",
    response_model=ListFilesResponse,
    status_code=status.HTTP_200_OK,
    summary="List Google Drive files",
)
async def list_files(
    request: ListFilesRequest, current_user: dict = Depends(role_required(["user"]))
):
    """List Google Drive files."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")

        # Call the connector service
        response = await connector_service.list_files(
            user_id=request.user_id,
            organisation_id=request.organisation_id,
            folder_id=request.folder_id,
            page=request.page,
            page_size=request.page_size,
        )

        # Map response to schema
        files = []
        for file in response.files:
            files.append(
                DriveFileModel(
                    id=file.id,
                    name=file.name,
                    mime_type=file.mime_type,
                    web_view_link=file.web_view_link,
                    created_time=file.created_time,
                    modified_time=file.modified_time,
                    parent_folder_id=file.parent_folder_id,
                    size=file.size,
                    shared_with=list(file.shared_with),
                    is_folder=file.is_folder,
                    child_count=file.child_count,
                )
            )

        return ListFilesResponse(
            success=response.success,
            message=response.message,
            files=files,
            total_count=response.total_count,
            page=response.page,
            page_size=response.page_size,
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/file-details",
    response_model=GetFileDetailsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Google Drive file details",
)
async def get_file_details(
    request: GetFileDetailsRequest, current_user: dict = Depends(role_required(["user"]))
):
    """Get Google Drive file details."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")

        # Call the connector service
        response = await connector_service.get_file_details(
            user_id=request.user_id,
            file_id=request.file_id,
            organisation_id=request.organisation_id,
        )

        # Map response to schema
        file_data = None
        if response.file:
            file_data = DriveFileModel(
                id=response.file.id,
                name=response.file.name,
                mime_type=response.file.mime_type,
                web_view_link=response.file.web_view_link,
                created_time=response.file.created_time,
                modified_time=response.file.modified_time,
                parent_folder_id=response.file.parent_folder_id,
                size=response.file.size,
                shared_with=list(response.file.shared_with),
                is_folder=response.file.is_folder,
                child_count=response.file.child_count,
            )

        return GetFileDetailsResponse(
            success=response.success, message=response.message, file=file_data
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/check-access",
    response_model=CheckFileAccessResponse,
    status_code=status.HTTP_200_OK,
    summary="Check if user has access to a file",
)
async def check_file_access(
    request: CheckFileAccessRequest, current_user: dict = Depends(role_required(["user"]))
):
    """Check if user has access to a file."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")

        # Call the connector service
        response = await connector_service.check_file_access(
            user_id=request.user_id,
            file_id=request.file_id,
            organisation_id=request.organisation_id,
        )

        # Map response to schema
        return CheckFileAccessResponse(
            success=response.success, message=response.message, has_access=response.has_access
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/folder-by-id",
    response_model=GetFolderByIdResponse,
    status_code=status.HTTP_200_OK,
    summary="Get folder by ID and its contents",
    description="""
    This endpoint retrieves a folder by its ID and its contents.
    
    - Requires user authentication
    - Returns folder details and its children
    """,
)
async def get_folder_by_id(
    request: GetFolderByIdRequest, current_user: dict = Depends(role_required(["user"]))
):
    """Get folder by ID and its contents."""
    try:
        # Call the connector service
        response = await connector_service.get_folder_by_id(
            organisation_id=request.organisation_id, folder_id=request.folder_id
        )

        # Map response to schema
        folder = None
        if response.folder:
            folder = DriveFileModel(
                id=response.folder.id,
                name=response.folder.name,
                mime_type=response.folder.mime_type,
                web_view_link=response.folder.web_view_link,
                created_time=response.folder.created_time,
                modified_time=response.folder.modified_time,
                parent_folder_id=response.folder.parent_folder_id,
                size=response.folder.size,
                shared_with=list(response.folder.shared_with),
                is_folder=response.folder.is_folder,
                child_count=response.folder.child_count,
            )

        children = [
            DriveFileModel(
                id=child.id,
                name=child.name,
                mime_type=child.mime_type,
                web_view_link=child.web_view_link,
                created_time=child.created_time,
                modified_time=child.modified_time,
                parent_folder_id=child.parent_folder_id,
                size=child.size,
                shared_with=list(child.shared_with),
                is_folder=child.is_folder,
                child_count=child.child_count,
            )
            for child in response.children
        ]

        return GetFolderByIdResponse(
            success=response.success, message=response.message, folder=folder, children=children
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/sync-folders",
    response_model=SyncFolderByIdsResponse,
    status_code=status.HTTP_200_OK,
    summary="Sync specific folders by their IDs",
    dependencies=[Depends(validate_org_auth_key)],
)
async def sync_folder_by_ids(request: SyncFolderByIdsRequest):
    """Sync specific folders by their IDs."""
    try:
        print("===============+YESSSSSSSS+==============")

        # Call the connector service
        response = await connector_service.sync_folder_by_ids(
            organisation_id=request.organisation_id,
            folder_ids=request.folder_ids,
        )

        # Map response to schema
        synced_files = []
        for file_info in response.synced_files:
            synced_files.append(
                SyncedFileInfo(
                    file_id=file_info.file_id,
                    file_name=file_info.file_name,
                    drive_url=file_info.drive_url,
                    sync_status=file_info.sync_status,
                    error_message=file_info.error_message,
                )
            )

        return SyncFolderByIdsResponse(
            success=response.success,
            message=response.message,
            synced_files=synced_files,
            total_files=response.total_files,
            successful_syncs=response.successful_syncs,
            failed_syncs=response.failed_syncs,
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


from app.core.security import validate_server_auth_key


@connector_router.post(
    "/google-drive/sync-file-by-url",
    response_model=SyncFileByUrlResponse,
    status_code=status.HTTP_200_OK,
    summary="Sync a file by its URL",
    # dependencies=[Depends(validate_server_auth_key)],
)
async def sync_file_by_url(request: SyncFileByUrlRequest):
    """Sync a file by its URL."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        # if request.user_id != current_user["user_id"]:
        #     raise HTTPException(status_code=403, detail="User ID mismatch")

        # Call the connector service
        response = await connector_service.sync_file_by_url(
            drive_urls=request.drive_url,
            agent_id=request.agent_id,
            organisation_id=request.organisation_id,
            user_id=request.user_id,
        )
        # print("response: ", response)

        # # Map response to schema
        # file_info = None
        # if response.file_info:
        #     file_info = SyncedFileInfo(
        #         file_id=response.file_info.file_id,
        #         file_name=response.file_info.file_name,
        #         drive_url=response.file_info.drive_url,
        #         sync_status=response.file_info.sync_status,
        #         error_message=response.file_info.error_message,
        #     )

        # return SyncFileByUrlResponse(
        #     success=response.success, message=response.message, file_info=file_info
        # )

        # Map response to schema
        synced_files = []
        for file_info in response.synced_files:
            synced_files.append(
                SyncedFileInfo(
                    file_id=file_info.file_id,
                    file_name=file_info.file_name,
                    drive_url=file_info.drive_url,
                    sync_status=file_info.sync_status,
                    error_message=file_info.error_message,
                )
            )

        return SyncFileByUrlResponse(
            success=response.success,
            message=response.message,
            synced_files=synced_files,
            total_files=response.total_files,
            successful_syncs=response.successful_syncs,
            failed_syncs=response.failed_syncs,
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@connector_router.post(
    "/google-drive/top-level-folders",
    response_model=ListTopLevelFoldersResponse,
    status_code=status.HTTP_200_OK,
    summary="List top-level folders in Google Drive",
)
async def list_top_level_folders(
    request: ListTopLevelFoldersRequest, current_user: dict = Depends(role_required(["user"]))
):
    """List top-level folders in Google Drive."""
    try:
        # Validate that the user_id in the request matches the authenticated user
        if request.user_id != current_user["user_id"]:
            raise HTTPException(status_code=403, detail="User ID mismatch")

        # Call the connector service
        response = await connector_service.list_top_level_folders(
            organisation_id=request.organisation_id
        )

        # Map response to schema
        folders = []
        for folder in response.folders:
            folders.append(FolderInfo(id=folder.id, name=folder.name))

        return ListTopLevelFoldersResponse(
            success=response.success, message=response.message, folders=folders
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
