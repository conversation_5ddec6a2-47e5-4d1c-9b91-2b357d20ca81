from typing import Optional

from app.core.auth_guard import role_required
from app.schemas import communication as communication_schemas
from app.services.communication_service import CommunicationServiceClient
from app.shared.constants import ChannelType
from fastapi import APIRouter, Depends, HTTPException

# Create router
communication_router = APIRouter(prefix="/communication", tags=["communication"])
communication_service = CommunicationServiceClient()


# Routes for conversation management
@communication_router.post("/conversation", status_code=201)
async def create_conversation(
    conversation: communication_schemas.ConversationCreate,
    current_user: dict = Depends(role_required(["user"])),
) -> communication_schemas.ConversationResponse:
    try:
        response = await communication_service.create_conversation(
            userId=current_user["user_id"],
            agentId=conversation.agentId,
            title=conversation.title,
            channel=conversation.channel,
            chatType=conversation.chatType,
            summary=conversation.summary,
        )
        return communication_schemas.ConversationResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get(
    "/conversation/{conversation_id}",
    response_model=communication_schemas.ConversationResponse,
)
async def get_conversation(
    conversation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.get_conversation(
            conversationId=conversation_id, userId=current_user["user_id"]
        )
        return communication_schemas.ConversationResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.delete("/conversation/{conversation_id}", status_code=204)
async def delete_conversation(
    conversation_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.delete_conversation(
            conversationId=conversation_id, userId=current_user["user_id"]
        )
        # Return 204 No Content on successful deletion
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get(
    "/conversations", response_model=communication_schemas.ConversationList
)
async def get_conversations(
    agentId: str,
    channel: Optional[ChannelType] = None,
    chatType: Optional[str] = None,
    page: Optional[int] = 1,
    limit: Optional[int] = 10,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.list_conversations(
            userId=current_user["user_id"],
            agentId=agentId,
            channel=channel,
            chatType=chatType,
            page=page,
            limit=limit,
        )

        # Ensure response has the expected structure
        if not response:
            response = {"data": [], "metadata": {}}

        # Ensure data key exists and is a list
        if "data" not in response or not isinstance(response["data"], list):
            response["data"] = []

        # Ensure metadata key exists
        if "metadata" not in response:
            response["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": page,
                "pageSize": limit,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }

        # If data is empty, return empty list with metadata
        if len(response["data"]) == 0:
            return communication_schemas.ConversationList(
                data=[],
                metadata=response["metadata"],
            )

        # Process and return the conversation list
        return communication_schemas.ConversationList(
            data=[
                communication_schemas.ConversationResponse(**conv)
                for conv in response["data"]
            ],
            metadata=response["metadata"],
        )
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


# Routes for message management
@communication_router.post(
    "/message", response_model=communication_schemas.MessageResponse, status_code=201
)
async def add_message(
    message: communication_schemas.MessageCreate,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.create_message(
            conversationId=message.conversationId,
            senderType=message.senderType,
            content=message.content,
            workflowId=message.workflowId,
            workflowResponse=message.workflowResponse,
            userId=current_user["user_id"],
        )
        return communication_schemas.MessageResponse(**response)
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.delete("/message/{message_id}", status_code=204)
async def delete_message(
    message_id: str,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        await communication_service.delete_message(
            messageId=message_id, userId=current_user["user_id"]
        )
        # Return 204 No Content on successful deletion
        return None
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code


@communication_router.get(
    "/messages/{conversation_id}", response_model=communication_schemas.MessageList
)
async def get_messages(
    conversation_id: str,
    page: Optional[int] = 1,
    limit: Optional[int] = 10,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await communication_service.list_messages(
            conversationId=conversation_id,
            userId=current_user["user_id"],
            page=page,
            limit=limit,
        )

        # Ensure response has the expected structure
        if not response:
            response = {"data": [], "metadata": {}}

        # Ensure data key exists and is a list
        if "data" not in response or not isinstance(response["data"], list):
            response["data"] = []

        # Ensure metadata key exists
        if "metadata" not in response:
            response["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": page,
                "pageSize": limit,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }

        # If data is empty, return empty list with metadata
        if len(response["data"]) == 0:
            return communication_schemas.MessageList(
                data=[],
                metadata=response["metadata"],
            )

        # Process and return the message list
        return communication_schemas.MessageList(
            data=[
                communication_schemas.MessageResponse(**msg) for msg in response["data"]
            ],
            metadata=response["metadata"],
        )
    except HTTPException as e:
        raise e  # Re-raise the exception to get proper status code
