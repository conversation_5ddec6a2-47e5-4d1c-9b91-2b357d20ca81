from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query

from app.services.mcp_service import MCPServiceClient
from app.schemas.mcp import (
    DeploymentStatus,
    DeploymentUpdatePayload,
    DeploymentUpdateResponse,
    EnvKeyValue,
    GetMcpEnvVarsResponseApi,
    MCPPatchPayload,
    MCPsByIdsRequest,
    MCPsByIdsResponse,
    McpComponentCategory,
    McpEnvKeyDefinitionApi,
    PaginatedMCPResponse,
    PaginationMetadata,
    StatusEnum,
    ToggleMcpVisibilityResponseAPI,
    ToolOutputSchemaUpdatePayload,
    UpdateMCPResponse,
    MCPResponse,
    MCPCreate,
    MCPInDB,
    CreateMCPResponse,
    DeleteMCPResponse,
    UpdateMcpEnvVarsRequestApi,
    UpdateMcpEnvVarsResponseApi,
    VisibilityEnum,
    McpCategory,
    MCPUrlsRequest,
    MCPUrlItem,
    MCPUrlsResponse,
    # Container Schemas
    CreateContainerRequest,
    CreateContainerResponse,
    StopContainerResponse,
    GetContainerStatusResponse,
    DeleteContainerResponse,
)
from enum import Enum
from typing import Optional

from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.core.security import (
    validate_server_auth_key,
    validate_agent_platform_auth_key,
    validate_workflow_service_auth_key,
)
from app.services.user_service import UserServiceClient
from app.utils.parse_error import parse_error
import json

mcp_router = APIRouter(prefix="/mcps", tags=["mcps"])
mcp_marketplace = APIRouter(prefix="/marketplace", tags=["mcps"])
mcp_service = MCPServiceClient()
user_service = UserServiceClient()


@mcp_router.post("", response_model=CreateMCPResponse)
async def create_mcp(
    mcp_data: MCPCreate, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Creates a new MCP.
    """
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]

        print(f"[DEBUG API] Owner details from user_service: {user_details}")

        # Initialize github_access_token to None
        github_access_token = None

        # Handle Git repository creation workflow
        if mcp_data.config is None:
            github_access_token = await user_service.get_user_github_token(current_user["user_id"])
            if not github_access_token:
                raise HTTPException(
                    status_code=400,
                    detail="GitHub token not found. Please authenticate with GitHub first.",
                )
            mcp_data.github_access_token = github_access_token

        response = await mcp_service.createMCP(
            logo=mcp_data.logo,
            name=mcp_data.name,
            description=mcp_data.description,
            owner_details=user_details,
            owner_type=current_user["role"],
            git_url=mcp_data.git_url,
            config=mcp_data.config,
            github_access_token=github_access_token,
            git_branch=mcp_data.git_branch,
            category=mcp_data.category,
            visibility=mcp_data.visibility,
            tags=mcp_data.tags,
            status=mcp_data.status,
            user_ids=mcp_data.user_ids,
            env_keys=mcp_data.env_keys,
            mcp_type=mcp_data.mcp_type,
            component_category=(
                mcp_data.component_category.value if mcp_data.component_category else None
            ),
            oauth_details=mcp_data.oauth_details,
        )

        return CreateMCPResponse(
            success=response.success,
            message=response.message,
            mcp_id=response.mcp.id if hasattr(response, "mcp") and response.mcp else None,
        )
    except Exception as e:
        print(f"[DEBUG] Error in create_mcp: {str(e)}")
        error_details = parse_error(str(e))  # Your existing error parsing
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("/{mcp_id}", response_model=MCPResponse)
async def get_mcp(mcp_id: str, current_user: dict = Depends(role_required(["user", "admin"]))):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id, current_user["user_id"])
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(
            success=response.success,
            message=response.message,
            mcp=MCPInDB(**mcp_dict),  # Use MCPInDB to parse the dictionary
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.patch("/{mcp_id}", response_model=UpdateMCPResponse)
async def update_mcp(
    mcp_id: str,
    payload: MCPPatchPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Partially updates an MCP by ID.
    Only the fields provided in the request body will be updated.
    """
    try:
        print(f"[DEBUG] Update MCP request: {payload}")
        validate_response = await user_service.validate_user(
            current_user["user_id"]
        )  # Assuming user_service exists
        if not validate_response.get("success"):
            raise HTTPException(
                status_code=400, detail=validate_response.get("message", "User validation failed")
            )

        user_details = validate_response["user"]
        print(f"[DEBUG] User details: {user_details}")

        # Only add github_access_token if we're actually updating Git-related fields
        update_data = payload.model_dump(exclude_unset=True)

        # Check if this is a Git-based update (updating git_url, git_branch, or github_access_token)
        git_fields = {"git_url", "git_branch", "github_access_token"}
        is_git_update = any(field in update_data for field in git_fields)

        # If it's a Git-based update and config is not provided, we need github_access_token
        if is_git_update and not payload.config:
            github_access_token = await user_service.get_user_github_token(current_user["user_id"])
            if not github_access_token:
                raise HTTPException(
                    status_code=400,
                    detail="GitHub token not found. Please authenticate with GitHub first.",
                )
            update_data["github_access_token"] = github_access_token
            print(f"[DEBUG] Added github_access_token for Git-based update: {github_access_token}")

        # if payload.env_keys is not None:
        #     update_data["env_keys"] = [env_key.model_dump() for env_key in payload.env_keys]

        print(f"[DEBUG] Final update_data: {update_data}")

        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update.")

        response = await mcp_service.updateMCP(
            mcp_id=mcp_id, update_fields=update_data, current_owner_details=user_details
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)
        return UpdateMCPResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in patch_mcp: {str(e)}")
        error_details = parse_error(str(e))  # Your existing error parser
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected error occurred."),
        )


@mcp_router.delete("/{mcp_id}", response_model=DeleteMCPResponse)
async def delete_mcp(mcp_id: str, current_user: dict = Depends(role_required(["user", "admin"]))):
    """
    Deletes an MCP by ID.

    This endpoint deletes an MCP by its ID.
    """
    try:
        user_id = current_user["user_id"]
        response = await mcp_service.deleteMCP(mcp_id, user_id)

        return DeleteMCPResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("", response_model=PaginatedMCPResponse, description="get MCPs for admin & user")
async def list_mcps(
    current_user: dict = Depends(role_required(["user", "admin"])),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    category: Optional[McpCategory] = Query(None, description="Filter by category"),
    visibility: Optional[VisibilityEnum] = Query(None, description="Filter by visibility"),
    search: Optional[str] = Query(None, description="Search term to filter by name or description"),
    status: Optional[StatusEnum] = Query(None, description="Filter by status"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    deployment_status: Optional[DeploymentStatus] = Query(
        None, description="Filter by deployment status"
    ),
    component_category: Optional[McpComponentCategory] = Query(
        None, description="Filter by component category"
    ),
):
    """
    Retrieves a paginated list of MCPs, with optional filters.
    - Admins can see all MCPs (subject to filters).
    - Users can only see their own MCPs (subject to filters).
    """
    try:
        print(
            f"[DEBUG] MCPs list request: page={page}, page_size={page_size}, category={category}, visibility={visibility}, status={status}, tags={tags}"
        )
        owner_id_filter: Optional[str] = None
        if current_user.get("role") == "user":
            owner_id_filter = current_user.get("user_id")
            if not owner_id_filter:
                raise HTTPException(status_code=403, detail="User ID not found for user role.")

        # The gRPC client expects Python enums (or their .value) which it maps to proto enums
        response = await mcp_service.listMCPS(
            page=page,
            page_size=page_size,
            owner_id=owner_id_filter,
            category=category.value if category else None,
            visibility=visibility,
            status=status,
            tags=tags,
            deployment_status=deployment_status,
            search=search,
            component_category=component_category.value if component_category else None,
        )

        print("[DEBUG] Response: ", response)
        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

            mcps.append(MCPInDB.parse_obj(mcp_dict))

        print("[DEBUG] Returning mcps: ", mcps)
        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=response.total,
            totalPages=response.total_pages,
            currentPage=response.page,
            pageSize=page_size,
            hasNextPage=response.page < response.total_pages,
            hasPreviousPage=response.page > 1,
        )

        return PaginatedMCPResponse(data=mcps, metadata=metadata)
    except Exception as e:
        print(f"[DEBUG] Error details: {e}")
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/agent-platform/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_mcp_for_agent_platform(
    mcp_id: str,
):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/orchestration/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_mcp_for_orchestration(mcp_id: str):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/workflow-service/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_workflow_service_auth_key)],
)
async def get_mcp_for_workflow_service(mcp_id: str):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post("/by-ids", response_model=MCPsByIdsResponse)
async def get_mcps_by_ids(
    request: MCPsByIdsRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Retrieves multiple MCPs by their IDs.

    This endpoint allows fetching multiple MCPs in a single request by providing a list of MCP IDs.

    ## Request Body
    - **ids**: List of MCP IDs to retrieve

    ## Response
    Returns a list of MCPs matching the provided IDs and the total count.

    ## Example
    ```
    POST /mcps/by-ids
    {
        "ids": ["mcp-id-1", "mcp-id-2", "mcp-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")

        print(f"[DEBUG] Fetching MCPs by IDs: {request.ids}")
        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)

        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            mcps.append(MCPInDB.model_validate(mcp_dict))

        return MCPsByIdsResponse(
            success=True, message=f"Retrieved {len(mcps)} MCPs", mcps=mcps, total=len(mcps)
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_mcps_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post(
    "/agent-platform/by-ids",
    response_model=MCPsByIdsResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_mcps_by_ids_for_agent_platform(request: MCPsByIdsRequest):
    """
    Retrieves multiple MCPs by their IDs.

    This endpoint allows fetching multiple MCPs in a single request by providing a list of MCP IDs.

    ## Request Body
    - **ids**: List of MCP IDs to retrieve

    ## Response
    Returns a list of MCPs matching the provided IDs and the total count.

    ## Example
    ```
    POST /mcps/by-ids
    {
        "ids": ["mcp-id-1", "mcp-id-2", "mcp-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")

        print(f"[DEBUG] Fetching MCPs by IDs: {request.ids}")
        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)

        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            mcps.append(MCPInDB.model_validate(mcp_dict))

        return MCPsByIdsResponse(
            success=True, message=f"Retrieved {len(mcps)} MCPs", mcps=mcps, total=len(mcps)
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_mcps_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post(
    "/orchestration/urls",
    response_model=MCPUrlsResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_mcps_urls_for_orchestration(request: MCPUrlsRequest):
    """
    Bulk fetches the `url` field for multiple MCPs by their IDs.
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")
        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)
        config = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            raw_urls = mcp_dict.get("config")
            parsed_urls = []

            # Parse only if it's a string
            if isinstance(raw_urls, str):
                try:
                    parsed_urls = json.loads(raw_urls)
                except json.JSONDecodeError:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Invalid JSON in 'config' field for MCP ID {mcp_dict.get('id')}",
                    )
            elif isinstance(raw_urls, list):
                parsed_urls = raw_urls

            # Get the first URL if available
            url = (
                parsed_urls[0].get("url")
                if parsed_urls and isinstance(parsed_urls[0], dict)
                else None
            )

            config.append(MCPUrlItem(mcp_id=mcp_dict.get("id"), url=url))

        return MCPUrlsResponse(
            success=True, message=f"Retrieved {len(config)} MCP config", config=config
        )

    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.put(
    "/deployment",
    response_model=DeploymentUpdateResponse,
)
async def update_mcp_deployment(
    payload: DeploymentUpdatePayload,
):
    """
    Updates the deployment status of an MCP.

    This endpoint is used to update the deployment status of an MCP.

    ## Request Body
    - **mcp_id**: The ID of the MCP to update
    - **deployment_status**: The new deployment status (pending/completed)
    - **type**: The type of the deployment (sse, http, stdio)
    - **image_name**: Optional image name
    - **error_message**: Optional error message
    - **url**: Optional URL

    ## Response
    Returns the updated MCP with the new deployment status.
    """
    try:
        print(f"[DEBUG] Update MCP deployment request: {payload}")

        # Call service method
        response = await mcp_service.update_deployment_status(
            mcp_id=payload.mcp_id,
            deployment_status=payload.deployment_status,
            type=payload.type,
            image_name=payload.image_name,
            error_message=payload.error_message,
            url=payload.url,
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        # Convert response to dict
        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return DeploymentUpdateResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in update_mcp_deployment: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected error occurred."),
        )


# Container Management Endpoints
@mcp_router.post(
    "/containers/create",
    response_model=CreateContainerResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def create_container_endpoint(payload: CreateContainerRequest):
    """
    Creates a new container for an MCP.
    """
    try:
        print(
            f"[DEBUG API] Create container request for mcp_id: {payload.mcp_id} by user_id: {payload.user_id}"
        )
        response = await mcp_service.create_container(
            mcp_id=payload.mcp_id,
            user_id=payload.user_id,
            type=payload.type,
            env=payload.env,
        )
        return CreateContainerResponse(
            success=response.success,
            message=response.message,
            container_id=getattr(response, "container_id", None),
        )
    except Exception as e:
        print(f"[DEBUG] Error in create_container_endpoint: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "Failed to create container."),
        )


@mcp_router.post("/containers/{container_id}/stop", response_model=StopContainerResponse)
async def stop_container_endpoint(
    container_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),  # Ensure user has rights
):
    """
    Stops a running container.
    """
    try:
        # Optional: Add logic here to verify if current_user is allowed to stop this container_id
        # This might involve a lookup in mcp-service or checking ownership.
        print(
            f"[DEBUG API] Stop container request for container_id: {container_id} by user_id: {current_user.get('user_id')}"
        )
        response = await mcp_service.stop_container(container_id=container_id)
        # Assuming gRPC response has 'success' and 'message'
        return StopContainerResponse(success=response.success, message=response.message)
    except Exception as e:
        print(f"[DEBUG] Error in stop_container_endpoint: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "Failed to stop container."),
        )


@mcp_router.delete(
    "/containers/{container_id}",
    response_model=DeleteContainerResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def delete_container_endpoint(container_id: str):
    """
    Deletes a container.
    This typically involves stopping it and removing its resources.
    """
    try:
        # Optional: Add logic here to verify if current_user is allowed to delete this container_id
        print(f"[DEBUG API] Delete container request for container_id: {container_id}")
        response = await mcp_service.delete_container(container_id=container_id)
        # Assuming gRPC response has 'success' and 'message'
        return DeleteContainerResponse(success=response.success, message=response.message)
    except Exception as e:
        print(f"[DEBUG] Error in delete_container_endpoint: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "Failed to delete container."),
        )


@mcp_router.put("/tool/output-schema", response_model=DeploymentUpdateResponse)
async def update_tool_output_schema(
    payload: ToolOutputSchemaUpdatePayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Updates the output schema for a tool in an MCP configuration.

    ## Request Body
    - **mcp_id**: The ID of the MCP
    - **tool_name**: The tool's name whose output schema should be updated
    - **output_schema_json**: The new output schema as a JSON object

    ## Response
    Returns the updated MCP config.
    """
    try:
        print(f"[DEBUG] UpdateToolOutputSchema payload: {payload}")

        response = await mcp_service.update_tool_output_schema(
            mcp_id=payload.mcp_id,
            tool_name=payload.tool_name,
            output_schema=payload.output_schema_json,  # 👇 we serialize this in the service layer
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return DeploymentUpdateResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in update_tool_output_schema: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@mcp_router.post("/{mcp_id}/toggle-visibility", response_model=ToggleMcpVisibilityResponseAPI)
async def toggle_mcp_visibility_api(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Toggles the visibility of an MCP between PUBLIC and PRIVATE.
    Only the owner of the MCP can perform this action.
    """
    try:
        print(
            f"[API] User {current_user['user_id']} attempting to toggle visibility for MCP {mcp_id}"
        )

        # 1. Call the gRPC service client - this returns a PROTOBUF OBJECT
        grpc_proto_response = await mcp_service.toggle_mcp_visibility(
            mcp_id=mcp_id,
            user_id=current_user["user_id"],
        )

        # 2. Convert the protobuf object to a Python dictionary
        grpc_response_as_dict = MessageToDict(grpc_proto_response, preserving_proto_field_name=True)
        print(f"[API] gRPC response (converted to dict): {grpc_response_as_dict}")

        # 3. Check the 'success' field from the DICTIONARY
        if not grpc_response_as_dict.get("success"):
            detail_message = grpc_response_as_dict.get(
                "message", "Failed to toggle MCP visibility."
            )
            status_code = 400
            if "not found" in detail_message.lower():
                status_code = 404
            elif (
                "forbidden" in detail_message.lower() or "not authorized" in detail_message.lower()
            ):
                status_code = 403
            raise HTTPException(status_code=status_code, detail=detail_message)

        return ToggleMcpVisibilityResponseAPI(
            success=True, message=grpc_response_as_dict.get("message")
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"[API_ERROR] Exception type: {type(e)}, args: {e.args}")
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}, Original error: {str(e)}")
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected server error occurred."),
        )


@mcp_router.put("/env-update", response_model=UpdateMcpEnvVarsResponseApi)
async def api_update_mcp_env_vars(
    mcp_id: str,
    payload: UpdateMcpEnvVarsRequestApi,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    user_id = current_user.get("user_id")

    try:

        response = await mcp_service.update_mcp_env_vars(
            user_id=user_id, mcp_id=mcp_id, env_key_values=payload.env_key_values
        )

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        response_dict = MessageToDict(response, preserving_proto_field_name=True)

        return UpdateMcpEnvVarsResponseApi(
            success=response_dict["success"],
            message=response_dict["message"],
            user_mcp_assignment_id=response_dict.get("user_mcp_assignment_id"),
            env_credential_status=response_dict.get("env_credential_status"),
        )

    except Exception as e:
        print(f"[ERROR] {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("/get-env-details/{mcp_id}", response_model=GetMcpEnvVarsResponseApi)
async def api_get_mcp_env_vars(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    user_id = current_user.get("user_id")

    try:
        response = await mcp_service.get_mcp_env_vars(user_id=user_id, mcp_id=mcp_id)

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        response = MessageToDict(response, preserving_proto_field_name=True)

        print(f"[RESPONSE] {response}")

        return GetMcpEnvVarsResponseApi(
            success=response["success"],
            message=response["message"],
            env_key_values=[EnvKeyValue(**env) for env in response.get("env_key_values", [])],
            defined_env_keys=[
                McpEnvKeyDefinitionApi(**key) for key in response.get("defined_env_keys", [])
            ],
            user_mcp_assignment_id=response.get("user_mcp_assignment_id"),
            env_credential_status=response.get("env_credential_status"),
        )

    except Exception as e:
        print(f"[ERROR] {e}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post("/refresh/{mcp_id}", response_model=MCPResponse)
async def refresh_mcp(
    mcp_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Refreshes the MCP's tool configuration from its config URLs.
    """
    try:
        response = await mcp_service.refresh_mcp(mcp_id)

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        return MCPResponse(
            success=response.success,
            message=response.message,
            mcp=MCPInDB(**mcp_dict),
        )
    except Exception as e:
        print(f"[ERROR] Unexpected error in refresh_mcp: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
