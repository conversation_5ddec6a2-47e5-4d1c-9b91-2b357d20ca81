from fastapi import APIRouter, Depends, HTTPException, status
from typing import Optional, List

from app.core.security import validate_org_auth_key
from app.services.search_service import SearchServiceClient
from app.schemas.search import (
    SearchSimilarDocumentsRequest,
    SearchSimilarDocumentsResponse,
    BatchSearchSimilarDocumentsRequest,
    BatchSearchSimilarDocumentsResponse,
    AdvancedSearchRequest,
    AdvancedSearchResponse,
    SearchSuggestionsRequest,
    SearchSuggestionsResponse,
    SearchBySourceRequest,
    SearchBySourceResponse,
    SearchAnalyticsRequest,
    SearchAnalyticsResponse,
    SearchResultItem,
    QueryResults,
    EntityInfo,
    RelationshipInfo,
    GraphContext,
    SearchSuggestion,
    SearchAnalytics,
    SourceSearchResult
)

from app.utils.parse_error import parse_error


search_router = APIRouter(prefix="/search", tags=["search"])
search_service = SearchServiceClient()


@search_router.post(
    "/similar-documents",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SearchSimilarDocumentsResponse,
    status_code=status.HTTP_200_OK,
    summary="Search for semantically similar documents",
)
async def search_similar_documents(
    request: SearchSimilarDocumentsRequest,
):
    """Search for documents semantically similar to a query."""
    try:
        # Call the search service
        response = await search_service.search_similar_documents(
            user_id=request.user_id,
            query_text=request.query_text,
            top_k=request.top_k,
            agent_id=request.agent_id,
            organisation_id=request.organisation_id,
            file_ids=request.file_ids,
            least_score=request.least_score
        )
        
        # Map response to schema
        results = []
        for result in response.results:
            results.append(SearchResultItem(
                file_id=result.file_id,
                file_name=result.file_name,
                mime_type=result.mime_type,
                web_view_link=result.web_view_link,
                created_time=result.created_time,
                modified_time=result.modified_time,
                score=result.score,
                vector_id=result.vector_id,
                chunk_text=result.chunk_text,
                search_type=result.search_type
            ))
        
        # Map graph context if present
        graph_context = None
        if response.graph_context:
            # Map all entities
            entities = []
            for entity in response.graph_context.entities:
                entities.append(EntityInfo(
                    id=entity.id,
                    name=entity.name,
                    type=entity.type,
                    properties=dict(entity.properties)
                ))
            
            # Map all relationships
            relationships = []
            for relationship in response.graph_context.relationships:
                relationships.append(RelationshipInfo(
                    source_id=relationship.source_id,
                    target_id=relationship.target_id,
                    relationship_type=relationship.relationship_type,
                    properties=dict(relationship.properties)
                ))
            
            graph_context = GraphContext(
                entities=entities,
                relationships=relationships
            )
        
        return SearchSimilarDocumentsResponse(
            success=response.success,
            message=response.message,
            results=results,
            graph_context=graph_context
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@search_router.post(
    "/batch-similar-documents",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=BatchSearchSimilarDocumentsResponse,
    status_code=status.HTTP_200_OK,
    summary="Batch search for semantically similar documents",
)
async def batch_search_similar_documents(
    request: BatchSearchSimilarDocumentsRequest,
):
    """Batch search for documents semantically similar to multiple queries."""
    try:
        # Call the search service
        response = await search_service.batch_search_similar_documents(
            user_id=request.user_id,
            query_texts=request.query_texts,
            top_k=request.top_k,
            agent_id=request.agent_id,
            organisation_id=request.organisation_id,
            file_ids=request.file_ids,
            least_score=request.least_score
        )
        
        # Map response to schema
        query_results = []
        for query_result in response.query_results:
            results = []
            for result in query_result.results:
                results.append(SearchResultItem(
                    file_id=result.file_id,
                    file_name=result.file_name,
                    mime_type=result.mime_type,
                    web_view_link=result.web_view_link,
                    created_time=result.created_time,
                    modified_time=result.modified_time,
                    score=result.score,
                    vector_id=result.vector_id,
                    chunk_text=result.chunk_text,
                    search_type=result.search_type
                ))
            
            query_results.append(QueryResults(
                query_text=query_result.query_text,
                results=results
            ))
        
        return BatchSearchSimilarDocumentsResponse(
            success=response.success,
            message=response.message,
            query_results=query_results
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@search_router.post(
    "/advanced",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=AdvancedSearchResponse,
    status_code=status.HTTP_200_OK,
    summary="Perform advanced search with filters and sorting",
)
async def advanced_search(
    request: AdvancedSearchRequest,
):
    """Perform advanced search with filters and sorting options."""
    try:
        # Call the search service
        response = await search_service.advanced_search(
            user_id=request.user_id,
            query_text=request.query_text,
            organisation_id=request.organisation_id,
            search_type=request.search_type,
            search_scope=request.search_scope,
            filters=request.filters,
            top_k=request.top_k,
            least_score=request.least_score,
            agent_id=request.agent_id,
            include_graph_context=request.include_graph_context
        )
        
        # Map response to schema
        results = []
        for result in response.results:
            results.append(SearchResultItem(
                file_id=result.file_id,
                file_name=result.file_name,
                mime_type=result.mime_type,
                web_view_link=result.web_view_link,
                created_time=result.created_time,
                modified_time=result.modified_time,
                score=result.score,
                vector_id=result.vector_id,
                chunk_text=result.chunk_text,
                search_type=result.search_type
            ))
        
        return AdvancedSearchResponse(
            success=response.success,
            message=response.message,
            results=results,
            total_count=response.total_count,
            has_more=response.has_more
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@search_router.post(
    "/suggestions",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SearchSuggestionsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get search suggestions based on partial query",
)
async def get_search_suggestions(
    request: SearchSuggestionsRequest,
):
    """Get search suggestions based on partial query input."""
    try:
        # Call the search service
        response = await search_service.get_search_suggestions(
            user_id=request.user_id,
            organisation_id=request.organisation_id,
            partial_query=request.partial_query,
            max_suggestions=request.max_suggestions,
            search_scope=request.search_scope,
            source_ids=request.source_ids
        )
        
        # Map response to schema
        suggestions = []
        for suggestion in response.suggestions:
            suggestions.append(SearchSuggestion(
                text=suggestion.text,
                score=suggestion.score,
                type=suggestion.type
            ))
        
        return SearchSuggestionsResponse(
            success=response.success,
            message=response.message,
            suggestions=suggestions
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@search_router.post(
    "/by-source",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SearchBySourceResponse,
    status_code=status.HTTP_200_OK,
    summary="Search within specific sources",
)
async def search_by_source(
    request: SearchBySourceRequest,
):
    """Search for content within specific sources."""
    try:
        # Call the search service
        response = await search_service.search_by_source(
            user_id=request.user_id,
            organisation_id=request.organisation_id,
            query_text=request.query_text,
            source_id=request.source_id,
            search_type=request.search_type,
            top_k=request.top_k,
            least_score=request.least_score,
            agent_id=request.agent_id,
            filters=request.filters
        )
        
        # Map response to schema
        source_results = []
        for source_result in response.source_results:
            results = []
            for result in source_result.results:
                results.append(SearchResultItem(
                    file_id=result.file_id,
                    file_name=result.file_name,
                    mime_type=result.mime_type,
                    web_view_link=result.web_view_link,
                    created_time=result.created_time,
                    modified_time=result.modified_time,
                    score=result.score,
                    vector_id=result.vector_id,
                    chunk_text=result.chunk_text,
                    search_type=result.search_type
                ))
            
            source_results.append(SourceSearchResult(
                source_id=source_result.source_id,
                source_name=source_result.source_name,
                source_type=source_result.source_type,
                results=results
            ))
        
        return SearchBySourceResponse(
            success=response.success,
            message=response.message,
            source_results=source_results
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@search_router.post(
    "/analytics",
    dependencies=[Depends(validate_org_auth_key)],
    response_model=SearchAnalyticsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get search analytics and insights",
)
async def get_search_analytics(
    request: SearchAnalyticsRequest,
):
    """Get search analytics and insights for the organization."""
    try:
        # Call the search service
        response = await search_service.get_search_analytics(
            user_id=request.user_id,
            organisation_id=request.organisation_id,
            date_from=request.start_date,
            date_to=request.end_date,
            source_ids=request.source_ids,
            department_ids=request.department_ids
        )
        
        # Map response to schema
        analytics = SearchAnalytics(
            total_searches=response.analytics.total_searches,
            unique_users=response.analytics.unique_users,
            avg_response_time=response.analytics.avg_response_time,
            top_queries=list(response.analytics.top_queries),
            search_trends=dict(response.analytics.search_trends),
            source_usage=dict(response.analytics.source_usage)
        )
        
        return SearchAnalyticsResponse(
            success=response.success,
            message=response.message,
            analytics=analytics
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])