import traceback
import logging
import json
from fastapi import APIRouter, HTTPException, Response, Body, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from app.services.workflow_service import WorkflowServiceClient


# Set up logging
logger = logging.getLogger(__name__)

# Create the router
workflow_builder_router = APIRouter(prefix="/components", tags=["components"])
workflow_service = WorkflowServiceClient()

# Define Pydantic models for request validation
class RequestNode(BaseModel):
    id: str
    type: str
    data: Dict[str, Any]
    position: Dict[str, float] = Field(default_factory=lambda: {"x": 0, "y": 0})

class RequestEdge(BaseModel):
    id: str
    source: str
    target: str
    sourceHandle: Optional[str] = None
    targetHandle: Optional[str] = None

class WorkflowExecutionRequest(BaseModel):
    nodes: List[RequestNode]
    edges: List[RequestEdge]
    workflow_name: str = "Untitled Workflow"
    workflow_id: Optional[str] = None
    execution_id: Optional[str] = None

class MissingField(BaseModel):
    node_id: str
    node_label: str
    input_name: str
    input_display_name: str
    is_handle: bool

class ValidationResponse(BaseModel):
    is_valid: bool
    missing_fields: List[MissingField] = []
    errors: List[str] = []
    warnings: List[str] = []
    error: Optional[str] = None

@workflow_builder_router.get("")
async def list_components(refresh: bool = False, response: Response = None):
    """
    Returns component definitions. Use ?refresh=true to force rediscovery.

    Args:
        refresh: Whether to force a refresh of the component registry.
        response: The response object for adding headers.

    Returns:
        A dictionary of component definitions by category and name.
    """
    try:
        logger.info(f"Listing components (refresh={refresh})")
        components_response = await workflow_service.discover_components(force_refresh=refresh)

        if not components_response or not hasattr(components_response, 'components'):
            logger.warning("No components found")
            raise HTTPException(status_code=404, detail="No components found or loaded.")

        # Organize components by category and name
        components_by_category = {}
        for component in components_response.components:
            category = component.category
            name = component.name

            # Initialize category if it doesn't exist
            if category not in components_by_category:
                components_by_category[category] = {}

            # Process inputs
            inputs = []
            for input_def in component.inputs:
                # Process visibility rules
                visibility_rules = None
                if input_def.visibility_rules:
                    visibility_rules = []
                    for rule in input_def.visibility_rules:
                        visibility_rules.append({
                            "field_name": rule.field_name,
                            "field_value": rule.field_value,
                            "operator": rule.operator
                        })

                # Process requirement rules
                requirement_rules = None
                if input_def.requirement_rules:
                    requirement_rules = []
                    for rule in input_def.requirement_rules:
                        requirement_rules.append({
                            "field_name": rule.field_name,
                            "field_value": rule.field_value,
                            "operator": rule.operator
                        })

                # Convert value to appropriate type based on input_type
                value = input_def.value
                if input_def.input_type == "int":
                    try:
                        value = int(value) if value else 0
                    except ValueError:
                        value = 0
                elif input_def.input_type == "float":
                    try:
                        value = float(value) if value else 0.0
                    except ValueError:
                        value = 0.0
                elif input_def.input_type == "bool":
                    value = value.lower() == "true" if value else False
                elif input_def.input_type == "dict":
                    try:
                        value = json.loads(value) if value and value != "{}" else {}
                    except json.JSONDecodeError:
                        value = {}
                elif input_def.input_type == "list":
                    try:
                        value = json.loads(value) if value and value != "[]" else []
                    except json.JSONDecodeError:
                        value = []
                elif input_def.is_handle and value == "None":
                    value = None

                # Convert options to list if present
                options = list(input_def.options) if input_def.options else None

                # Create input dictionary with all fields
                input_dict = {
                    "name": input_def.name,
                    "display_name": input_def.display_name,
                    "info": input_def.info,
                    "input_type": input_def.input_type,
                    "input_types": list(input_def.input_types) if input_def.input_types else None,
                    "required": input_def.required,
                    "is_handle": input_def.is_handle,
                    "is_list": input_def.is_list,
                    "real_time_refresh": input_def.real_time_refresh,
                    "advanced": input_def.advanced,
                    "value": value,
                    "options": options,
                    "visibility_rules": visibility_rules,
                    "visibility_logic": input_def.visibility_logic if input_def.visibility_logic else "OR",
                    "requirement_rules": requirement_rules,
                    "requirement_logic": input_def.requirement_logic if input_def.requirement_logic else "OR"
                }

                # Add credential-specific fields if this is a credential input
                if input_def.input_type == "credential":
                    input_dict["credential_type"] = input_def.credential_type if input_def.credential_type else "api_key"
                    input_dict["use_credential_id"] = input_def.use_credential_id
                    input_dict["credential_id"] = input_def.credential_id

                inputs.append(input_dict)

            # Process outputs
            outputs = []
            for output_def in component.outputs:
                output_dict = {
                    "name": output_def.name,
                    "display_name": output_def.display_name,
                    "output_type": output_def.output_type,
                    "semantic_type": output_def.semantic_type if output_def.semantic_type else None,
                    "method": None  # Use null instead of empty string
                }
                outputs.append(output_dict)

            # Create component details
            component_details = {
                "name": name,
                "display_name": component.display_name,
                "description": component.description,
                "category": category,
                "icon": component.icon,
                "beta": component.beta,
                "requires_approval": component.requires_approval,
                "inputs": inputs,
                "outputs": outputs,
                "is_valid": component.is_valid,
                "path": component.path if component.path else f"components.{category.lower()}.{name.lower().replace('module', '_module').replace('node', '_node').replace('component', '_component').replace('ai', '_ai')}.{name}",
                "interface_issues": []
            }

            # Add the component to its category
            components_by_category[category][name] = component_details

        # Add cache headers
        if response:
            if refresh:
                # No caching for forced refresh
                response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate"
            else:
                # Cache for 5 minutes
                response.headers["Cache-Control"] = "public, max-age=300"
                response.headers["Vary"] = "Accept-Encoding"

        return components_by_category
    except Exception as e:
        logger.error(f"Error listing components: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing components: {str(e)}")

@workflow_builder_router.post("/validate_workflow", response_model=ValidationResponse)
async def validate_workflow(payload: WorkflowExecutionRequest):
    """
    Validates a workflow and returns any missing required fields.

    Args:
        payload: The workflow execution request containing nodes and edges.

    Returns:
        A validation response with information about missing required fields.
    """
    try:
        logger.info(f"Validating workflow with {len(payload.nodes)} nodes and {len(payload.edges)} edges")

        # Convert Pydantic models to dictionaries
        nodes = [node.model_dump() for node in payload.nodes]
        edges = [edge.model_dump() for edge in payload.edges]

        # Call the gRPC service to validate the workflow
        validation_response = await workflow_service.validate_workflow(
            nodes=nodes,
            edges=edges,
            workflow_name=payload.workflow_name,
            workflow_id=payload.workflow_id,
            execution_id=payload.execution_id
        )

        # Convert the gRPC response to a Pydantic model
        missing_fields = []
        for field in validation_response.missing_fields:
            missing_fields.append({
                "node_id": field.node_id,
                "node_label": field.node_label,
                "input_name": field.input_name,
                "input_display_name": field.input_display_name,
                "is_handle": field.is_handle
            })

        errors = list(validation_response.errors)
        warnings = list(validation_response.warnings)
        error = validation_response.error if validation_response.HasField("error") else None

        return ValidationResponse(
            is_valid=validation_response.is_valid,
            missing_fields=missing_fields,
            errors=errors,
            warnings=warnings,
            error=error
        )
    except Exception as e:
        logger.error(f"Error validating workflow: {e}")
        import traceback
        traceback.print_exc()
        return ValidationResponse(is_valid=False, error=f"Error validating workflow: {str(e)}")

