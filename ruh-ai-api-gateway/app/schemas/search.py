from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict
from enum import Enum

# Enums from search.proto
class SearchType(str, Enum):
    SEMANTIC = "SEMANTIC"
    KEYWORD = "KEYWORD"
    HYBRID = "HYBRID"
    GRAPH = "GRAPH"

class SearchScope(str, Enum):
    ALL = "ALL"
    DEPARTMENT = "DEPARTMENT"
    SOURCE = "SOURCE"
    FILE_TYPE = "FILE_TYPE"

# Entity information from knowledge graph
class EntityInfo(BaseModel):
    id: str
    name: str
    type: str
    properties: Dict[str, str] = {}
    relevance_score: float = 0.0

# Relationship information from knowledge graph
class RelationshipInfo(BaseModel):
    id: str
    type: str
    source_entity_id: str
    target_entity_id: str
    source_entity_name: str = ""
    target_entity_name: str = ""
    properties: Dict[str, str] = {}
    confidence_score: float = 0.0
    relevance_score: float = 0.0
    context: str = ""

# Graph context containing all knowledge graph discoveries
class GraphContext(BaseModel):
    all_entities: List[EntityInfo] = []
    all_relationships: List[RelationshipInfo] = []

# Search result item
class SearchResultItem(BaseModel):
    file_id: str
    file_name: str
    mime_type: str
    web_view_link: str
    created_time: str
    modified_time: str
    score: float
    vector_id: str
    chunk_text: str
    search_type: str = ""
    source_type: str = ""
    source_id: str = ""
    metadata: Dict[str, str] = {}

# Search Similar Documents models
class SearchSimilarDocumentsRequest(BaseModel):
    user_id: str
    query_text: str
    top_k: int = 5
    agent_id: Optional[str] = None
    organisation_id: str
    file_ids: Optional[List[str]] = None
    least_score: Optional[float] = None
    search_type: SearchType = SearchType.SEMANTIC
    search_scope: SearchScope = SearchScope.ALL
    source_ids: Optional[List[str]] = None
    file_types: Optional[List[str]] = None

class SearchSimilarDocumentsResponse(BaseModel):
    success: bool
    message: str
    results: List[SearchResultItem] = []
    graph_context: Optional[GraphContext] = None
    total_results: int = 0
    search_time_ms: float = 0.0
    search_id: str = ""

# Batch Search Similar Documents models
class BatchSearchSimilarDocumentsRequest(BaseModel):
    user_id: str
    query_texts: List[str]
    top_k: int = 5
    agent_id: Optional[str] = None
    organisation_id: str
    file_ids: Optional[List[str]] = None
    least_score: Optional[float] = None
    search_type: SearchType = SearchType.SEMANTIC
    search_scope: SearchScope = SearchScope.ALL
    source_ids: Optional[List[str]] = None
    file_types: Optional[List[str]] = None

class QueryResults(BaseModel):
    query_text: str
    results: List[SearchResultItem] = []
    graph_context: Optional[GraphContext] = None
    search_time_ms: float = 0.0

class BatchSearchSimilarDocumentsResponse(BaseModel):
    success: bool
    message: str
    query_results: List[QueryResults] = []
    total_search_time_ms: float = 0.0
    batch_search_id: str = ""

# Advanced search filters
class SearchFilters(BaseModel):
    file_types: Optional[List[str]] = None
    source_ids: Optional[List[str]] = None
    department_ids: Optional[List[str]] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    min_file_size: Optional[int] = None
    max_file_size: Optional[int] = None
    tags: Optional[List[str]] = None
    custom_filters: Optional[Dict[str, str]] = None

# Advanced search models
class AdvancedSearchRequest(BaseModel):
    user_id: str
    query_text: str
    organisation_id: str
    search_type: SearchType = SearchType.SEMANTIC
    search_scope: SearchScope = SearchScope.ALL
    filters: Optional[SearchFilters] = None
    top_k: int = 5
    least_score: Optional[float] = None
    agent_id: Optional[str] = None
    include_graph_context: bool = True

class AdvancedSearchResponse(BaseModel):
    success: bool
    message: str
    results: List[SearchResultItem] = []
    graph_context: Optional[GraphContext] = None
    total_results: int = 0
    search_time_ms: float = 0.0
    search_id: str = ""
    facets: Dict[str, int] = {}

# Search suggestions models
class SearchSuggestionsRequest(BaseModel):
    user_id: str
    organisation_id: str
    partial_query: str
    max_suggestions: int = 10
    search_scope: SearchScope = SearchScope.ALL
    source_ids: Optional[List[str]] = None

class SearchSuggestion(BaseModel):
    suggestion_text: str
    suggestion_type: str
    relevance_score: float = 0.0
    metadata: Dict[str, str] = {}

class SearchSuggestionsResponse(BaseModel):
    success: bool
    message: str
    suggestions: List[SearchSuggestion] = []

# Search by source models
class SearchBySourceRequest(BaseModel):
    user_id: str
    organisation_id: str
    query_text: str
    source_id: str
    search_type: SearchType = SearchType.SEMANTIC
    top_k: int = 5
    least_score: Optional[float] = None
    agent_id: Optional[str] = None
    filters: Optional[SearchFilters] = None

class SearchBySourceResponse(BaseModel):
    success: bool
    message: str
    results: List[SearchResultItem] = []
    graph_context: Optional[GraphContext] = None
    total_results: int = 0
    search_time_ms: float = 0.0
    search_id: str = ""
    source_name: str = ""

# Source search result model
class SourceSearchResult(BaseModel):
    id: str
    name: str
    type: str
    results: List[SearchResultItem] = []

# Search analytics models
class SearchAnalytics(BaseModel):
    total_searches: int = 0
    successful_searches: int = 0
    failed_searches: int = 0
    average_response_time_ms: float = 0.0
    top_queries: List[str] = []
    top_file_types: List[str] = []
    top_sources: List[str] = []
    search_type_distribution: Dict[str, int] = {}
    daily_search_counts: Dict[str, int] = {}

class SearchAnalyticsRequest(BaseModel):
    user_id: str
    organisation_id: str
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    source_ids: Optional[List[str]] = None
    department_ids: Optional[List[str]] = None

class SearchAnalyticsResponse(BaseModel):
    success: bool
    message: str
    analytics: Optional[SearchAnalytics] = None