# In your router file or a separate schemas.py
from pydantic import BaseModel, HttpUrl, Field
from typing import List, Optional, Dict, Any


class RepositoryOwnerInfo(BaseModel):
    login: str
    id: int
    avatar_url: Optional[HttpUrl] = None
    html_url: HttpUrl


class RepositoryBasicInfo(BaseModel):
    id: int
    name: str
    full_name: str
    private: bool
    html_url: HttpUrl
    description: Optional[str] = None
    default_branch: str
    owner: RepositoryOwnerInfo  # Use the nested model
    language: Optional[str] = None
    stargazers_count: int
    forks_count: int
    updated_at: str  # Or datetime, if you parse it

    # Helper to create from GitHub's full dict
    @classmethod
    def from_github_dict(cls, github_repo_dict: Dict[str, Any]) -> "RepositoryBasicInfo":
        return cls(
            id=github_repo_dict["id"],
            name=github_repo_dict["name"],
            full_name=github_repo_dict["full_name"],
            private=github_repo_dict["private"],
            html_url=github_repo_dict["html_url"],
            description=github_repo_dict.get("description"),
            default_branch=github_repo_dict["default_branch"],
            owner=RepositoryOwnerInfo(
                login=github_repo_dict["owner"]["login"],
                id=github_repo_dict["owner"]["id"],
                avatar_url=github_repo_dict["owner"].get("avatar_url"),
                html_url=github_repo_dict["owner"]["html_url"],
            ),
            language=github_repo_dict.get("language"),
            stargazers_count=github_repo_dict.get("stargazers_count", 0),
            forks_count=github_repo_dict.get("forks_count", 0),
            updated_at=github_repo_dict["updated_at"],
        )


class PaginationMetadata(BaseModel):
    total_items: int
    total_pages: int
    current_page: int
    page_size: int
    has_next_page: bool
    has_previous_page: bool


class PaginatedRepositoriesResponse(BaseModel):
    success: bool = True
    message: str
    repositories: List[RepositoryBasicInfo]
    metadata: PaginationMetadata


class BranchInfo(BaseModel):
    name: str
    # commit_sha: str # Example: commit['sha']
    # protected: bool # If available

    @classmethod
    def from_github_dict(cls, github_branch_dict: Dict[str, Any]) -> "BranchInfo":
        return cls(
            name=github_branch_dict["name"]
            # commit_sha=github_branch_dict["commit"]["sha"],
            # protected=github_branch_dict.get("protected", False)
        )


class RepositoryBranchesResponse(BaseModel):
    success: bool = True
    message: str
    repository_full_name: str
    owner_login: str
    branches: List[BranchInfo]
