from typing import Dict, List, Optional

import grpc

from app.core.config import settings
from app.grpc_ import connector_pb2, connector_pb2_grpc


class ConnectorServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )
        self.stub = connector_pb2_grpc.ConnectorServiceStub(self.channel)

    async def add_source(
        self,
        organisation_id: str,
        source_type: str,
        name: str,
        key: str,
        file_ids: Optional[List[str]] = None,
        jira_url: Optional[str] = None,
        jira_email: Optional[str] = None,
        config: Optional[Dict[str, str]] = None,
    ):
        """
        Add a new source with credentials.

        Args:
            organisation_id: ID of the organisation
            source_type: Type of source (GOOGLE_DRIVE, SLACK, JIRA)
            name: Name of the source
            key: Service Account JSON or API key (required)
            file_ids: Optional list of specific file IDs to sync (for Google Drive)
            jira_url: Optional Jira URL (for Jira sources)
            jira_email: Optional Jira email (for Jira sources)
            config: Additional connector-specific configuration

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        print(f"Adding sourcex: {name} of type {source_type} for organisation {organisation_id}")
        # Convert source_type to proto enum
        # source_type comes as SourceType enum, we need its value for proto conversion
        source_type_str = source_type.value if hasattr(source_type, "value") else str(source_type)
        source_type_enum = connector_pb2.SourceType.Value(source_type_str)
        print(f"Source type string: {source_type_str}")
        print(f"Source type enum: {source_type_enum}")
        request = connector_pb2.AddSourceRequest(
            organisation_id=organisation_id,
            type=source_type_enum,
            name=name,
            key=key,
            file_ids=file_ids if file_ids else [],
            jira_url=jira_url if jira_url else "",
            jira_email=jira_email if jira_email else "",
            config=config if config else {},
        )

        try:
            response = self.stub.addSource(request)
            return response
        except grpc.RpcError as e:
            print(f"gRPC error while adding source: {e}")
            raise self._handle_error(e)

    async def list_sources(
        self,
        organisation_id: str,
        source_type: Optional[str] = None,
        connector_type: Optional[str] = None,
    ):
        """
        List sources for an organisation.

        Args:
            organisation_id: ID of the organisation
            source_type: Optional filter by source type
            connector_type: Optional filter by connector type

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.ListSourcesRequest(organisation_id=organisation_id)

        # Set optional filters
        if source_type:
            source_type_enum = getattr(connector_pb2.SourceType, source_type, None)
            if source_type_enum:
                request.type = source_type_enum

        if connector_type:
            connector_type_enum = getattr(connector_pb2.ConnectorType, connector_type, None)
            if connector_type_enum:
                request.connector_type = connector_type_enum

        try:
            response = self.stub.listSources(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_source(self, source_id: str, user_id: str, organisation_id: str):
        """
        Delete a source.

        Args:
            source_id: ID of the source to delete
            user_id: Admin user ID
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.DeleteSourceRequest(
            source_id=source_id, user_id=user_id, organisation_id=organisation_id
        )

        try:
            response = self.stub.deleteSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_source_credentials(
        self,
        source_id: str,
        user_id: str,
        key: str,
        jira_url: Optional[str] = None,
        jira_email: Optional[str] = None,
        config: Optional[Dict[str, str]] = None,
    ):
        """
        Update source credentials.

        Args:
            source_id: ID of the source
            user_id: ID of the user updating credentials
            key: Service Account JSON or API key (required)
            jira_url: Optional Jira URL (for Jira sources)
            jira_email: Optional Jira email (for Jira sources)
            config: Additional connector-specific configuration

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.UpdateSourceCredentialsRequest(
            source_id=source_id,
            user_id=user_id,
            key=key,
            jira_url=jira_url if jira_url else "",
            jira_email=jira_email if jira_email else "",
            config=config if config else {},
        )

        try:
            response = self.stub.updateSourceCredentials(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def validate_source(self, source_id: str, organisation_id: str):
        """
        Validate source and get accessible folders.

        Args:
            source_id: ID of the source to validate
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.ValidateSourceRequest(
            source_id=source_id, organisation_id=organisation_id
        )

        try:
            response = self.stub.validateSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_connector_info(self, source_type: str):
        """
        Get connector information and capabilities.

        Args:
            source_type: Type of source (GOOGLE_DRIVE, SLACK, JIRA)

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        source_type_enum = getattr(
            connector_pb2.SourceType, source_type, connector_pb2.SourceType.GOOGLE_DRIVE
        )

        request = connector_pb2.GetConnectorInfoRequest(type=source_type_enum)

        try:
            response = self.stub.getConnectorInfo(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_connector_types(self, connector_type: Optional[str] = None):
        """
        List available connector types.

        Args:
            connector_type: Optional filter by connector type

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.ListConnectorTypesRequest()

        if connector_type:
            connector_type_enum = getattr(connector_pb2.ConnectorType, connector_type, None)
            if connector_type_enum:
                request.connector_type = connector_type_enum

        try:
            response = self.stub.listConnectorTypes(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def test_connection(self, source_id: str, organisation_id: str):
        """
        Test connector connection.

        Args:
            source_id: ID of the source
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.TestConnectionRequest(
            source_id=source_id, organisation_id=organisation_id
        )

        try:
            response = self.stub.testConnection(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def sync_source(
        self,
        source_id: str,
        organisation_id: str,
        user_id: str,
        full_sync: bool = False,
        specific_items: Optional[List[str]] = None,
    ):
        """
        Sync data from a source.

        Args:
            source_id: ID of the source
            organisation_id: ID of the organisation
            user_id: ID of the user who initiated the sync
            full_sync: Whether to perform full sync or incremental
            specific_items: Optional specific files/folders to sync

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.SyncSourceRequest(
            source_id=source_id,
            organisation_id=organisation_id,
            user_id=user_id,
            full_sync=full_sync,
            specific_items=specific_items if specific_items else [],
        )

        try:
            response = self.stub.syncSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_sync_status(
        self, source_id: str, organisation_id: str, sync_id: Optional[str] = None
    ):
        """
        Get sync status for a source.

        Args:
            source_id: ID of the source
            organisation_id: ID of the organisation
            sync_id: Optional specific sync operation ID

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.GetSyncStatusRequest(
            source_id=source_id, organisation_id=organisation_id, sync_id=sync_id if sync_id else ""
        )

        try:
            response = self.stub.getSyncStatus(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    # Google Drive specific operations

    async def disconnect_drive(self, organisation_id: str):
        """
        Disconnect Google Drive account.

        Args:
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.DisconnectDriveRequest(organisation_id=organisation_id)

        try:
            response = self.stub.disconnectDrive(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_files(
        self,
        user_id: str,
        organisation_id: str,
        folder_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
    ):
        """
        List Google Drive files and folders.

        Args:
            user_id: ID of the user
            organisation_id: Organisation ID for source context
            folder_id: Optional ID of the folder to list files from
            page: Page number for pagination
            page_size: Number of items per page

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.ListFilesRequest(
            user_id=user_id,
            folder_id=folder_id if folder_id else "",
            page=page,
            page_size=page_size,
            organisation_id=organisation_id,
        )

        try:
            response = self.stub.listFiles(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_file_details(self, user_id: str, file_id: str, organisation_id: str):
        """
        Get file details.

        Args:
            user_id: ID of the user
            file_id: ID of the file
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.GetFileDetailsRequest(
            user_id=user_id, file_id=file_id, organisation_id=organisation_id
        )

        try:
            response = self.stub.getFileDetails(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_folder_by_id(self, organisation_id: str, folder_id: str):
        """
        Get folder by ID and its contents.

        Args:
            organisation_id: ID of the organisation
            folder_id: ID of the folder

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.GetFolderByIdRequest(
            organisation_id=organisation_id, folder_id=folder_id
        )

        try:
            response = self.stub.getFolderById(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def sync_folder_by_ids(self, organisation_id: str, folder_ids: List[str]):
        """
        Sync folders by IDs.

        Args:
            organisation_id: ID of the organisation
            folder_ids: List of folder IDs to sync

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        # import pdb

        # pdb.set_trace()
        request = connector_pb2.SyncFolderByIdsRequest(
            organisation_id=organisation_id, folder_ids=folder_ids
        )

        try:
            response = self.stub.syncFolderByIds(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def check_file_access(self, user_id: str, file_id: str, organisation_id: str):
        """
        Check if a user has access to a file.

        Args:
            user_id: ID of the user
            file_id: ID of the file
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.CheckFileAccessRequest(
            user_id=user_id, file_id=file_id, organisation_id=organisation_id
        )

        try:
            response = self.stub.checkFileAccess(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def sync_file_by_url(
        self,
        drive_urls: List[str],
        agent_id: str,
        organisation_id: str,
        user_id: Optional[str] = None,
    ):
        """
        Sync specific Google Drive files by URLs.

        Args:
            drive_urls: List of Google Drive URLs
            agent_id: ID of the agent
            organisation_id: ID of the organisation
            user_id: Optional ID of the user

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.SyncFileByUrlRequest(
            drive_url=drive_urls,  # Now accepts repeated string (list)
            agent_id=agent_id,
            user_id=user_id if user_id else "",
            organisation_id=organisation_id,
        )
        try:
            response = self.stub.syncFileByUrl(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_top_level_folders(self, organisation_id: str):
        """
        List top-level folders using service account.

        Args:
            organisation_id: ID of the organisation

        Returns:
            The gRPC response from the Connector service

        Raises:
            GrpcError: If the gRPC call fails
        """
        request = connector_pb2.ListTopLevelFoldersRequest(organisation_id=organisation_id)

        try:
            response = self.stub.listTopLevelFolders(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    def _handle_error(self, e: grpc.RpcError):
        """
        Handle gRPC errors and convert them to appropriate HTTP exceptions.

        Args:
            e: The gRPC error

        Returns:
            HTTPException with appropriate status code and detail
        """
        status_code = e.code()
        details = e.details()

        from fastapi import HTTPException

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")
