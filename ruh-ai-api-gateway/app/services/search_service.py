import grpc
from typing import List, Optional, Dict
from app.core.config import settings
from app.grpc_ import search_pb2, search_pb2_grpc


class SearchServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )
        self.stub = search_pb2_grpc.SearchServiceStub(self.channel)
    
    async def search_similar_documents(
        self, 
        user_id: str, 
        query_text: str, 
        top_k: int = 5, 
        agent_id: Optional[str] = None, 
        organisation_id: str = None, 
        file_ids: Optional[List[str]] = None, 
        least_score: Optional[float] = None,
        search_type: str = "SEMANTIC",
        search_scope: str = "ALL",
        source_ids: Optional[List[str]] = None,
        file_types: Optional[List[str]] = None
    ):
        """
        Search for documents semantically similar to a query.
        
        Args:
            user_id: ID of the user
            query_text: The query text to search for
            top_k: Number of results to return
            agent_id: Optional agent ID to filter results by department access
            organisation_id: Organization ID
            file_ids: Optional list of specific file IDs to search within
            least_score: Optional minimum score threshold for results
            search_type: Type of search to perform
            search_scope: Scope of search
            source_ids: Optional list of source IDs to search within
            file_types: Optional list of file types to filter by
            
        Returns:
            The gRPC response from the Search service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Convert search_type string to enum
        search_type_enum = getattr(search_pb2.SearchType, search_type, search_pb2.SearchType.SEMANTIC)
        search_scope_enum = getattr(search_pb2.SearchScope, search_scope, search_pb2.SearchScope.ALL)
        
        request = search_pb2.SearchSimilarDocumentsRequest(
            user_id=user_id,
            query_text=query_text,
            top_k=top_k,
            agent_id=agent_id if agent_id else "",
            organisation_id=organisation_id,
            file_ids=file_ids if file_ids else [],
            least_score=least_score if least_score is not None else 0.0,
            search_type=search_type_enum,
            search_scope=search_scope_enum,
            source_ids=source_ids if source_ids else [],
            file_types=file_types if file_types else []
        )
        
        try:
            response = self.stub.searchSimilarDocuments(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def batch_search_similar_documents(
        self, 
        user_id: str, 
        query_texts: List[str], 
        top_k: int = 5, 
        agent_id: Optional[str] = None, 
        organisation_id: str = None, 
        file_ids: Optional[List[str]] = None, 
        least_score: Optional[float] = None,
        search_type: str = "SEMANTIC",
        search_scope: str = "ALL",
        source_ids: Optional[List[str]] = None,
        file_types: Optional[List[str]] = None
    ):
        """
        Batch search for documents semantically similar to multiple queries.
        
        Args:
            user_id: ID of the user
            query_texts: List of query texts to search for
            top_k: Number of results to return per query
            agent_id: Optional agent ID to filter results by department access
            organisation_id: Organization ID
            file_ids: Optional list of specific file IDs to search within
            least_score: Optional minimum score threshold for results
            search_type: Type of search to perform
            search_scope: Scope of search
            source_ids: Optional list of source IDs to search within
            file_types: Optional list of file types to filter by
            
        Returns:
            The gRPC response from the Search service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Convert search_type string to enum
        search_type_enum = getattr(search_pb2.SearchType, search_type, search_pb2.SearchType.SEMANTIC)
        search_scope_enum = getattr(search_pb2.SearchScope, search_scope, search_pb2.SearchScope.ALL)
        
        request = search_pb2.BatchSearchSimilarDocumentsRequest(
            user_id=user_id,
            query_texts=query_texts,
            top_k=top_k,
            agent_id=agent_id if agent_id else "",
            organisation_id=organisation_id,
            file_ids=file_ids if file_ids else [],
            least_score=least_score if least_score is not None else 0.0,
            search_type=search_type_enum,
            search_scope=search_scope_enum,
            source_ids=source_ids if source_ids else [],
            file_types=file_types if file_types else []
        )
        
        try:
            response = self.stub.batchSearchSimilarDocuments(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def advanced_search(
        self,
        user_id: str,
        query_text: str,
        organisation_id: str,
        search_type: str = "SEMANTIC",
        search_scope: str = "ALL",
        filters: Optional[Dict] = None,
        top_k: int = 5,
        least_score: Optional[float] = None,
        agent_id: Optional[str] = None,
        include_graph_context: bool = True
    ):
        """
        Perform advanced search with filters and options.
        
        Args:
            user_id: ID of the user
            query_text: The query text to search for
            organisation_id: Organization ID
            search_type: Type of search to perform
            search_scope: Scope of search
            filters: Search filters
            top_k: Number of results to return
            least_score: Optional minimum score threshold for results
            agent_id: Optional agent ID
            include_graph_context: Whether to include graph context
            
        Returns:
            The gRPC response from the Search service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Convert search_type string to enum
        search_type_enum = getattr(search_pb2.SearchType, search_type, search_pb2.SearchType.SEMANTIC)
        search_scope_enum = getattr(search_pb2.SearchScope, search_scope, search_pb2.SearchScope.ALL)
        
        # Build filters if provided
        search_filters = None
        if filters:
            search_filters = search_pb2.SearchFilters(
                file_types=filters.get('file_types', []),
                source_ids=filters.get('source_ids', []),
                department_ids=filters.get('department_ids', []),
                date_from=filters.get('date_from', ''),
                date_to=filters.get('date_to', ''),
                min_file_size=filters.get('min_file_size', 0),
                max_file_size=filters.get('max_file_size', 0),
                tags=filters.get('tags', []),
                custom_filters=filters.get('custom_filters', {})
            )
        
        request = search_pb2.AdvancedSearchRequest(
            user_id=user_id,
            query_text=query_text,
            organisation_id=organisation_id,
            search_type=search_type_enum,
            search_scope=search_scope_enum,
            filters=search_filters,
            top_k=top_k,
            least_score=least_score if least_score is not None else 0.0,
            agent_id=agent_id if agent_id else "",
            include_graph_context=include_graph_context
        )
        
        try:
            response = self.stub.advancedSearch(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def get_search_suggestions(
        self,
        user_id: str,
        organisation_id: str,
        partial_query: str,
        max_suggestions: int = 10,
        search_scope: str = "ALL",
        source_ids: Optional[List[str]] = None
    ):
        """
        Get search suggestions/autocomplete.
        
        Args:
            user_id: ID of the user
            organisation_id: Organization ID
            partial_query: Partial query text for autocomplete
            max_suggestions: Maximum number of suggestions to return
            search_scope: Scope of search
            source_ids: Optional source filtering
            
        Returns:
            The gRPC response from the Search service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        search_scope_enum = getattr(search_pb2.SearchScope, search_scope, search_pb2.SearchScope.ALL)
        
        request = search_pb2.SearchSuggestionsRequest(
            user_id=user_id,
            organisation_id=organisation_id,
            partial_query=partial_query,
            max_suggestions=max_suggestions,
            search_scope=search_scope_enum,
            source_ids=source_ids if source_ids else []
        )
        
        try:
            response = self.stub.getSearchSuggestions(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def search_by_source(
        self,
        user_id: str,
        organisation_id: str,
        query_text: str,
        source_id: str,
        search_type: str = "SEMANTIC",
        top_k: int = 5,
        least_score: Optional[float] = None,
        agent_id: Optional[str] = None,
        filters: Optional[Dict] = None
    ):
        """
        Search within specific sources.
        
        Args:
            user_id: ID of the user
            organisation_id: Organization ID
            query_text: The query text to search for
            source_id: Specific source to search within
            search_type: Type of search to perform
            top_k: Number of results to return
            least_score: Optional minimum score threshold for results
            agent_id: Optional agent ID
            filters: Search filters
            
        Returns:
            The gRPC response from the Search service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        search_type_enum = getattr(search_pb2.SearchType, search_type, search_pb2.SearchType.SEMANTIC)
        
        # Build filters if provided
        search_filters = None
        if filters:
            search_filters = search_pb2.SearchFilters(
                file_types=filters.get('file_types', []),
                source_ids=filters.get('source_ids', []),
                department_ids=filters.get('department_ids', []),
                date_from=filters.get('date_from', ''),
                date_to=filters.get('date_to', ''),
                min_file_size=filters.get('min_file_size', 0),
                max_file_size=filters.get('max_file_size', 0),
                tags=filters.get('tags', []),
                custom_filters=filters.get('custom_filters', {})
            )
        
        request = search_pb2.SearchBySourceRequest(
            user_id=user_id,
            organisation_id=organisation_id,
            query_text=query_text,
            source_id=source_id,
            search_type=search_type_enum,
            top_k=top_k,
            least_score=least_score if least_score is not None else 0.0,
            agent_id=agent_id if agent_id else "",
            filters=search_filters
        )
        
        try:
            response = self.stub.searchBySource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    async def get_search_analytics(
        self,
        user_id: str,
        organisation_id: str,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        source_ids: Optional[List[str]] = None,
        department_ids: Optional[List[str]] = None
    ):
        """
        Get search analytics and insights.
        
        Args:
            user_id: ID of the user
            organisation_id: Organization ID
            date_from: Analytics date range start (ISO format)
            date_to: Analytics date range end (ISO format)
            source_ids: Optional source filtering
            department_ids: Optional department filtering
            
        Returns:
            The gRPC response from the Search service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = search_pb2.SearchAnalyticsRequest(
            user_id=user_id,
            organisation_id=organisation_id,
            date_from=date_from if date_from else "",
            date_to=date_to if date_to else "",
            source_ids=source_ids if source_ids else [],
            department_ids=department_ids if department_ids else []
        )
        
        try:
            response = self.stub.getSearchAnalytics(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    
    def _handle_error(self, e: grpc.RpcError):
        """
        Handle gRPC errors and convert them to appropriate HTTP exceptions.
        
        Args:
            e: The gRPC error
            
        Returns:
            HTTPException with appropriate status code and detail
        """
        status_code = e.code()
        details = e.details()
        
        from fastapi import HTTPException
        
        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")