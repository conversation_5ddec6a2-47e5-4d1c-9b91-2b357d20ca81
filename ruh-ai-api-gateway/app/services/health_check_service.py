import grpc
import structlog
from typing import Dict, Any
import time

from app.core.config import settings
from app.grpc_ import (
    user_pb2_grpc,
    admin_pb2_grpc,
    notification_pb2_grpc,
    communication_pb2_grpc,
    workflow_pb2_grpc,
    agent_pb2_grpc,
    mcp_pb2_grpc,
    authentication_pb2_grpc,
)

logger = structlog.get_logger()


class HealthService:
    """Service for checking the health of microservices."""

    @staticmethod
    def check_service(
        service_name: str, host_setting: str, port_setting: int, stub_class
    ) -> Dict[str, Any]:
        """
        Generic method to check the health of a service.

        Args:
            service_name: Name of the service
            host_setting: Host address of the service
            port_setting: Port number of the service
            stub_class: gRPC stub class for the service

        Returns:
            Dict[str, Any]: Dictionary with status and connection details
        """
        start_time = time.time()
        status = "healthy"
        error_message = None

        # Check if settings are available
        if not host_setting or not port_setting:
            status = "unknown"
            error_message = "Service configuration not available"
            logger.warning(f"{service_name} health check skipped: configuration not available")
            response_time = 0
        else:
            try:
                # Create a gRPC channel to the service
                channel = grpc.insecure_channel(f"{host_setting}:{port_setting}")

                # Set a deadline for the connection attempt (2 seconds)
                grpc.channel_ready_future(channel).result(timeout=2)

                # Try to create a stub (this doesn't make an actual RPC call)
                stub = stub_class(channel)

                # Close the channel
                channel.close()

                response_time = round((time.time() - start_time) * 1000, 2)  # in milliseconds

            except grpc.FutureTimeoutError:
                status = "unhealthy"
                error_message = "Connection timeout"
                logger.error(f"{service_name} health check failed: connection timeout")
                response_time = round((time.time() - start_time) * 1000, 2)

            except Exception as e:
                status = "unhealthy"
                error_message = str(e)
                logger.error(f"{service_name} health check failed: {str(e)}")
                response_time = round((time.time() - start_time) * 1000, 2)

        return {
            "service": service_name,
            "status": status,
            "host": host_setting,
            "port": port_setting,
            "response_time_ms": response_time,
            "error": error_message,
        }

    @staticmethod
    def check_user_service() -> Dict[str, Any]:
        """Check the health of the user service."""
        return HealthService.check_service(
            "user-service",
            getattr(settings, "USER_SERVICE_HOST"),
            getattr(settings, "USER_SERVICE_PORT"),
            user_pb2_grpc.UserServiceStub,
        )

    @staticmethod
    def check_admin_service() -> Dict[str, Any]:
        """Check the health of the admin service."""
        return HealthService.check_service(
            "admin-service",
            getattr(settings, "ADMIN_SERVICE_HOST"),
            getattr(settings, "ADMIN_SERVICE_PORT"),
            admin_pb2_grpc.AdminServiceStub,
        )

    @staticmethod
    def check_notification_service() -> Dict[str, Any]:
        """Check the health of the notification service."""
        return HealthService.check_service(
            "notification-service",
            getattr(settings, "NOTIFICATION_SERVICE_HOST"),
            getattr(settings, "NOTIFICATION_SERVICE_PORT"),
            notification_pb2_grpc.NotificationServiceStub,
        )

    @staticmethod
    def check_communication_service() -> Dict[str, Any]:
        """Check the health of the communication service."""
        return HealthService.check_service(
            "communication-service",
            getattr(settings, "COMMUNICATION_SERVICE_HOST"),
            getattr(settings, "COMMUNICATION_SERVICE_PORT"),
            communication_pb2_grpc.CommunicationServiceStub,
        )

    @staticmethod
    def check_workflow_service() -> Dict[str, Any]:
        """Check the health of the workflow service."""
        return HealthService.check_service(
            "workflow-service",
            getattr(settings, "WORKFLOW_SERVICE_HOST"),
            getattr(settings, "WORKFLOW_SERVICE_PORT"),
            workflow_pb2_grpc.WorkflowServiceStub,
        )

    @staticmethod
    def check_agent_service() -> Dict[str, Any]:
        """Check the health of the agent service."""
        return HealthService.check_service(
            "agent-service",
            getattr(settings, "AGENT_SERVICE_HOST"),
            getattr(settings, "AGENT_SERVICE_PORT"),
            agent_pb2_grpc.AgentServiceStub,
        )

    @staticmethod
    def check_mcp_service() -> Dict[str, Any]:
        """Check the health of the MCP service."""
        return HealthService.check_service(
            "mcp-service",
            getattr(settings, "MCP_SERVICE_HOST"),
            getattr(settings, "MCP_SERVICE_PORT"),
            mcp_pb2_grpc.MCPServiceStub,
        )

    @staticmethod
    def check_auth_service() -> Dict[str, Any]:
        """Check the health of the MCP service."""
        return HealthService.check_service(
            "auth-service",
            getattr(settings, "AUTH_SERVICE_HOST"),
            getattr(settings, "AUTH_SERVICE_PORT"),
            authentication_pb2_grpc.AuthenticationServiceStub,
        )

    @staticmethod
    def check_all_services() -> Dict[str, Any]:
        """
        Check the health of all microservices.

        Returns:
            Dict[str, Any]: Dictionary with overall status and individual service statuses
        """
        # Define all service check methods
        service_checks = [
            HealthService.check_user_service,
            HealthService.check_admin_service,
            HealthService.check_notification_service,
            HealthService.check_communication_service,
            HealthService.check_workflow_service,
            HealthService.check_agent_service,
            HealthService.check_mcp_service,
            HealthService.check_auth_service,
        ]

        # Run all checks
        services = [check() for check in service_checks]

        # Determine overall status
        overall_status = "healthy"
        for service in services:
            if service["status"] == "unhealthy":
                overall_status = "unhealthy"
                break

        return {
            "status": overall_status,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()),
            "services": services,
        }
