"""
<PERSON><PERSON><PERSON> to test the PostgreSQL database connection.
"""
import sys
import os
from pathlib import Path
import logging

# Add the parent directory to sys.path
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from app.core.config import settings
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
    
    # Test PostgreSQL connection
    logger.info("Testing PostgreSQL connection...")
    
    # Check if PostgreSQL settings are configured
    if not settings.DB_HOST or not settings.DB_PORT or not settings.DB_USER or not settings.DB_NAME:
        logger.error("PostgreSQL settings are not configured. Please set DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, and DB_NAME environment variables.")
        sys.exit(1)
    
    # Convert PostgresDsn to string
    db_uri = str(settings.SQLALCHEMY_DATABASE_URI)
    logger.info(f"Connecting to PostgreSQL: {db_uri}")
    
    # Create engine and connect
    engine = create_engine(db_uri)
    
    # Test connection
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        if result.scalar() == 1:
            logger.info("Successfully connected to PostgreSQL!")
        else:
            logger.error("Connection test failed")
            sys.exit(1)
    
    # Get table information
    with engine.connect() as conn:
        result = conn.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"))
        tables = [row[0] for row in result]
        logger.info(f"Tables in database: {tables}")
        
        if 'oauth_credentials' in tables:
            logger.info("oauth_credentials table exists")
            # Get column information
            result = conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'oauth_credentials'
            """))
            columns = [(row[0], row[1]) for row in result]
            logger.info("Columns in oauth_credentials table:")
            for col_name, col_type in columns:
                logger.info(f"  - {col_name} ({col_type})")
        else:
            logger.warning("oauth_credentials table does not exist")
    
    logger.info("PostgreSQL test completed successfully")
    
except Exception as e:
    logger.error(f"Error testing PostgreSQL connection: {e}")
    sys.exit(1)
