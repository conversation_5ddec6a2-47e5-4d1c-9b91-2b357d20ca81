"""
Authentication Service Integration Tests

This module tests the integration between the API gateway and
the authentication service via gRPC.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

from app.main import app
from app.core.oauth_providers import OAuthProvider


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_auth_service_client():
    """Mock authentication service client."""
    with patch("app.services.authentication_service.auth_service_client") as mock:
        # Mock successful provider list response
        mock.list_oauth_providers = AsyncMock(
            return_value={
                "success": True,
                "message": "Providers retrieved successfully",
                "providers": [
                    {
                        "provider": "google",
                        "display_name": "Google OAuth",
                        "supported_tools": ["google_calendar", "google_drive", "gmail"],
                        "is_configured": True,
                    },
                    {
                        "provider": "microsoft",
                        "display_name": "Microsoft OAuth",
                        "supported_tools": ["microsoft_graph"],
                        "is_configured": True,
                    },
                ],
            }
        )

        # Mock successful tool scopes response
        mock.get_tool_scopes = AsyncMock(
            return_value={
                "success": True,
                "message": "Tool scopes retrieved successfully",
                "tool_name": "google_calendar",
                "provider": "google",
                "scopes": ["https://www.googleapis.com/auth/calendar"],
                "description": "Google Calendar access",
            }
        )

        # Mock successful OAuth initiation
        mock.initiate_oauth = AsyncMock(
            return_value={
                "success": True,
                "message": "OAuth authorization URL generated successfully",
                "authorization_url": "https://accounts.google.com/o/oauth2/auth?client_id=test&...",
                "state": "test_state_token",
            }
        )

        # Mock successful callback handling
        mock.handle_oauth_callback = AsyncMock(
            return_value={
                "success": True,
                "message": "OAuth authorization completed successfully",
                "user_id": "test_user",
                "mcp_id": "test_mcp",
                "tool_name": "google_calendar",
                "provider": "google",
            }
        )

        # Mock successful credential retrieval
        mock.get_oauth_credentials = AsyncMock(
            return_value={
                "success": True,
                "message": "OAuth credentials retrieved successfully",
                "user_id": "test_user",
                "mcp_id": "test_mcp",
                "tool_name": "google_calendar",
                "provider": "google",
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token",
                "token_type": "Bearer",
                "expires_in": 3600,
                "scope": "https://www.googleapis.com/auth/calendar",
            }
        )

        # Mock server credential retrieval
        mock.get_server_oauth_credentials = AsyncMock(
            return_value={
                "success": True,
                "message": "OAuth credentials retrieved successfully",
                "user_id": "test_user",
                "mcp_id": "test_mcp",
                "tool_name": "google_calendar",
                "provider": "google",
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token",
                "token_type": "Bearer",
                "expires_in": 3600,
                "scope": "https://www.googleapis.com/auth/calendar",
            }
        )

        # Mock health check
        mock.health_check = AsyncMock(
            return_value={
                "healthy": True,
                "status": "healthy",
                "version": "1.0.0",
                "dependencies": {
                    "database": "healthy",
                    "redis": "healthy",
                    "secret_manager": "healthy",
                },
            }
        )

        yield mock


@pytest.fixture
def mock_user_auth():
    """Mock user authentication."""
    with patch("app.core.auth_guard.role_required") as mock:
        mock.return_value = lambda: {"user_id": "test_user", "role": "user"}
        yield mock


@pytest.fixture
def mock_server_auth():
    """Mock server authentication."""
    with patch("app.core.security.validate_server_auth_key") as mock:
        mock.return_value = "valid_server_key"
        yield mock


class TestAuthServiceIntegration:
    """Test authentication service integration."""

    def test_list_oauth_providers(self, client, mock_auth_service_client):
        """Test listing OAuth providers via authentication service."""
        response = client.get("/api/v1/v3/oauth/providers")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["providers"]) == 2
        assert data["providers"][0]["provider"] == "google"
        assert "google_calendar" in data["providers"][0]["supported_tools"]

        # Verify authentication service was called
        mock_auth_service_client.list_oauth_providers.assert_called_once()

    def test_get_tool_scopes(self, client, mock_auth_service_client):
        """Test getting tool scopes via authentication service."""
        response = client.get("/api/v1/v3/oauth/tools/google_calendar/scopes")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["tool_name"] == "google_calendar"
        assert "google" in data["provider_scopes"]
        assert "https://www.googleapis.com/auth/calendar" in data["provider_scopes"]["google"]

        # Verify authentication service was called
        mock_auth_service_client.get_tool_scopes.assert_called_once_with(
            "google_calendar", OAuthProvider.GOOGLE
        )

    def test_oauth_authorize(self, client, mock_auth_service_client, mock_user_auth):
        """Test OAuth authorization initiation via authentication service."""
        response = client.post(
            "/api/v1/v3/oauth/authorize",
            params={"provider": "google", "mcp_id": "test_mcp", "tool_name": "google_calendar"},
        )

        assert response.status_code == 302  # Redirect response
        assert "accounts.google.com" in response.headers["location"]

        # Verify authentication service was called
        mock_auth_service_client.initiate_oauth.assert_called_once_with(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider=OAuthProvider.GOOGLE,
            scopes=None,
        )

    def test_oauth_authorize_with_custom_scopes(
        self, client, mock_auth_service_client, mock_user_auth
    ):
        """Test OAuth authorization with custom scopes."""
        response = client.post(
            "/api/v1/v3/oauth/authorize",
            params={
                "provider": "google",
                "mcp_id": "test_mcp",
                "tool_name": "google_calendar",
                "scopes": "scope1,scope2,scope3",
            },
        )

        assert response.status_code == 302

        # Verify custom scopes were passed
        mock_auth_service_client.initiate_oauth.assert_called_once_with(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider=OAuthProvider.GOOGLE,
            scopes=["scope1", "scope2", "scope3"],
        )

    def test_oauth_callback_success(self, client, mock_auth_service_client):
        """Test successful OAuth callback handling."""
        response = client.get(
            "/api/v1/v3/oauth/callback",
            params={"code": "test_auth_code", "state": "test_state_token"},
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["provider"] == "google"
        assert data["tool_name"] == "google_calendar"

        # Verify authentication service was called
        mock_auth_service_client.handle_oauth_callback.assert_called_once_with(
            code="test_auth_code", state="test_state_token", error=None
        )

    def test_oauth_callback_error(self, client, mock_auth_service_client):
        """Test OAuth callback with error."""
        response = client.get(
            "/api/v1/v3/oauth/callback",
            params={"error": "access_denied", "state": "test_state_token"},
        )

        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "denied" in data["message"]

    def test_get_oauth_credentials(self, client, mock_auth_service_client, mock_user_auth):
        """Test getting OAuth credentials via authentication service."""
        response = client.get(
            "/api/v1/v3/oauth/credentials",
            params={"mcp_id": "test_mcp", "tool_name": "google_calendar", "provider": "google"},
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["access_token"] == "test_access_token"
        assert data["refresh_token"] == "test_refresh_token"
        assert data["token_type"] == "Bearer"

        # Verify authentication service was called
        mock_auth_service_client.get_oauth_credentials.assert_called_once_with(
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider=OAuthProvider.GOOGLE,
        )

    def test_get_server_oauth_credentials(self, client, mock_auth_service_client, mock_server_auth):
        """Test getting OAuth credentials with server authentication."""
        response = client.get(
            "/api/v1/v3/oauth/server/credentials",
            params={
                "user_id": "test_user",
                "mcp_id": "test_mcp",
                "tool_name": "google_calendar",
                "provider": "google",
            },
            headers={"X-Server-Auth-Key": "valid_server_key"},
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["user_id"] == "test_user"
        assert data["access_token"] == "test_access_token"

        # Verify authentication service was called
        mock_auth_service_client.get_server_oauth_credentials.assert_called_once_with(
            server_auth_key="valid_server_key",
            user_id="test_user",
            mcp_id="test_mcp",
            tool_name="google_calendar",
            provider=OAuthProvider.GOOGLE,
        )

    def test_auth_service_health(self, client, mock_auth_service_client):
        """Test authentication service health check."""
        response = client.get("/api/v1/v3/oauth/health")

        assert response.status_code == 200
        data = response.json()
        assert data["healthy"] is True
        assert data["status"] == "healthy"
        assert "database" in data["dependencies"]

        # Verify authentication service was called
        mock_auth_service_client.health_check.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
