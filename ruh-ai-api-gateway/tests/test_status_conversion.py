#!/usr/bin/env python3
"""
Test script to verify that status conversion works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_status_conversion():
    """Test that Application model can handle integer status values."""
    print("🧪 Testing Status Conversion...")
    
    try:
        from app.schemas.application import Application, ApplicationStatusEnum
        
        # Test with integer status (as returned by gRPC)
        app_data_with_int_status = {
            "id": "test_123",
            "user_id": "user_123",
            "name": "Test App",
            "description": "Test description",
            "workflow_ids": [],
            "agent_ids": [],
            "status": 1,  # Integer value from gRPC
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "api_keys": [],
            "is_deleted": False
        }
        
        # This should work now with the validator
        app = Application(**app_data_with_int_status)
        print(f"✅ Integer status 1 converted to: {app.status}")
        assert app.status == ApplicationStatusEnum.ACTIVE
        
        # Test with different integer values
        test_cases = [
            (0, ApplicationStatusEnum.UNSPECIFIED),
            (1, ApplicationStatusEnum.ACTIVE),
            (2, ApplicationStatusEnum.INACTIVE),
            (3, ApplicationStatusEnum.SUSPENDED),
            (999, ApplicationStatusEnum.UNSPECIFIED)  # Unknown value should default to unspecified
        ]
        
        for int_status, expected_enum in test_cases:
            app_data_with_int_status["status"] = int_status
            app = Application(**app_data_with_int_status)
            print(f"✅ Integer status {int_status} converted to: {app.status}")
            assert app.status == expected_enum
        
        # Test with string status (should work as before)
        app_data_with_string_status = app_data_with_int_status.copy()
        app_data_with_string_status["status"] = "active"
        app = Application(**app_data_with_string_status)
        print(f"✅ String status 'active' preserved as: {app.status}")
        assert app.status == ApplicationStatusEnum.ACTIVE
        
        print("✅ All status conversion tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Status conversion test failed: {e}")
        return False


def test_enum_values():
    """Test that enum values are correct."""
    print("\n🧪 Testing Enum Values...")
    
    try:
        from app.schemas.application import ApplicationStatusEnum
        
        expected_values = ["unspecified", "active", "inactive", "suspended"]
        actual_values = [e.value for e in ApplicationStatusEnum]
        
        for expected in expected_values:
            if expected not in actual_values:
                print(f"❌ Missing enum value: {expected}")
                return False
            print(f"✅ Found enum value: {expected}")
        
        print("✅ All enum values verified!")
        return True
        
    except Exception as e:
        print(f"❌ Enum test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Status Conversion Tests...\n")
    
    tests = [
        test_status_conversion,
        test_enum_values
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All status conversion tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
