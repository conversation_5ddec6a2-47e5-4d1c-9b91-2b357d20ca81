import uuid
from datetime import datetime
import grpc
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.api_key import APIKey
from app.models.user import User
from app.grpc import user_pb2, user_pb2_grpc
from app.utils.secret_manager.secret_manager import EncryptionManager
from app.utils.secret_manager.public_key_manager import HMACAPIKeyManager

class APIKeyService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.encryption_manager = EncryptionManager()

    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()
    def CreatePublicKey(
        self, request: user_pb2.CreatePublicKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreatePublicKeyResponse:
        """
        Create a public key using the user's encryption key from GSM as private key.
        Returns the whole data with id, name, description, is_active, user_id, created_at and the generated public key.
        """
        db = self.get_db()
        try:
            # Verify user exists
            user = db.query(User).filter(User.id == request.user_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.CreatePublicKeyResponse(
                    success=False,
                    message="User not found"
                )

            # Get user's encryption key from GSM
            try:
                encryption_key = self.encryption_manager.get_user_encryption_key(request.user_id)
            except ValueError as e:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"User encryption key not found: {str(e)}")
                return user_pb2.CreatePublicKeyResponse(
                    success=False,
                    message="User encryption key not found"
                )

            # Get the next counter value for this user
            max_counter = db.query(APIKey).filter(
                APIKey.user_id == request.user_id
            ).order_by(APIKey.counter.desc()).first()

            next_counter = (max_counter.counter + 1) if max_counter else 1

            # Create API key record in database with counter and organization_id
            api_key = APIKey(
                id=str(uuid.uuid4()),
                name=request.name,
                description=request.description if request.description else None,
                type=request.type,
                organization_id=request.organization_id,
                counter=next_counter,
                is_active=True,  # Default to active
                user_id=request.user_id,
                created_at=datetime.utcnow()
            )

            db.add(api_key)
            db.commit()
            db.refresh(api_key)

            # Generate public key using HMAC with user_id, counter, and type
            key_manager = HMACAPIKeyManager(encryption_key)
            public_key = key_manager.generate_public_key(request.user_id, api_key.counter, request.type)

            # Return the complete data including the generated public key
            api_key_info = user_pb2.PublicKeyInfo(
                id=api_key.id,
                name=api_key.name,
                description=api_key.description or "",
                is_active=api_key.is_active,
                user_id=api_key.user_id,
                created_at=api_key.created_at.isoformat(),
                public_key=public_key,  # Include the generated public key
                organization_id=api_key.organization_id,
                counter=api_key.counter,
                type=api_key.type
            )

            return user_pb2.CreatePublicKeyResponse(
                success=True,
                message="Public key created successfully",
                api_key=api_key_info
            )

        except Exception as e:
            db.rollback()
            print(f"Error creating public key: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to create public key: {str(e)}")
            return user_pb2.CreatePublicKeyResponse(
                success=False,
                message=f"Failed to create public key: {str(e)}"
            )
        finally:
            db.close()

    def ValidatePublicKey(
        self, request: user_pb2.ValidatePublicKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.ValidatePublicKeyResponse:
        """
        Validate the public key against the private key present in GSM and check existence in DB.
        Returns success, message, and is_validated = true/false.
        """
        db = self.get_db()
        try:
            # First, parse the public key to extract user_id and counter (without encryption key)
            temp_key_manager = HMACAPIKeyManager("temp")  # Temporary manager just for parsing
            parsed_key = temp_key_manager.parse_public_key(request.public_key)

            if not parsed_key:
                return user_pb2.ValidatePublicKeyResponse(
                    success=True,
                    message="Invalid public key format",
                    is_validated=False
                )

            # Verify user exists
            user = db.query(User).filter(User.id == parsed_key["user_id"]).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.ValidatePublicKeyResponse(
                    success=False,
                    message="User not found",
                    is_validated=False
                )

            # Get user's encryption key from GSM
            try:
                encryption_key = self.encryption_manager.get_user_encryption_key(parsed_key["user_id"])
            except ValueError as e:
                return user_pb2.ValidatePublicKeyResponse(
                    success=False,
                    message="User encryption key not found",
                    is_validated=False
                )

            # Create the actual key manager with the real encryption key
            key_manager = HMACAPIKeyManager(encryption_key)

            # Validate the public key using HMAC
            is_valid = key_manager.validate_public_key(request.public_key, encryption_key)

            if not is_valid:
                return user_pb2.ValidatePublicKeyResponse(
                    success=True,
                    message="Public key validation failed",
                    is_validated=False
                )

            # Check if the API key with the specific counter exists and is active in the database
            api_key_exists = db.query(APIKey).filter(
                APIKey.counter == parsed_key["counter"],
                APIKey.user_id == parsed_key["user_id"],
                APIKey.is_active == True
            ).first()

            if not api_key_exists:
                return user_pb2.ValidatePublicKeyResponse(
                    success=True,
                    message="API key not found or inactive",
                    is_validated=False
                )

            return user_pb2.ValidatePublicKeyResponse(
                success=True,
                message="Public key validated successfully",
                is_validated=True
            )

        except Exception as e:
            print(f"Error validating public key: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to validate public key: {str(e)}")
            return user_pb2.ValidatePublicKeyResponse(
                success=False,
                message=f"Failed to validate public key: {str(e)}",
                is_validated=False
            )
        finally:
            db.close()

    def ListPublicKeys(
        self, request: user_pb2.ListPublicKeysRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListPublicKeysResponse:
        """
        List all data present in DB without public_key.
        """
        db = self.get_db()
        try:
            # Build query with user_id filter
            query = db.query(APIKey).filter(APIKey.user_id == request.user_id)

            # Add organization_id filter if provided
            if request.organization_id:
                query = query.filter(APIKey.organization_id == request.organization_id)

            # Add type filter if provided
            if request.type:
                query = query.filter(APIKey.type == request.type)

            api_keys = query.all()

            api_key_list = [
                user_pb2.PublicKeyInfo(
                    id=key.id,
                    name=key.name,
                    description=key.description or "",
                    is_active=key.is_active,
                    user_id=key.user_id,
                    created_at=key.created_at.isoformat(),
                    public_key="",  # Don't include public key in list
                    organization_id=key.organization_id,
                    counter=key.counter,
                    type=key.type
                ) for key in api_keys
            ]

            return user_pb2.ListPublicKeysResponse(
                success=True,
                message="API keys retrieved successfully",
                api_keys=api_key_list
            )
        except Exception as e:
            print(f"Error listing public keys: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list API keys: {str(e)}")
            return user_pb2.ListPublicKeysResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def GetPublicKey(
        self, request: user_pb2.GetPublicKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetPublicKeyResponse:
        """
        Retrieve all data for specific key_id.
        """
        db = self.get_db()
        try:
            api_key = db.query(APIKey).filter(
                APIKey.id == request.key_id,
                APIKey.user_id == request.user_id
            ).first()

            if not api_key:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("API key not found")
                return user_pb2.GetPublicKeyResponse(
                    success=False,
                    message="API key not found"
                )

            api_key_info = user_pb2.PublicKeyInfo(
                id=api_key.id,
                name=api_key.name,
                description=api_key.description or "",
                is_active=api_key.is_active,
                user_id=api_key.user_id,
                created_at=api_key.created_at.isoformat(),
                public_key="",  # Don't include public key in get response
                organization_id=getattr(api_key, 'organization_id', '') or "",  # Handle missing field
                counter=getattr(api_key, 'counter', 0) or 0,  # Handle missing field
                type=getattr(api_key, 'type', '') or ""  # Handle missing field
            )

            return user_pb2.GetPublicKeyResponse(
                success=True,
                message="API key retrieved successfully",
                api_key=api_key_info
            )
        except Exception as e:
            print(f"Error retrieving public key: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve API key: {str(e)}")
            return user_pb2.GetPublicKeyResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def DeletePublicKey(
        self, request: user_pb2.DeletePublicKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeletePublicKeyResponse:
        """
        Delete the key from the database.
        """
        db = self.get_db()
        try:
            api_key = db.query(APIKey).filter(
                APIKey.user_id == request.user_id,
                APIKey.id == request.key_id
            ).first()

            if not api_key:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("API key not found")
                return user_pb2.DeletePublicKeyResponse(
                    success=False,
                    message="API key not found"
                )

            db.delete(api_key)
            db.commit()

            return user_pb2.DeletePublicKeyResponse(
                success=True,
                message="API key deleted successfully"
            )
        except Exception as e:
            db.rollback()
            print(f"Error deleting public key: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to delete API key: {str(e)}")
            return user_pb2.DeletePublicKeyResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def GetUserDetailsByPublicKey(
        self, request: user_pb2.GetUserDetailsByPublicKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetUserDetailsByPublicKeyResponse:
        """
        Validate the public key and return user details (user_id and organisation_id).
        This method combines validation with user detail retrieval.
        """
        db = self.get_db()
        try:
            # First, parse the public key to extract user_id and counter (without encryption key)
            temp_key_manager = HMACAPIKeyManager("temp")  # Temporary manager just for parsing
            parsed_key = temp_key_manager.parse_public_key(request.public_key)

            if not parsed_key:
                return user_pb2.GetUserDetailsByPublicKeyResponse(
                    success=True,
                    message="Invalid public key format",
                    is_validated=False,
                    user_id=None,
                    organisation_id=None
                )

            user_id = parsed_key["user_id"]
            counter = parsed_key["counter"]

            # Verify user exists and get their encryption key
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return user_pb2.GetUserDetailsByPublicKeyResponse(
                    success=True,
                    message="User not found",
                    is_validated=False,
                    user_id=None,
                    organisation_id=None
                )

            # Get user's encryption key from GSM
            encryption_key = self.encryption_manager.get_user_encryption_key(user_id)
            if not encryption_key:
                return user_pb2.GetUserDetailsByPublicKeyResponse(
                    success=True,
                    message="User encryption key not found",
                    is_validated=False,
                    user_id=None,
                    organisation_id=None
                )

            # Create the actual key manager with the real encryption key
            key_manager = HMACAPIKeyManager(encryption_key)

            # Validate the public key using HMAC
            is_valid = key_manager.validate_public_key(request.public_key, encryption_key)

            if not is_valid:
                return user_pb2.GetUserDetailsByPublicKeyResponse(
                    success=True,
                    message="Public key validation failed",
                    is_validated=False,
                    user_id=None,
                    organisation_id=None
                )

            # Check if the API key with the specific counter exists and is active in the database
            api_key_exists = db.query(APIKey).filter(
                APIKey.counter == counter,
                APIKey.user_id == user_id,
                APIKey.is_active == True
            ).first()

            if not api_key_exists:
                return user_pb2.GetUserDetailsByPublicKeyResponse(
                    success=True,
                    message="API key not found or inactive",
                    is_validated=False,
                    user_id=None,
                    organisation_id=None
                )
            return user_pb2.GetUserDetailsByPublicKeyResponse(
                success=True,
                message="User details retrieved successfully",
                is_validated=True,
                user_id=user_id,
                organisation_id=user.default_organization or ""
            )

        except Exception as e:
            print(f"Error getting user details by public key: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get user details: {str(e)}")
            return user_pb2.GetUserDetailsByPublicKeyResponse(
                success=False,
                message="Internal server error",
                is_validated=False,
                user_id=None,
                organisation_id=None
            )
        finally:
            db.close()
