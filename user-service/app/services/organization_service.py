from typing import Any, Dict
import uuid
from datetime import datetime, timedelta
import grpc
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from app.models.user import User, UserRole
from app.grpc import user_pb2, user_pb2_grpc
from app.db.session import SessionLocal
from app.services.auth_headers import AuthService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.redis.redis_service import RedisService
import math
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.models.organization import Organization

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class OrganizationService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.redis_service = RedisService()
        self.kafka_producer = KafkaProducer()
        self.auth_service = AuthService()
    
    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    
    def organization_activity_email(
            self, user: User, organization: Organization, email_type: str
        ) -> Dict[str, str]:
            try:
                # Title mapping for different organization activities
                title_map = {
                    SendEmailTypeEnum.ORG_CREATED.value: f"Organization {organization.title} Created",
                    SendEmailTypeEnum.ORG_UPDATED.value: f"Organization {organization.title} Updated",
                    SendEmailTypeEnum.ORG_DELETED.value: f"Organization {organization.title} Deleted"
                }
                
                # Body mapping for different organization activities
                body_map = {
                    SendEmailTypeEnum.ORG_CREATED.value: f"A new organization '{organization.title}' has been created successfully",
                    SendEmailTypeEnum.ORG_UPDATED.value: f"The organization '{organization.title}' has been updated",
                    SendEmailTypeEnum.ORG_DELETED.value: f"The organization '{organization.title}' has been deleted"
                }

                # Get title and body from maps, with defaults for unexpected email types
                title = title_map.get(email_type, "Organization Update")
                body = body_map.get(email_type, f"Organization {organization.title} has been updated")
                
                self.kafka_producer.send_email_event_unified(
                    email_type=email_type,
                    data={
                        "emailId": user.email,
                        "userName": user.full_name,
                        "userId": user.id,
                        "fcmToken": user.fcm_token,
                        "title": title,
                        "link": f"https://example.com/organizations/{organization.id}",
                        "logo": "https://example.com/logo.png",
                        "body": body,
                        "orgName": organization.title,
                        "timestamp": datetime.utcnow().isoformat()
                    },
                    action=["sendOrgEmail", "sendNotification"]
                )
            except Exception as e:
                raise Exception("Failed to send email")

    def organization_member_email(
            self, user: User, organization: Organization, email_type: str
        ) -> Dict[str, str]:
            try:
                title_map = {
                    SendEmailTypeEnum.ORG_USER_ADDED.value: f"Added to {organization.title}",
                    SendEmailTypeEnum.ORG_USER_REMOVED.value: f"Removed from {organization.title}"
                }
                
                body_map = {
                    SendEmailTypeEnum.ORG_USER_ADDED.value: f"You have been added to {organization.title}",
                    SendEmailTypeEnum.ORG_USER_REMOVED.value: f"You have been removed from {organization.title}"
                }
                
                self.kafka_producer.send_email_event_unified(
                    email_type=email_type,
                    data={
                        "emailId": user.email,
                        "userName": user.full_name,
                        "userId": user.id,
                        "fcmToken": user.fcm_token,
                        "title": title_map.get(email_type, "Organization Membership Update"),
                        "link": f"https://example.com/organizations/{organization.id}",
                        "logo": "https://example.com/logo.png",
                        "body": body_map.get(email_type, "Your organization membership has been updated"),
                        "orgName": organization.title
                    },
                    action=["sendEmailToMember","sendNotification"]
                )
            except Exception as e:
                raise Exception("Failed to send email")
            
    def _get_organization_or_404(self, db: Session, org_id: str) -> Organization:
         organization = db.query(Organization).filter(Organization.id == org_id).first()
         if not organization:
             raise ValueError(f"Organization not found: {org_id}") # Will be mapped to NOT_FOUND
         return organization

    def _check_is_owner(self, organization: Organization, user_id: str):
        if organization.owner_id != user_id:
            # Consider adding admin role check here if admins should bypass ownership
            raise PermissionError("User is not the owner of the organization") # Map to PERMISSION_DENIED

    def _get_user_or_404(self, db: Session, user_id: str) -> User:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError(f"User not found: {user_id}") # Map to NOT_FOUND
        return user

    def _build_organization_info(self, organization: Organization, owner_info: user_pb2.UserInfo) -> user_pb2.OrganizationInfo:
         """Helper to convert SQLAlchemy Organization model to gRPC OrganizationInfo"""
         return user_pb2.OrganizationInfo(
             id=organization.id,
             title=organization.title,
             description=organization.description or "", # Handle None description
             ownerId=organization.owner_id,
             userIds=organization.user_ids, # Directly use the list
             createdAt=organization.created_at.isoformat(),
             updatedAt=organization.updated_at.isoformat(),
             owner=owner_info # Attach owner details
         )

    def createOrganization(
        self, request: user_pb2.CreateOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        db = self.get_db()
        try:
            print(f"DEBUG: Starting organization creation for requester: {request.requesterUserId}")
            
            # Validate owner exists
            print(f"DEBUG: Validating owner existence")
            owner = self._get_user_or_404(db, request.requesterUserId)
            print(f"DEBUG: Owner validated successfully: {owner.email}")

            print(f"DEBUG: Creating new organization with title: {request.title}")
            new_org = Organization(
                id=str(uuid.uuid4()),
                title=request.title,
                description=request.description,
                owner_id=request.requesterUserId,
                user_ids=[request.requesterUserId] # Owner is initially the only member
            )
            print(f"DEBUG: Adding organization to database")
            db.add(new_org)
            
            print(f"DEBUG: Committing transaction")
            db.commit()
            db.refresh(new_org)
            print(f"DEBUG: Organization created successfully with ID: {new_org.id}")

            print(f"DEBUG: Building owner info for response")
            owner_info = user_pb2.UserInfo( # Build owner info for response
                userId=owner.id, email=owner.email, fullName=owner.full_name,
                createdAt=owner.created_at.isoformat(), updatedAt=owner.updated_at.isoformat(), role=owner.role.value
            )

            print(f"DEBUG: Building organization info")
            org_info = self._build_organization_info(new_org, owner_info)

            print(f"DEBUG: Sending organization creation email")
            self.organization_activity_email(owner,new_org,SendEmailTypeEnum.ORG_CREATED.value)

            print(f"DEBUG: Returning successful response")
            return user_pb2.OrganizationResponse(
                success=True,
                message="Organization created successfully",
                organization=org_info
            )
        except ValueError as ve: # Catches user/org not found
            print(f"DEBUG: ValueError occurred: {str(ve)}")
            db.rollback()
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.OrganizationResponse(success=False, message=str(ve))
        except Exception as e:
            print(f"DEBUG: Unexpected error occurred: {str(e)}")
            print(f"DEBUG: Error type: {type(e).__name__}")
            import traceback
            print(f"DEBUG: Traceback: {traceback.format_exc()}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to create organization: {str(e)}")
            return user_pb2.OrganizationResponse(success=False, message="Internal server error")
        finally:
            print("DEBUG: Closing database connection")
            db.close()

    def getOrganization(
        self, request: user_pb2.GetOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        db = self.get_db()
        try:
            org = self._get_organization_or_404(db, request.organizationId)

            # Optional: Add check if requester is a member or owner if needed
            # if request.requesterUserId not in org.user_ids and org.owner_id != request.requesterUserId:
            #     raise PermissionError("User does not have access to this organization")

            # Fetch owner details for the response
            owner = self._get_user_or_404(db, org.owner_id)
            owner_info = user_pb2.UserInfo(
                userId=owner.id, email=owner.email, fullName=owner.full_name,
                createdAt=owner.created_at.isoformat(), updatedAt=owner.updated_at.isoformat(), role=owner.role.value
            )

            org_info = self._build_organization_info(org, owner_info)

            return user_pb2.OrganizationResponse(
                success=True,
                message="Organization retrieved successfully",
                organization=org_info
            )
        except ValueError as ve: # Catches user/org not found
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.OrganizationResponse(success=False, message=str(ve))
        except PermissionError as pe:
             context.set_code(grpc.StatusCode.PERMISSION_DENIED)
             context.set_details(str(pe))
             return user_pb2.OrganizationResponse(success=False, message=str(pe))
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve organization: {str(e)}")
            return user_pb2.OrganizationResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def updateOrganization(
        self, request: user_pb2.UpdateOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        db = self.get_db()
        try:
            org = self._get_organization_or_404(db, request.organizationId)
            self._check_is_owner(org, request.requesterUserId) # Only owner can update

            updated = False
            if request.HasField("title"):
                org.title = request.title
                updated = True
            if request.HasField("description"):
                org.description = request.description
                updated = True

            if updated:
                org.updated_at = datetime.utcnow() # Manually update timestamp if needed by logic
                db.commit()
                db.refresh(org)

            # Fetch owner details for the response
            owner = self._get_user_or_404(db, org.owner_id)
            owner_info = user_pb2.UserInfo(
                userId=owner.id, email=owner.email, fullName=owner.full_name,
                createdAt=owner.created_at.isoformat(), updatedAt=owner.updated_at.isoformat(), role=owner.role.value
            )

            org_info = self._build_organization_info(org, owner_info)

            self.organization_activity_email(owner,org_info,SendEmailTypeEnum.ORG_UPDATED.value)

            return user_pb2.OrganizationResponse(
                success=True,
                message="Organization updated successfully",
                organization=org_info
            )
        except ValueError as ve: # Catches user/org not found
            db.rollback()
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.OrganizationResponse(success=False, message=str(ve))
        except PermissionError as pe:
            db.rollback()
            context.set_code(grpc.StatusCode.PERMISSION_DENIED)
            context.set_details(str(pe))
            return user_pb2.OrganizationResponse(success=False, message=str(pe))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to update organization: {str(e)}")
            return user_pb2.OrganizationResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def deleteOrganization(
        self, request: user_pb2.DeleteOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteOrganizationResponse:
        db = self.get_db()
        try:
            org = self._get_organization_or_404(db, request.organizationId)
            self._check_is_owner(org, request.requesterUserId) # Only owner can delete
            owner = self._get_user_or_404(db, org.owner_id)

            db.delete(org)
            db.commit()

            self.organization_activity_email(owner,org,SendEmailTypeEnum.ORG_DELETED.value)

            return user_pb2.DeleteOrganizationResponse(
                success=True,
                message="Organization deleted successfully"
            )
        except ValueError as ve: # Catches org not found
            db.rollback()
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.DeleteOrganizationResponse(success=False, message=str(ve))
        except PermissionError as pe:
            db.rollback()
            context.set_code(grpc.StatusCode.PERMISSION_DENIED)
            context.set_details(str(pe))
            return user_pb2.DeleteOrganizationResponse(success=False, message=str(pe))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to delete organization: {str(e)}")
            return user_pb2.DeleteOrganizationResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def addUserToOrganization(
         self, request: user_pb2.AddUserToOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
         db = self.get_db()
         try:
             org = self._get_organization_or_404(db, request.organizationId)
             self._check_is_owner(org, request.requesterUserId) # Only owner can add users

             # Check if user to add exists
             user_to_add = self._get_user_or_404(db, request.userIdToAdd)

             # Add user if not already present (handles potential duplicates)
             current_users = set(org.user_ids)
             if request.userIdToAdd in current_users:
                 raise ValueError("User is already a member of this organization") # Map to ALREADY_EXISTS maybe? or just bad request

             current_users.add(request.userIdToAdd)
             org.user_ids = list(current_users) # Update the JSON field
             org.updated_at = datetime.utcnow()

             db.commit()
             db.refresh(org)

             # Fetch owner details for the response
             owner = self._get_user_or_404(db, org.owner_id)
             owner_info = user_pb2.UserInfo(
                userId=owner.id, email=owner.email, fullName=owner.full_name,
                createdAt=owner.created_at.isoformat(), updatedAt=owner.updated_at.isoformat(), role=owner.role.value
             )
             org_info = self._build_organization_info(org, owner_info)

             self.organization_member_email(user=user_to_add,organization=org,email_type=SendEmailTypeEnum.ORG_USER_ADDED.value)

             return user_pb2.OrganizationResponse(
                 success=True,
                 message=f"User {user_to_add.email} added successfully",
                 organization=org_info
             )
         except ValueError as ve: # Catches user/org not found or already member
             db.rollback()
             # Distinguish between NOT_FOUND and ALREADY_EXISTS/BAD_REQUEST if needed
             if "not found" in str(ve):
                context.set_code(grpc.StatusCode.NOT_FOUND)
             else: # "already a member" or other value errors
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT) # Or ALREADY_EXISTS
             context.set_details(str(ve))
             return user_pb2.OrganizationResponse(success=False, message=str(ve))
         except PermissionError as pe:
             db.rollback()
             context.set_code(grpc.StatusCode.PERMISSION_DENIED)
             context.set_details(str(pe))
             return user_pb2.OrganizationResponse(success=False, message=str(pe))
         except Exception as e:
             db.rollback()
             context.set_code(grpc.StatusCode.INTERNAL)
             context.set_details(f"Failed to add user: {str(e)}")
             return user_pb2.OrganizationResponse(success=False, message="Internal server error")
         finally:
             db.close()

    def removeUserFromOrganization(
         self, request: user_pb2.RemoveUserFromOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
         db = self.get_db()
         try:
             org = self._get_organization_or_404(db, request.organizationId)
             self._check_is_owner(org, request.requesterUserId) # Only owner can remove users

             # Check if user to remove exists (optional, but good practice)
             user_to_remove = self._get_user_or_404(db, request.userIdToRemove)

             # Prevent owner from removing themselves
             if org.owner_id == request.userIdToRemove:
                 raise ValueError("Owner cannot be removed from the organization") # Map to INVALID_ARGUMENT

             # Remove user if present
             current_users = set(org.user_ids)
             if request.userIdToRemove not in current_users:
                 raise ValueError("User is not a member of this organization") # Map to NOT_FOUND or INVALID_ARGUMENT

             current_users.remove(request.userIdToRemove)
             org.user_ids = list(current_users) # Update the JSON field
             org.updated_at = datetime.utcnow()

             db.commit()
             db.refresh(org)

             # Fetch owner details for the response
             owner = self._get_user_or_404(db, org.owner_id)
             owner_info = user_pb2.UserInfo(
                userId=owner.id, email=owner.email, fullName=owner.full_name,
                createdAt=owner.created_at.isoformat(), updatedAt=owner.updated_at.isoformat(), role=owner.role.value
             )
             org_info = self._build_organization_info(org, owner_info)

             self.organization_member_email(user=user_to_remove,organization=org,email_type=SendEmailTypeEnum.ORG_USER_REMOVED.value)

             return user_pb2.OrganizationResponse(
                 success=True,
                 message=f"User {user_to_remove.email} removed successfully",
                 organization=org_info
             )
         except ValueError as ve: # Catches user/org not found, not member, owner removal
             db.rollback()
             if "not found" in str(ve) or "not a member" in str(ve):
                 context.set_code(grpc.StatusCode.NOT_FOUND) # Or INVALID_ARGUMENT
             else: # Owner removal or other value errors
                 context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
             context.set_details(str(ve))
             return user_pb2.OrganizationResponse(success=False, message=str(ve))
         except PermissionError as pe:
             db.rollback()
             context.set_code(grpc.StatusCode.PERMISSION_DENIED)
             context.set_details(str(pe))
             return user_pb2.OrganizationResponse(success=False, message=str(pe))
         except Exception as e:
             db.rollback()
             context.set_code(grpc.StatusCode.INTERNAL)
             context.set_details(f"Failed to remove user: {str(e)}")
             return user_pb2.OrganizationResponse(success=False, message="Internal server error")
         finally:
             db.close()

    def listUserOrganizations(
        self, request: user_pb2.ListUserOrganizationsRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListOrganizationsResponse:
        db = self.get_db()
        print(f"DEBUG: Starting listUserOrganizations with request: {request}")
        
        try:
            user_id = request.requesterUserId
            print(f"DEBUG: Processing for user_id: {user_id}")
            
            # Check if user exists
            requesting_user = self._get_user_or_404(db, user_id)
            print(f"DEBUG: User found: {requesting_user.email}")
            
            # First, query for organizations where user is the owner
            owned_orgs_query = db.query(Organization).filter(
                Organization.owner_id == user_id
            )
            print(f"DEBUG: Owner query built")
            
            # Execute the owner query to get owned organizations
            owned_organizations = owned_orgs_query.all()
            print(f"DEBUG: Found {len(owned_organizations)} owned organizations")
            
            # Get all organizations (inefficient but safer for now)
            all_orgs = db.query(Organization).all()
            print(f"DEBUG: Fetched all {len(all_orgs)} organizations for member check")
            
            # Filter organizations where user is a member using Python
            member_organizations = []
            for org in all_orgs:
                # Skip owner's orgs to avoid duplicates
                if org.owner_id == user_id:
                    continue
                    
                # Check if user_id is in the user_ids list
                print(f"DEBUG: Checking membership for org {org.id}, user_ids: {org.user_ids}")
                if org.user_ids and user_id in org.user_ids:
                    member_organizations.append(org)
            
            print(f"DEBUG: Found {len(member_organizations)} member organizations")
            
            # Combine both lists
            organizations = owned_organizations + member_organizations
            print(f"DEBUG: Combined total: {len(organizations)} organizations")
            
            # Sort by created_at in descending order (newest first)
            organizations.sort(key=lambda org: org.created_at, reverse=True)
            
            # Fetch owner details for all organizations efficiently
            owner_ids = {org.owner_id for org in organizations}
            print(f"DEBUG: Collected {len(owner_ids)} unique owner IDs")
            
            # Handle case where there are no organizations found for the user
            if not owner_ids:
                owners_map = {}
                print(f"DEBUG: No owners to fetch")
            else:
                owners = db.query(User).filter(User.id.in_(owner_ids)).all()
                owners_map = {owner.id: owner for owner in owners}
                print(f"DEBUG: Fetched {len(owners_map)} owners")
            
            org_infos = []
            for org in organizations:
                print(f"DEBUG: Processing org: {org.id} - {org.title}")
                print(f"DEBUG: Org user_ids type: {type(org.user_ids)}")
                if org.user_ids:
                    print(f"DEBUG: Org user_ids content: {org.user_ids}")
                
                owner = owners_map.get(org.owner_id)
                if owner:
                    print(f"DEBUG: Found owner: {owner.email}")
                    owner_info = user_pb2.UserInfo(
                        userId=owner.id,
                        email=owner.email,
                        fullName=owner.full_name,
                        createdAt=owner.created_at.isoformat(),
                        updatedAt=owner.updated_at.isoformat(),
                        role=owner.role.value
                    )
                    # Build the organization info
                    org_info = self._build_organization_info(org, owner_info)
                    org_infos.append(org_info)
                else:
                    print(f"DEBUG: WARNING - Owner not found for org {org.id}")
            
            print(f"DEBUG: Prepared {len(org_infos)} organization infos for response")
            
            return user_pb2.ListOrganizationsResponse(
                success=True,
                message="Organizations retrieved successfully",
                organizations=org_infos
            )
        except ValueError as ve:
            print(f"DEBUG: User not found error: {ve}")
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.ListOrganizationsResponse(success=False, message=str(ve))
        except Exception as e:
            print(f"DEBUG: Error listing organizations: {e}")
            if hasattr(e, 'orig') and isinstance(e.orig, Exception):
                print(f"DEBUG: Original DB Error: {e.orig}")
            
            import traceback
            print(f"DEBUG: Traceback: {traceback.format_exc()}")
            
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list organizations due to an internal error.")
            return user_pb2.ListOrganizationsResponse(success=False, message="Internal server error")
        finally:
            if db:
                db.close()
                print(f"DEBUG: Database connection closed")

    def generateOrganisationTokens(
        self, request: user_pb2.GenerateOrganisationTokensRequest, context: grpc.ServicerContext
    ) -> user_pb2.GenerateOrganisationTokensResponse:
        """
        Generate access and refresh tokens for an organization.
        
        Args:
            request: Contains organization_id
            context: gRPC context
            
        Returns:
            Response containing access and refresh tokens with their expiration ages
        """
        try:

            db = self.get_db()
            user = db.query(User).filter(User.email == request.user_email).first()
            
            if not user:
                raise ValueError(f"User not found with email: {request.user_email}")

            # Update the user's default_organization with the requested organisation_id
            user.default_organization = request.organisation_id
            db.commit()
            db.refresh(user)

            access_token = self.auth_service._generate_access_token(
                {"email": user.email, "id": user.id, "role": "user", "organization_id": request.organisation_id,"name": user.full_name}
            )
            refresh_token = self.auth_service._generate_refresh_token(
                {"email": user.email, "id": user.id, "role": "user", "organization_id": request.organisation_id,"name": user.full_name}
            )

            return user_pb2.GenerateOrganisationTokensResponse(
                success=True,
                message="Access token generated successfully",
                accessToken=access_token['token'],
                refreshToken=refresh_token['refresh_token']
            )
            
        except ValueError as ve:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details(str(ve))
            return user_pb2.GenerateOrganisationTokensResponse(
                success=False,
                message=str(ve)
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to generate organization tokens: {str(e)}")
            return user_pb2.GenerateOrganisationTokensResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()
