from typing import Any, Dict
import uuid
from datetime import datetime, timedelta
import grpc
from jose import jwt
from passlib.context import CryptContext
import random
from app.core.config import settings
from app.models.user import User, UserRole
from app.grpc import user_pb2, user_pb2_grpc
from app.db.session import SessionLocal
from app.models.waitlist import WaitlistEntry, WaitlistStatus
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.redis.redis_service import RedisService
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.utils.constants.otp_verification_type_enum import OTPVerificationTypeEnum
from app.utils.secret_manager.secret_manager import EncryptionManager

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.redis_service = RedisService()
        self.kafka_producer = KafkaProducer()
        self.encryption_manager = EncryptionManager()

    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def _create_token(self, user_id: str, expires_delta: timedelta) -> str:
        expire = datetime.now() + expires_delta
        to_encode = {"exp": expire, "sub": str(user_id)}
        encoded_jwt = jwt.encode(
            to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM
        )
        return encoded_jwt

    def _generate_refresh_token(self, data: dict) -> dict:
        """
        Generates or fetches a refresh token and stores it in Redis.

        Args:
            data: Dictionary containing email and optional Google OAuth tokens.

        Returns:
            dict: Contains success flag and refresh_token.
        """
        try:
            expire_in = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
            token_expire_at = int(datetime.now().timestamp() + expire_in.total_seconds())
            payload = {
                "exp": token_expire_at,
                "user_id": str(data["id"]),
                "email": data["email"],
                "role": data["role"],
                "name": data.get("name"),
                "organization_id": data.get("organization_id"),
            }

            refresh_token = jwt.encode(
                payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM
            )

            key = f"{settings.ENV}_refreshToken_{data['email']}"
            self.redis_service.set_data_in_redis(key, "token", refresh_token)

            # Set TTL (30 days in seconds)
            self.redis_service.set_ttl_hset(key, settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60)

            return {"success": True, "refresh_token": refresh_token}

        except Exception as e:
            raise e  # Let the caller handle the exception

    def _generate_access_token(self, data: dict) -> dict:
        """
        Generates or fetches an access token and stores it in Redis.

        Args:
            data: Dictionary containing email and optional Google OAuth tokens.

        Returns:
            dict: Contains success flag, token, and expiration time.
        """
        try:
            expire_in = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            token_expire_at = int(datetime.now().timestamp() + expire_in.total_seconds())

            payload = {
                "exp": token_expire_at,
                "user_id": str(data["id"]),
                "email": data["email"],
                "role": data["role"],
                "name": data.get("name"),
                "organization_id": data.get("organization_id")
            }
            token = jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

            key = f"{settings.ENV}_user_login_{data['email']}"

            self.redis_service.set_data_in_redis(key, "token", token)
            self.redis_service.set_ttl_hset(key, settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60)

            return {"success": True, "token": token, "token_expire_at": token_expire_at}

        except Exception as e:
            raise e  # Let the caller handle the exception

    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)

    def _get_password_hash(self, password: str) -> str:
        return pwd_context.hash(password)

    def _generate_numeric_otp(self) -> str:
        return str(random.randint(100000, 999999))

    def _decrypt_otp_token(self, token: str) -> Dict[str, Any]:
        """
        Decrypts a JWT token and returns its payload.

        Args:
            token (str): The JWT token to decrypt.

        Returns:
            Dict containing the decoded token payload.

        Raises:
            UnauthorizedException: If token decryption fails.
        """
        try:
            secret = settings.JWT_SECRET_KEY
            payload = jwt.decode(token, secret, algorithms=[settings.JWT_ALGORITHM])
            return payload
        except Exception as e:
            raise ValueError("OTP is incorrect")

    def _check_otp_token_validity(self, token: str, verification_type_email: str) -> Dict[str, Any]:
        """
        @description A generic method to check the validity of a token provided in an email to a user.
                     This method checks in Redis and authenticates the token.
        @param token - The token to be checked for validity.
        @param verification_type_email - The type of email verification (e.g., PASSWORD, EMAIL, etc.).
        @returns An object containing both the tokenObject and the redisOTPObject.
        """

        # Decrypt the provided token and obtain the tokenObject
        token_object = self._decrypt_otp_token(token)

        # If the token cannot be decrypted, throw an UnauthorizedException
        if not token_object:
            raise ValueError("OTP is Incorrect")

        # Build the Redis key for the specified verification type and user's email
        key = f"{settings.ENV}{verification_type_email}:{token_object.get('email')}"

        # Retrieve the OTP from Redis using the key
        redis_otp_token = self.redis_service.get_data_from_redis_using_key(key)

        # If the OTP token does not exist in Redis, throw a OTP is expired exception
        if not redis_otp_token:
            raise ValueError("OTP is expired")

        # Decrypt the OTP token from Redis and obtain the redisOTPObject
        redis_otp_object = self._decrypt_otp_token(redis_otp_token)
        # Compare the OTP from the provided token with the OTP from Redis
        if token_object.get("otp") != redis_otp_object.get("otp"):
            raise ValueError("OTP is incorrect")

        # Return an object containing both the tokenObject and the redisOTPObject
        return {"token_object": token_object, "redis_OTP_object": redis_otp_object}

    def register(
        self, request: user_pb2.RegisterRequest, context: grpc.ServicerContext
    ) -> user_pb2.RegisterResponse:

        try:
            print("DEBUG: Starting registration process...")
            db = self.get_db()
            # # check is user is approved in waitlist
            #             # check is user is approved in waitlist
            # waitlist_entry = db.query(WaitlistEntry).filter(WaitlistEntry.email == request.email).first()
            # if not waitlist_entry:
            #     raise ValueError("Please join the waitlist first before registering")
            # if waitlist_entry.status != WaitlistStatus.APPROVED:
            #     raise ValueError("Your registration is pending approval from the waitlist")

            # Check if user already exists
            existing_user = db.query(User).filter(User.email == (request.email).lower()).first()
            if existing_user:
                if existing_user.is_active and existing_user.is_email_verified:
                    raise ValueError("Email already registered")
                elif not existing_user.is_email_verified:
                    raise ValueError("Email verification pending")

            # Create new user
            user = User(
                id=str(uuid.uuid4()),
                email=request.email,
                full_name=request.fullName,
                hashed_password=self._get_password_hash(request.password),
                is_active=True,
                is_email_verified=False,  # Initially unverified
                default_organization=request.organizationId or None,
                # Generate a new 6-digit OTP
                otp=self._generate_numeric_otp(),
                otp_timestamp=datetime.utcnow(),
            )

            db.add(user)
            db.flush()

            # Send verification email
            self.resend_verification_email(
                user,
                OTPVerificationTypeEnum.EMAIL.value,
                SendEmailTypeEnum.EMAIL_VERIFICATION.value,
                ["sendEmail"],
            )

            print("f[CREATING CSM ENCRYPTION KEY]")
            # Create or get encryption key for user
            secret_id = self.encryption_manager._get_secret_name(user.id)
            try:
                # Try to get existing key
                self.encryption_manager.get_user_encryption_key(user.id)
                print(f"Using existing encryption key for user: {user.id}")
            except ValueError:
                # Create a new key if it doesn't exist
                self.encryption_manager.create_and_store_user_key(secret_id)
                print(f"Creating new encryption key for user: {user.id}")

            # Commit only if everything succeeds
            db.commit()
            db.refresh(user)

            return user_pb2.RegisterResponse(
                success=True,
                message="An email has been sent to your email for verification",
            )
        except ValueError as ve:
            db.rollback()
            if str(ve) == "Email already registered":
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(str(ve))
            elif str(ve) == "Email verification pending":
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(str(ve))
            return user_pb2.RegisterResponse(success=False, message=str(ve))
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.RegisterResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def login(
        self, request: user_pb2.LoginRequest, context: grpc.ServicerContext
    ) -> user_pb2.LoginResponse:
        try:
            db = self.get_db()
            user = db.query(User).filter(User.email == (request.email).lower()).first()
            if not user:
                raise ValueError("User does not exist")

            if not self._verify_password(request.password, user.hashed_password):
                raise ValueError("Invalid credentials")

            # Check user status
            if not user.is_email_verified:
                self.resend_verification_email(
                    user,
                    OTPVerificationTypeEnum.EMAIL.value,
                    SendEmailTypeEnum.EMAIL_VERIFICATION.value,
                    ["sendEmail"],
                )
                raise ValueError("Account inactive")

            # Update FCM token
            # if user.fcm_token != request.fcmToken:
            #     self.kafka_producer.send_email_event_unified(
            #         email_type=SendEmailTypeEnum.WELCOME.value,
            #         data={
            #             "emailId": user.email,
            #             "userName": user.full_name,
            #             "userId": user.id,
            #             "fcmToken": user.fcm_token,
            #             "title": "New Login Detected",
            #             "link": "https://example.com",
            #             "logo": "https://example.com/logo.png",
            #             "body": "Welcome to our platform!",

            #         },
            #         action=["sendNotification"]
            #     )
            #     user.fcm_token = request.fcmToken

            isFirstLogin = user.is_first_login
            if user.is_first_login:
                user.is_first_login = False

                db.commit()

            # else:
            #     self.kafka_producer.send_email_event_unified(
            #         email_type=SendEmailTypeEnum.WELCOME.value,
            #         data={
            #             "emailId": user.email,
            #             "userName": user.full_name,
            #             "userId": user.id,
            #             "fcmToken": user.fcm_token,
            #             "title": "Login Successful",
            #             "link": "https://example.com",
            #             "logo": "https://example.com/logo.png",
            #             "body": "Welcome to our platform!",

            #         },
            #         action=["sendNotification"]
            #     )

            access_token = self._generate_access_token(
                {"email": user.email, "id": user.id, "role": "user", "organization_id": user.default_organization,"name": user.full_name}
            )
            refresh_token = self._generate_refresh_token(
                {"email": user.email, "id": user.id, "role": "user", "organization_id": user.default_organization,"name": user.full_name}
            )

            return user_pb2.LoginResponse(
                success=True,
                message="Login successful",
                accessToken=access_token["token"],
                refreshToken=refresh_token["refresh_token"],
                user=user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    isFirstLogin=isFirstLogin,
                    organizationId=user.default_organization
                ),
            )
        except ValueError as ve:
            print(f"Failed to login: {str(ve)}")
            db.rollback()
            if str(ve) == "User does not exist":
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(str(ve))
            elif str(ve) == "Invalid credentials":
                print(str(ve))
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(str(ve))
            elif str(ve) == "Account inactive":
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("Account inactive. Verification email resent.")
            return user_pb2.LoginResponse(success=False, message=str(ve))
        except Exception as e:
            print(f"Failed to login: {str(e)}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to login: {str(e)}")
            print(f"Failed to login: {str(e)}")
            return user_pb2.LoginResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def googleOAuthLogin(
        self, request: user_pb2.GoogleOAuthRequest, context: grpc.ServicerContext
    ) -> user_pb2.LoginResponse:
        try:
            db = self.get_db()
            email = request.email
            user = db.query(User).filter(User.email == email).first()
            is_new_user = False
            print(f"EMAIL: {email}")
            print(f"USER: {user}")
            isFirstLogin = False
            if not user:
                is_new_user = True
                user = User(
                    id=str(uuid.uuid4()),
                    email=email,
                    full_name=request.fullName,
                    google_id=request.googleId,
                    is_oauth_user=True,
                    oauth_provider="google",
                    is_email_verified=True,
                )
                db.add(user)
                db.flush()  # So user.id is available
                print(f"USER ID: {user.id}")
                isFirstLogin = user.is_first_login
                if user.is_first_login:
                    user.is_first_login = False

            print(f"IS NEW USER: {is_new_user}")

            # Create or get encryption key for user BEFORE committing to database
            print("[CREATING CSM ENCRYPTION KEY]")
            secret_id = self.encryption_manager._get_secret_name(user.id)
            try:
                self.encryption_manager.get_user_encryption_key(user.id)
                print(f"Using existing encryption key for user: {user.id}")
            except ValueError:
                try:
                    self.encryption_manager.create_and_store_user_key(secret_id)
                    print(f"Creating new encryption key for user: {user.id}")
                except Exception as encryption_error:
                    print(f"Failed to create encryption key for user {user.id}: {encryption_error}")
                    db.rollback()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Failed to create encryption key: {str(encryption_error)}")
                    return user_pb2.LoginResponse(
                        success=False,
                        message="Failed to create user encryption key",
                        accessToken="",
                        refreshToken=""
                    )

            # Commit any DB changes only after encryption key is successfully created
            db.commit()
            db.refresh(user)

        # JWT token and Redis logic continues here...

            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

            payload = {
                "email": user.email,
                "user_id": user.id,
                "role": "user",
                "exp": expire,
                "name": user.full_name,
                "organization_id": user.default_organization
                
            }

            access_token = jwt.encode(
                payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM
            )

            # Store tokens in Redis with TTL
            key = f"{settings.ENV}_user_login_{email}"

            self.redis_service.set_data_in_redis(key, "token", access_token)

            self.redis_service.set_ttl_hset(key, 86400)

            refresh_token = self._generate_refresh_token(
                {"email": user.email, "id": user.id, "role": "user", "name": user.full_name, "organization_id": user.default_organization}
            )

            return user_pb2.LoginResponse(
                success=True,
                message="Google login successful",
                accessToken=access_token,
                refreshToken=refresh_token["refresh_token"],
                user=user_pb2.UserInfo(
                    userId=user.id,
                    email=user.email,
                    fullName=user.full_name,
                    createdAt=user.created_at.isoformat(),
                    updatedAt=user.updated_at.isoformat(),
                    role=user.role.value,
                    isFirstLogin=isFirstLogin,
                    organizationId=user.default_organization
                ),
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return user_pb2.LoginResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def resend_verification_email(
        self, user: User, otp_verification_type: str, email_type: str, action: list
    ) -> Dict[str, str]:
        try:
            # Create a JWT token with OTP and email
            encrypted_otp = jwt.encode(
                {"otp": user.otp, "email": user.email},
                settings.JWT_SECRET_KEY,
                algorithm=settings.JWT_ALGORITHM,
            )

            # Store encrypted OTP in Redis
            redis_key = f"{settings.ENV}{otp_verification_type}:{user.email}"
            self.redis_service.set_data_in_redis_with_ttl(
                redis_key, settings.ENCRYPTED_OTP_VALIDITY, encrypted_otp
            )

            # Determine the frontend route
            frontend_route = (
                "verify-email"
                if email_type == SendEmailTypeEnum.EMAIL_VERIFICATION.value
                else "update-password"
            )

            verification_url = f"{settings.FRONTEND_URL}/{frontend_route}/{encrypted_otp}"

            # Send email via unified kafka method
            self.kafka_producer.send_email_event_unified(
                email_type=email_type,
                data={
                    "emailId": user.email,
                    "userName": user.full_name,
                    "userId": user.id,
                    "fcmToken": user.fcm_token,
                    "otp": verification_url,
                },
                action=action,
            )

            return {"encryptedOtp": encrypted_otp}
        except Exception as e:
            raise Exception("Failed to send email")
