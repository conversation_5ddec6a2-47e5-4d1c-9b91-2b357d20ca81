#!/usr/bin/env python3
"""
Test script to verify API endpoints for API key type functionality
"""

import requests
import json
import sys


def test_api_key_creation():
    """Test API key creation with different types"""
    print("🔑 Testing API Key Creation via API...")
    
    # API Gateway URL (adjust as needed)
    base_url = "http://localhost:8000"  # Adjust port as needed
    
    # Test data for different key types
    test_cases = [
        {
            "name": "Test MCP Key",
            "description": "Test MCP integration key",
            "type": "mcp"
        },
        {
            "name": "Test Workflow Key", 
            "description": "Test workflow automation key",
            "type": "workflow"
        },
        {
            "name": "Test Knowledge Key",
            "description": "Test knowledge base key", 
            "type": "knowledge"
        },
        {
            "name": "Test Voice Key",
            "description": "Test voice interface key",
            "type": "voice"
        }
    ]
    
    # Mock authentication headers (adjust as needed)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer mock_token"  # Replace with actual token
    }
    
    created_keys = []
    
    for test_case in test_cases:
        print(f"Creating {test_case['type']} key...")
        
        try:
            response = requests.post(
                f"{base_url}/api-keys/create-key",
                headers=headers,
                json=test_case,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    api_key = data.get("api_key", {})
                    public_key = api_key.get("public_key", "")
                    
                    # Verify the key format
                    expected_prefix = f"ruh-{test_case['type']}-"
                    if public_key.startswith(expected_prefix):
                        print(f"✅ {test_case['type'].upper()} key created: {public_key[:50]}...")
                        created_keys.append({
                            "id": api_key.get("id"),
                            "type": test_case['type'],
                            "public_key": public_key
                        })
                    else:
                        print(f"❌ {test_case['type'].upper()} key format incorrect: {public_key}")
                else:
                    print(f"❌ {test_case['type'].upper()} key creation failed: {data.get('message')}")
            else:
                print(f"❌ {test_case['type'].upper()} key creation failed with status {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error creating {test_case['type']} key: {e}")
            return None
    
    return created_keys


def test_api_key_listing():
    """Test API key listing with type filtering"""
    print("\n📋 Testing API Key Listing with Type Filtering...")
    
    base_url = "http://localhost:8000"
    headers = {
        "Authorization": "Bearer mock_token"  # Replace with actual token
    }
    
    # Test listing all keys
    try:
        response = requests.get(
            f"{base_url}/api-keys/list",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                all_keys = data.get("api_keys", [])
                print(f"✅ Listed all keys: {len(all_keys)} keys found")
                
                # Show types of all keys
                types_found = set(key.get("type") for key in all_keys if key.get("type"))
                print(f"   Types found: {', '.join(types_found)}")
            else:
                print(f"❌ Listing all keys failed: {data.get('message')}")
        else:
            print(f"❌ Listing all keys failed with status {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error listing all keys: {e}")
    
    # Test filtering by each type
    key_types = ['mcp', 'workflow', 'knowledge', 'voice']
    
    for key_type in key_types:
        try:
            response = requests.get(
                f"{base_url}/api-keys/list?type={key_type}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    filtered_keys = data.get("api_keys", [])
                    print(f"✅ Listed {key_type} keys: {len(filtered_keys)} keys found")
                    
                    # Verify all returned keys are of the correct type
                    for key in filtered_keys:
                        if key.get("type") != key_type:
                            print(f"❌ Wrong type returned: expected {key_type}, got {key.get('type')}")
                            return False
                else:
                    print(f"❌ Listing {key_type} keys failed: {data.get('message')}")
            else:
                print(f"❌ Listing {key_type} keys failed with status {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error listing {key_type} keys: {e}")
    
    return True


def test_invalid_type_rejection():
    """Test that invalid types are rejected"""
    print("\n🚫 Testing Invalid Type Rejection...")
    
    base_url = "http://localhost:8000"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer mock_token"  # Replace with actual token
    }
    
    # Test invalid types
    invalid_cases = [
        {
            "name": "Invalid Type Key",
            "description": "This should fail",
            "type": "invalid_type"
        },
        {
            "name": "Empty Type Key",
            "description": "This should fail",
            "type": ""
        },
        {
            "name": "Admin Type Key",
            "description": "This should fail", 
            "type": "admin"
        }
    ]
    
    for test_case in invalid_cases:
        try:
            response = requests.post(
                f"{base_url}/api-keys/create-key",
                headers=headers,
                json=test_case,
                timeout=10
            )
            
            # Should return 422 for validation error
            if response.status_code == 422:
                print(f"✅ Invalid type '{test_case['type']}' correctly rejected")
            else:
                print(f"❌ Invalid type '{test_case['type']}' was not rejected (status: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error testing invalid type '{test_case['type']}': {e}")


def main():
    """Run API endpoint tests"""
    print("🚀 Starting API Endpoint Tests for API Key Type Functionality...\n")
    
    print("⚠️  Note: These tests require the API Gateway to be running on localhost:8000")
    print("⚠️  Note: Authentication may need to be adjusted for your setup\n")
    
    try:
        # Test key creation
        created_keys = test_api_key_creation()
        
        if created_keys:
            print(f"\n✅ Successfully created {len(created_keys)} test keys")
        
        # Test key listing
        test_api_key_listing()
        
        # Test invalid type rejection
        test_invalid_type_rejection()
        
        print("\n🎉 API endpoint tests completed!")
        print("\n📋 Summary:")
        print("✅ API key creation with type validation")
        print("✅ API key listing with type filtering")
        print("✅ Invalid type rejection")
        print("✅ Proper key format generation (ruh-{type}-{counter}-{key}-{user_id})")
        
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
