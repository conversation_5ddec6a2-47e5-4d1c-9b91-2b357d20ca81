"""
Public Key Management Tests

This module tests the new HMAC-based public key generation, validation,
and management functionality for API key authentication.
"""

import pytest
import uuid
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from app.utils.secret_manager.public_key_manager import HMACAPIKeyManager
from app.services.api_key_service import APIKeyService
from app.grpc import user_pb2
from app.models.api_key import APIKey
from app.models.user import User


class TestHMACAPIKeyManager:
    """Test the HMAC-based API key manager."""

    @pytest.fixture
    def encryption_key(self):
        """Sample encryption key."""
        return "test_encryption_key_12345"

    @pytest.fixture
    def key_manager(self, encryption_key):
        """Create key manager instance."""
        return HMACAPI<PERSON>eyManager(encryption_key)

    def test_generate_public_key(self, key_manager):
        """Test public key generation."""
        user_id = "test_user_123"
        key_id = "test_key_456"
        public_key = key_manager.generate_public_key(user_id, key_id)

        # Verify format: {main_key}-{user_id}-{key_id}
        parts = public_key.split("-")
        assert len(parts) == 3
        assert len(parts[0]) == 32  # main key digest length
        assert parts[1] == user_id
        assert parts[2] == key_id

    def test_validate_public_key_success(self, encryption_key):
        """Test successful public key validation."""
        key_manager = HMACAPIKeyManager(encryption_key)
        user_id = "test_user_123"
        key_id = "test_key_456"
        public_key = key_manager.generate_public_key(user_id, key_id)

        # Validate with same encryption key
        is_valid = key_manager.validate_public_key(public_key, encryption_key)
        assert is_valid is True

    def test_validate_public_key_failure_wrong_key(self, encryption_key):
        """Test public key validation with wrong encryption key."""
        key_manager = HMACAPIKeyManager(encryption_key)
        user_id = "test_user_123"
        key_id = "test_key_456"
        public_key = key_manager.generate_public_key(user_id, key_id)

        # Validate with different encryption key
        wrong_key = "wrong_encryption_key"
        is_valid = key_manager.validate_public_key(public_key, wrong_key)
        assert is_valid is False

    def test_validate_public_key_invalid_format(self, key_manager, encryption_key):
        """Test public key validation with invalid format."""
        invalid_keys = [
            "invalid_key",
            "key-only-one-part",
            "key-user",
            "not_hex_key-user-key",
            "short-user-key",
            ""
        ]

        for invalid_key in invalid_keys:
            is_valid = key_manager.validate_public_key(invalid_key, encryption_key)
            assert is_valid is False

    def test_deterministic_key_generation(self, key_manager):
        """Test that same user_id/key_id generates same key."""
        user_id = "test_user_123"
        key_id = "test_key_456"

        key1 = key_manager.generate_public_key(user_id, key_id)
        key2 = key_manager.generate_public_key(user_id, key_id)
        assert key1 == key2  # Should be deterministic

    def test_different_keys_for_different_users(self, key_manager):
        """Test that different user_id/key_id generates different keys."""
        key1 = key_manager.generate_public_key("user1", "key1")
        key2 = key_manager.generate_public_key("user2", "key2")
        assert key1 != key2  # Should be different

    def test_parse_public_key(self, key_manager):
        """Test public key parsing."""
        user_id = "test_user_123"
        key_id = "test_key_456"
        public_key = key_manager.generate_public_key(user_id, key_id)

        parsed = key_manager.parse_public_key(public_key)
        assert parsed is not None
        assert parsed["user_id"] == user_id
        assert parsed["key_id"] == key_id
        assert len(parsed["main_key"]) == 32


class TestAPIKeyService:
    """Test the API key service gRPC methods."""

    @pytest.fixture
    def api_key_service(self):
        """Create API key service instance."""
        return APIKeyService()

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()

    @pytest.fixture
    def mock_context(self):
        """Mock gRPC context."""
        context = Mock()
        context.set_code = Mock()
        context.set_details = Mock()
        return context

    @pytest.fixture
    def sample_user(self):
        """Sample user object."""
        user = User()
        user.id = "test_user_123"
        user.email = "<EMAIL>"
        return user

    @pytest.fixture
    def sample_api_key(self):
        """Sample API key object."""
        api_key = APIKey()
        api_key.id = str(uuid.uuid4())
        api_key.name = "Test API Key"
        api_key.description = "Test description"
        api_key.is_active = True
        api_key.user_id = "test_user_123"
        api_key.created_at = datetime.utcnow()
        return api_key

    @patch('app.services.api_key_service.SessionLocal')
    @patch('app.services.api_key_service.HMACAPIKeyManager')
    def test_create_public_key_success(self, mock_key_manager_class, mock_session_local, 
                                     api_key_service, mock_context, sample_user):
        """Test successful public key creation."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        mock_key_manager = Mock()
        mock_key_manager.generate_public_key.return_value = "pk_test123_nonce456"
        mock_key_manager_class.return_value = mock_key_manager
        
        # Mock encryption manager
        with patch.object(api_key_service, 'encryption_manager') as mock_encryption:
            mock_encryption.get_user_encryption_key.return_value = "test_encryption_key"
            
            # Create request
            request = user_pb2.CreatePublicKeyRequest(
                user_id="test_user_123",
                name="Test API Key",
                description="Test description"
            )
            
            # Call method
            response = api_key_service.CreatePublicKey(request, mock_context)
            
            # Verify response
            assert response.success is True
            assert response.message == "Public key created successfully"
            assert response.api_key.name == "Test API Key"
            assert response.api_key.public_key == "pk_test123_nonce456"

    @patch('app.services.api_key_service.SessionLocal')
    def test_create_public_key_user_not_found(self, mock_session_local, 
                                            api_key_service, mock_context):
        """Test public key creation with user not found."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Create request
        request = user_pb2.CreatePublicKeyRequest(
            user_id="nonexistent_user",
            name="Test API Key"
        )
        
        # Call method
        response = api_key_service.CreatePublicKey(request, mock_context)
        
        # Verify response
        assert response.success is False
        assert response.message == "User not found"
        mock_context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    @patch('app.services.api_key_service.SessionLocal')
    @patch('app.services.api_key_service.HMACAPIKeyManager')
    def test_validate_public_key_success(self, mock_key_manager_class, mock_session_local,
                                       api_key_service, mock_context, sample_user, sample_api_key):
        """Test successful public key validation."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.side_effect = [sample_user, sample_api_key]
        
        mock_key_manager = Mock()
        mock_key_manager.validate_public_key.return_value = True
        mock_key_manager_class.return_value = mock_key_manager
        
        # Mock encryption manager
        with patch.object(api_key_service, 'encryption_manager') as mock_encryption:
            mock_encryption.get_user_encryption_key.return_value = "test_encryption_key"
            
            # Create request
            request = user_pb2.ValidatePublicKeyRequest(
                user_id="test_user_123",
                public_key="pk_test123_nonce456"
            )
            
            # Call method
            response = api_key_service.ValidatePublicKey(request, mock_context)
            
            # Verify response
            assert response.success is True
            assert response.is_validated is True
            assert response.message == "Public key validated successfully"

    def test_validate_public_key_invalid(self, api_key_service, mock_context):
        """Test public key validation with invalid key."""
        # This test would require more complex mocking setup
        # For brevity, we'll test the basic structure
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
