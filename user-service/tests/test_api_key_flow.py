#!/usr/bin/env python3
"""
Test script to verify API key type functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.secret_manager.public_key_manager import HMACAPIKeyManager


def test_key_generation():
    """Test the new key generation format"""
    print("🔑 Testing API Key Generation...")
    
    # Mock encryption key
    encryption_key = "test_encryption_key_12345"
    key_manager = HMACAPIKeyManager(encryption_key)
    
    # Test data
    user_id = "user_123"
    counter = 1
    
    # Test different key types
    key_types = ['mcp', 'workflow', 'knowledge', 'voice']
    
    for key_type in key_types:
        public_key = key_manager.generate_public_key(user_id, counter, key_type)
        print(f"✅ {key_type.upper()} key: {public_key}")
        
        # Verify format
        expected_prefix = f"ruh-{key_type}-{counter}-"
        assert public_key.startswith(expected_prefix), f"Key should start with {expected_prefix}"
        assert public_key.endswith(f"-{user_id}"), f"Key should end with -{user_id}"
        
        # Verify parts count
        parts = public_key.split("-")
        assert len(parts) >= 4, f"Key should have at least 4 parts, got {len(parts)}"
        
        counter += 1
    
    print("✅ Key generation test passed!")


def test_key_validation():
    """Test key validation"""
    print("\n🔍 Testing API Key Validation...")
    
    encryption_key = "test_encryption_key_12345"
    key_manager = HMACAPIKeyManager(encryption_key)
    
    user_id = "user_123"
    counter = 1
    key_type = "mcp"
    
    # Generate a valid key
    valid_key = key_manager.generate_public_key(user_id, counter, key_type)
    print(f"Generated key: {valid_key}")
    
    # Test validation with correct key
    is_valid = key_manager.validate_public_key(valid_key, encryption_key)
    assert is_valid, "Valid key should pass validation"
    print("✅ Valid key validation passed!")
    
    # Test validation with wrong encryption key
    wrong_key = "wrong_encryption_key"
    is_invalid = key_manager.validate_public_key(valid_key, wrong_key)
    assert not is_invalid, "Key with wrong encryption key should fail validation"
    print("✅ Invalid encryption key validation passed!")
    
    # Test validation with malformed key
    malformed_keys = [
        "invalid-key",
        "ruh-mcp-abc-key-user",  # non-numeric counter
        "ruh-invalid_type-1-key-user",  # invalid type
        "ruh-mcp-1-short-user",  # short key
        "ruh-mcp-1",  # too few parts
    ]
    
    for malformed_key in malformed_keys:
        is_invalid = key_manager.validate_public_key(malformed_key, encryption_key)
        assert not is_invalid, f"Malformed key should fail validation: {malformed_key}"
    
    print("✅ Malformed key validation passed!")


def test_key_parsing():
    """Test key parsing"""
    print("\n📝 Testing API Key Parsing...")
    
    encryption_key = "test_encryption_key_12345"
    key_manager = HMACAPIKeyManager(encryption_key)
    
    user_id = "user_123"
    counter = 2
    key_type = "workflow"
    
    # Generate a key
    public_key = key_manager.generate_public_key(user_id, counter, key_type)
    print(f"Generated key: {public_key}")
    
    # Parse the key
    parsed = key_manager.parse_public_key(public_key)
    assert parsed is not None, "Key should be parseable"
    
    # Verify parsed components
    assert parsed["prefix"] == "ruh", f"Expected prefix 'ruh', got {parsed['prefix']}"
    assert parsed["counter"] == counter, f"Expected counter {counter}, got {parsed['counter']}"
    assert parsed["type"] == key_type, f"Expected type {key_type}, got {parsed['type']}"
    assert parsed["user_id"] == user_id, f"Expected user_id {user_id}, got {parsed['user_id']}"
    
    print(f"✅ Parsed components: {parsed}")
    
    # Test parsing invalid keys
    invalid_keys = [
        "invalid-key",
        "ruh-mcp-1",  # too few parts
        "ruh-invalid_type-1-key-user",  # invalid type
    ]
    
    for invalid_key in invalid_keys:
        parsed = key_manager.parse_public_key(invalid_key)
        assert parsed is None, f"Invalid key should not be parseable: {invalid_key}"
    
    print("✅ Key parsing test passed!")


def test_type_validation():
    """Test that only valid types are accepted"""
    print("\n🎯 Testing Type Validation...")
    
    encryption_key = "test_encryption_key_12345"
    key_manager = HMACAPIKeyManager(encryption_key)
    
    valid_types = ['mcp', 'workflow', 'knowledge', 'voice']
    invalid_types = ['invalid', 'test', 'admin', '']
    
    user_id = "user_123"
    counter = 1
    
    # Test valid types
    for key_type in valid_types:
        public_key = key_manager.generate_public_key(user_id, counter, key_type)
        parsed = key_manager.parse_public_key(public_key)
        assert parsed is not None, f"Valid type {key_type} should be parseable"
        assert parsed["type"] == key_type, f"Parsed type should match {key_type}"
    
    print("✅ Valid types test passed!")
    
    # Test that invalid types in manually crafted keys are rejected
    for invalid_type in invalid_types:
        # Create a manually crafted key with invalid type
        fake_key = f"ruh-{invalid_type}-1-abcd1234567890abcdef1234567890ab-user_123"
        parsed = key_manager.parse_public_key(fake_key)
        assert parsed is None, f"Invalid type {invalid_type} should be rejected"
    
    print("✅ Invalid types rejection test passed!")


def main():
    """Run all tests"""
    print("🚀 Starting API Key Type Functionality Tests...\n")
    
    try:
        test_key_generation()
        test_key_validation()
        test_key_parsing()
        test_type_validation()
        
        print("\n🎉 All tests passed! API Key type functionality is working correctly.")
        print("\n📋 Summary:")
        print("✅ Key generation with type prefix")
        print("✅ Key validation (only new format)")
        print("✅ Key parsing (only new format)")
        print("✅ Type validation (mcp, workflow, knowledge, voice)")
        print("✅ Rejection of old format and invalid types")
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
