import json
import tempfile
import os
import structlog
from typing import <PERSON><PERSON>, Optional, Dict, Any
from google.oauth2 import service_account
from google.oauth2.credentials import Credentials

from app.db.neo4j import execute_read_query
from app.utils.constants.sources import SourceType

logger = structlog.get_logger()

def get_source_auth_info(organisation_id: str, source_type: str = None) -> Optional[Dict[str, Any]]:
    """
    Get source authentication information including auth type and credentials.
    
    Args:
        organisation_id: The ID of the organization
        source_type: The type of source (default: google_drive)
        
    Returns:
        Dictionary with auth info or None if not found
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    
    query = """
    MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
    WHERE s.type = $source_type
    RETURN s.auth_type as auth_type,
           s.key as service_account_key,
           s.oauth_credentials as oauth_credentials,
           s.oauth_user_email as oauth_user_email,
           s.name as source_name
    """
    
    params = {
        "org_id": organisation_id,
        "source_type": source_type
    }
    
    result = execute_read_query(query, params)
    if not result:
        logger.error(f"No source found for organization {organisation_id} with type {source_type}")
        return None
    
    source_data = result[0]
    auth_type = source_data.get('auth_type', 'service_account')  # Default to service_account for backward compatibility
    
    return {
        'auth_type': auth_type,
        'service_account_key': source_data.get('service_account_key'),
        'oauth_credentials': source_data.get('oauth_credentials'),
        'oauth_user_email': source_data.get('oauth_user_email'),
        'source_name': source_data.get('source_name')
    }

def get_source_credentials(organisation_id, source_type=None, credential_type='oauth'):
    """
    Retrieve credentials from a Source node.
    
    Args:
        organisation_id: The ID of the organization
        source_type: The type of source (default: google_drive)
        credential_type: Type of credentials to retrieve ('oauth' or 'service_account')
        
    Returns:
        The credentials JSON string or None if not found
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
        
    # For all source types, we use key field as a generic key
    # This field stores different types of credentials based on the source type:
    # - For Google Drive: Service account JSON
    # - For Jira: API key
    if credential_type == 'service_account':
        field = 's.key'
    else:
        field = 's.oauth_credentials'
    
    query = f"""
    MATCH (o:Organisation {{id: $org_id}})-[:HAS_SOURCE]->(s:Source)
    WHERE s.type = $source_type
    RETURN {field}
    """
    
    params = {
        "org_id": organisation_id,
        "source_type": source_type
    }
    
    result = execute_read_query(query, params)
    if not result or not result[0].get(field):
        logger.error(f"No {credential_type} credentials found for organization {organisation_id}")
        return None
    
    return result[0][field]

def get_service_account_credentials(organisation_id: str, source_type: str = None):
    """
    Get service account credentials for Google API authentication.
    
    Args:
        organisation_id: Organisation ID
        source_type: Source type (default: google_drive)
        
    Returns:
        Google service account credentials object or None
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    
    try:
        credentials_json = get_source_credentials(organisation_id, source_type, 'service_account')
        if not credentials_json:
            logger.error(f"No service account credentials found for organization {organisation_id}")
            return None
        
        # Parse and create service account credentials
        credentials_info = json.loads(credentials_json)
        credentials = service_account.Credentials.from_service_account_info(
            credentials_info,
            scopes=[
                'https://www.googleapis.com/auth/drive.readonly',
                'https://www.googleapis.com/auth/drive.metadata.readonly'
            ]
        )
        
        return credentials
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid service account JSON for organization {organisation_id}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Failed to create service account credentials for organization {organisation_id}: {str(e)}")
        return None

def get_oauth_credentials(organisation_id: str, source_type: str = None) -> Optional[Credentials]:
    """
    Get OAuth credentials for Google API authentication.
    
    Args:
        organisation_id: Organisation ID
        source_type: Source type (default: google_drive)
        
    Returns:
        Google OAuth credentials object or None
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    
    try:
        # Use the OAuth manager to get credentials
        from app.utils.google_oauth_manager import oauth_manager
        return oauth_manager.get_oauth_credentials(organisation_id)
        
    except Exception as e:
        logger.error(f"Failed to get OAuth credentials for organization {organisation_id}: {str(e)}")
        return None

def get_google_credentials(organisation_id: str, source_type: str = None):
    """
    Get Google API credentials (OAuth or service account) based on source configuration.
    
    Args:
        organisation_id: Organisation ID
        source_type: Source type (default: google_drive)
        
    Returns:
        Google credentials object or None
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    
    try:
        # Get source auth info to determine auth type
        auth_info = get_source_auth_info(organisation_id, source_type)
        if not auth_info:
            logger.error(f"No source found for organization {organisation_id}")
            return None
        
        auth_type = auth_info.get('auth_type', 'service_account')
        
        if auth_type == 'oauth':
            logger.info(f"Using OAuth credentials for organization {organisation_id}")
            return get_oauth_credentials(organisation_id, source_type)
        else:
            logger.info(f"Using service account credentials for organization {organisation_id}")
            return get_service_account_credentials(organisation_id, source_type)
            
    except Exception as e:
        logger.error(f"Failed to get Google credentials for organization {organisation_id}: {str(e)}")
        return None

# Backward compatibility function
def get_service_account_credentials_legacy(organisation_id: str, source_type: str = None) -> Tuple[bool, str]:
    """
    Legacy function for backward compatibility.
    Get service account credentials or API key for an organisation.
    
    Args:
        organisation_id: Organisation ID
        source_type: Source type (default: google_drive)
        
    Returns:
        Tuple of (success, credentials_json_or_error_message)
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value
    
    credentials_json = get_source_credentials(organisation_id, source_type, 'service_account')
    if not credentials_json:
        return False, f"No authentication key found for organization {organisation_id}"
    
    return True, credentials_json