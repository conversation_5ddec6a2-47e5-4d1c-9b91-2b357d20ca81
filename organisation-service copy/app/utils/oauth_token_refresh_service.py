"""
OAuth Token Refresh Service

This module provides automatic token refresh functionality for OAuth-authenticated
Google Drive sources. It runs as a background service to proactively refresh
tokens before they expire.
"""

import asyncio
import structlog
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from app.db.neo4j import execute_read_query, execute_write_query
from app.utils.google_oauth_manager import oauth_manager
from app.utils.constants.sources import SourceType

logger = structlog.get_logger()


class OAuthTokenRefreshService:
    """
    Service for automatic OAuth token refresh.
    
    This service periodically checks for OAuth tokens that are about to expire
    and refreshes them automatically to maintain uninterrupted access.
    """
    
    def __init__(self, refresh_interval_minutes: int = 30, token_buffer_minutes: int = 10):
        """
        Initialize the token refresh service.
        
        Args:
            refresh_interval_minutes: How often to check for tokens needing refresh
            token_buffer_minutes: Refresh tokens this many minutes before expiry
        """
        self.refresh_interval = refresh_interval_minutes * 60  # Convert to seconds
        self.token_buffer = token_buffer_minutes * 60  # Convert to seconds
        self.running = False
        self.executor = ThreadPoolExecutor(max_workers=5)
        
    async def start(self):
        """Start the token refresh service."""
        if self.running:
            logger.warning("Token refresh service is already running")
            return
        
        self.running = True
        logger.info("Starting OAuth token refresh service", 
                   refresh_interval_minutes=self.refresh_interval // 60,
                   token_buffer_minutes=self.token_buffer // 60)
        
        # Start the refresh loop
        asyncio.create_task(self._refresh_loop())
    
    async def stop(self):
        """Stop the token refresh service."""
        if not self.running:
            return
        
        self.running = False
        self.executor.shutdown(wait=True)
        logger.info("OAuth token refresh service stopped")
    
    async def _refresh_loop(self):
        """Main refresh loop that runs periodically."""
        while self.running:
            try:
                await self._check_and_refresh_tokens()
                await asyncio.sleep(self.refresh_interval)
            except Exception as e:
                logger.error("Error in token refresh loop", error=str(e))
                # Continue running even if there's an error
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _check_and_refresh_tokens(self):
        """Check for tokens that need refresh and refresh them."""
        try:
            # Get all OAuth sources that might need token refresh
            sources_to_refresh = await self._get_sources_needing_refresh()
            
            if not sources_to_refresh:
                logger.debug("No OAuth tokens need refresh")
                return
            
            logger.info(f"Found {len(sources_to_refresh)} OAuth sources that may need token refresh")
            
            # Refresh tokens concurrently
            refresh_tasks = []
            for source in sources_to_refresh:
                task = asyncio.create_task(
                    self._refresh_source_token(source)
                )
                refresh_tasks.append(task)
            
            # Wait for all refresh operations to complete
            results = await asyncio.gather(*refresh_tasks, return_exceptions=True)
            
            # Log results
            successful_refreshes = sum(1 for result in results if result is True)
            failed_refreshes = len(results) - successful_refreshes
            
            logger.info("Token refresh batch completed",
                       successful=successful_refreshes,
                       failed=failed_refreshes,
                       total=len(results))
            
        except Exception as e:
            logger.error("Error checking and refreshing tokens", error=str(e))
    
    async def _get_sources_needing_refresh(self) -> List[Dict[str, Any]]:
        """Get OAuth sources that need token refresh."""
        try:
            # Calculate the threshold time for token refresh
            threshold_time = datetime.utcnow() + timedelta(seconds=self.token_buffer)
            threshold_iso = threshold_time.isoformat()
            
            query = """
            MATCH (o:Organisation)-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = $source_type 
              AND s.auth_type = 'oauth'
              AND s.oauth_credentials IS NOT NULL
            RETURN o.id as organisation_id,
                   s.id as source_id,
                   s.name as source_name,
                   s.oauth_credentials as oauth_credentials,
                   s.oauth_user_email as user_email,
                   s.updated_at as last_updated
            """
            
            params = {
                "source_type": SourceType.GOOGLE_DRIVE.value
            }
            
            result = execute_read_query(query, params)
            
            sources_needing_refresh = []
            for record in result:
                try:
                    import json
                    oauth_creds = json.loads(record['oauth_credentials'])
                    token_expires_at = oauth_creds.get('token_expires_at')
                    
                    if token_expires_at:
                        # Parse token expiry time
                        try:
                            expiry_time = datetime.fromisoformat(token_expires_at.replace('Z', '+00:00'))
                            # Remove timezone info for comparison (assuming UTC)
                            if expiry_time.tzinfo:
                                expiry_time = expiry_time.replace(tzinfo=None)
                            
                            # Check if token expires within the buffer time
                            if expiry_time <= threshold_time:
                                sources_needing_refresh.append({
                                    'organisation_id': record['organisation_id'],
                                    'source_id': record['source_id'],
                                    'source_name': record['source_name'],
                                    'user_email': record['user_email'],
                                    'token_expires_at': token_expires_at,
                                    'last_updated': record['last_updated']
                                })
                        except (ValueError, TypeError) as e:
                            logger.warning("Invalid token expiry format", 
                                         organisation_id=record['organisation_id'],
                                         token_expires_at=token_expires_at,
                                         error=str(e))
                    else:
                        # If no expiry time, assume token might need refresh
                        # This handles cases where expiry wasn't stored properly
                        sources_needing_refresh.append({
                            'organisation_id': record['organisation_id'],
                            'source_id': record['source_id'],
                            'source_name': record['source_name'],
                            'user_email': record['user_email'],
                            'token_expires_at': None,
                            'last_updated': record['last_updated']
                        })
                        
                except (json.JSONDecodeError, KeyError) as e:
                    logger.warning("Invalid OAuth credentials format", 
                                 organisation_id=record['organisation_id'],
                                 error=str(e))
            
            return sources_needing_refresh
            
        except Exception as e:
            logger.error("Error getting sources needing refresh", error=str(e))
            return []
    
    async def _refresh_source_token(self, source: Dict[str, Any]) -> bool:
        """
        Refresh token for a specific source.
        
        Args:
            source: Source information dictionary
            
        Returns:
            bool: True if refresh was successful, False otherwise
        """
        organisation_id = source['organisation_id']
        source_name = source.get('source_name', 'Unknown')
        user_email = source.get('user_email', 'Unknown')
        
        try:
            logger.info("Refreshing OAuth token",
                       organisation_id=organisation_id,
                       source_name=source_name,
                       user_email=user_email)
            
            # Refresh token using OAuth manager
            success, message, new_token_data = oauth_manager.refresh_access_token(organisation_id)
            
            if success and new_token_data:
                # Update token in database
                updated = await self._update_source_tokens(organisation_id, new_token_data)
                
                if updated:
                    logger.info("OAuth token refreshed successfully",
                               organisation_id=organisation_id,
                               user_email=user_email,
                               new_expires_at=new_token_data.get('token_expires_at'))
                    return True
                else:
                    logger.error("Failed to update refreshed token in database",
                               organisation_id=organisation_id,
                               user_email=user_email)
                    return False
            else:
                logger.warning("Token refresh failed",
                             organisation_id=organisation_id,
                             user_email=user_email,
                             message=message)
                
                # If refresh token is expired, we might need to notify for re-authorization
                if "refresh_token" in message.lower() and "expired" in message.lower():
                    await self._handle_refresh_token_expired(organisation_id, user_email)
                
                return False
                
        except Exception as e:
            logger.error("Error refreshing source token",
                        error=str(e),
                        organisation_id=organisation_id,
                        user_email=user_email)
            return False
    
    async def _update_source_tokens(self, organisation_id: str, token_data: Dict[str, Any]) -> bool:
        """Update OAuth tokens in the database."""
        try:
            import json
            
            oauth_credentials = {
                'access_token': token_data.get('access_token'),
                'refresh_token': token_data.get('refresh_token'),
                'token_expires_at': token_data.get('token_expires_at'),
                'scopes': token_data.get('scopes', []),
                'user_email': token_data.get('user_email'),
                'user_name': token_data.get('user_name')
            }
            
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = $source_type AND s.auth_type = 'oauth'
            SET s.oauth_credentials = $oauth_credentials,
                s.updated_at = $updated_at,
                s.last_validated_at = $updated_at
            RETURN count(s) as updated_count
            """
            
            params = {
                "org_id": organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value,
                "oauth_credentials": json.dumps(oauth_credentials),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = execute_write_query(query, params)
            return result and result[0].get('updated_count', 0) > 0
            
        except Exception as e:
            logger.error("Failed to update source tokens", error=str(e))
            return False
    
    async def _handle_refresh_token_expired(self, organisation_id: str, user_email: str):
        """Handle the case where refresh token has expired."""
        try:
            logger.warning("Refresh token expired, marking source for re-authorization",
                         organisation_id=organisation_id,
                         user_email=user_email)
            
            # Update source to indicate re-authorization needed
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = $source_type AND s.auth_type = 'oauth'
            SET s.is_validated = false,
                s.validation_message = 'Re-authorization required: refresh token expired',
                s.updated_at = $updated_at
            """
            
            params = {
                "org_id": organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            execute_write_query(query, params)
            
            # TODO: Send notification to user about re-authorization requirement
            # This could be implemented as:
            # - Email notification
            # - In-app notification
            # - Webhook to frontend
            
        except Exception as e:
            logger.error("Error handling expired refresh token", error=str(e))
    
    async def refresh_specific_source(self, organisation_id: str) -> bool:
        """
        Manually refresh token for a specific organization.
        
        Args:
            organisation_id: Organization ID
            
        Returns:
            bool: True if refresh was successful, False otherwise
        """
        try:
            # Get source info
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = $source_type AND s.auth_type = 'oauth'
            RETURN s.id as source_id,
                   s.name as source_name,
                   s.oauth_user_email as user_email
            """
            
            result = execute_read_query(query, {
                "org_id": organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value
            })
            
            if not result:
                logger.warning("No OAuth source found for manual refresh", 
                             organisation_id=organisation_id)
                return False
            
            source_info = {
                'organisation_id': organisation_id,
                'source_id': result[0]['source_id'],
                'source_name': result[0]['source_name'],
                'user_email': result[0]['user_email']
            }
            
            return await self._refresh_source_token(source_info)
            
        except Exception as e:
            logger.error("Error in manual token refresh", error=str(e))
            return False


# Global token refresh service instance
token_refresh_service = OAuthTokenRefreshService()