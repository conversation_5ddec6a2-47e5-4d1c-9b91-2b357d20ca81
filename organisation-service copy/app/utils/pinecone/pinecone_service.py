from app.core.config import settings
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
import structlog
from datetime import datetime

from app.utils.pinecone.pinecone_client import PineconeClient
from app.db.neo4j import execute_write_query, execute_read_query
from app.utils.redis.redis_service import RedisService
from app.utils.sentence_transformers import get_sentence_transformer_service
from app.utils.llm.client import LLMClient
from app.constants.schemas import EntityRelationship

logger = structlog.get_logger()

class PineconeService:
    """
    Service for Pinecone vector database operations.
    """
    
    def __init__(self):
        self.sentence_transformer_service = get_sentence_transformer_service()
        # Get dimension from Sentence Transformers and pass to PineconeClient
        dimension = self.sentence_transformer_service.get_embedding_dimension()
        self.pinecone_client = PineconeClient(dimension=dimension)
        self.redis_service = RedisService()
        self.llm_client = LLMClient()
    
    def is_initialized(self) -> bool:
        """
        Check if the service is properly initialized.
        
        Returns:
            Boolean indicating if the service is initialized
        """
        return self.pinecone_client.is_initialized() and self.sentence_transformer_service is not None
    
    def generate_embedding(self, text: str, organisation_id: str = None, source_type: str = "google_drive") -> Optional[List[float]]:
        """
        Generate an embedding for the given text using Sentence Transformers.
        
        Args:
            text: The text to generate an embedding for
            organisation_id: The organization ID (not used with Sentence Transformers, kept for compatibility)
            source_type: The source type (not used with Sentence Transformers, kept for compatibility)
            
        Returns:
            The embedding vector (768 dimensions) or None if generation failed
        """
        try:
            # Generate embedding using Sentence Transformers
            embedding = self.sentence_transformer_service.generate_single_embedding(text)
            if embedding:
                return embedding
            else:
                logger.error("No embedding returned from Sentence Transformers")
                return None
                
        except Exception as e:
            logger.error(f"Error generating embedding with Sentence Transformers: {str(e)}")
            return None
    
    def extract_text_from_file(self, file_data: Dict[str, Any], service) -> Optional[str]:
        """
        Extract text from a Google Drive file.
        
        Args:
            file_data: The file data from Google Drive
            service: The Google Drive API service
            
        Returns:
            The extracted text or None if extraction failed
        """
        try:
            file_id = file_data['id']
            mime_type = file_data['mimeType']
            file_name = file_data['name']
            
            # Skip extraction for certain file types
            if any(mime_type.startswith(t) for t in ['image/', 'video/', 'audio/']):
                logger.info(f"Skipping text extraction for {mime_type} file: {file_name}")
                return None
            
            # Handle Google Docs, Sheets, Slides
            if mime_type in [
                'application/vnd.google-apps.document',
                'application/vnd.google-apps.spreadsheet',
                'application/vnd.google-apps.presentation'
            ]:
                export_mime_type = 'text/plain'
                request = service.files().export_media(fileId=file_id, mimeType=export_mime_type)
                content = request.execute()
                return content.decode('utf-8', errors='replace') if isinstance(content, bytes) else content
            
            # Download the file content
            request = service.files().get_media(fileId=file_id)
            content = request.execute()
            
            # Process based on mime type
            if mime_type == 'application/pdf':
                return self._extract_text_from_pdf(content, file_name)
            elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
                return self._extract_text_from_docx(content, file_name)
            elif mime_type == 'text/plain':
                return content.decode('utf-8', errors='replace') if isinstance(content, bytes) else content
            elif mime_type == 'text/csv':
                return self._extract_text_from_csv(content, file_name)
            elif mime_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']:
                return self._extract_text_from_excel(content, file_name)
            elif mime_type == 'text/html':
                return self._extract_text_from_html(content, file_name)
            elif mime_type == 'application/json':
                return self._extract_text_from_json(content, file_name)
            elif mime_type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint']:
                logger.info(f"PowerPoint parsing not fully implemented for: {file_name}")
                return f"PowerPoint content for {file_name}"
            else:
                logger.info(f"Unsupported mime type for text extraction: {mime_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from file {file_data['name']}: {str(e)}")
            return None
            
    def _extract_text_from_pdf(self, content, file_name: str) -> str:
        """Extract text from PDF file"""
        try:
            import io
            import PyPDF2
            
            file_stream = io.BytesIO(content)
            text = ""
            
            try:
                pdf_reader = PyPDF2.PdfReader(file_stream)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            except Exception as e:
                logger.error(f"Error extracting PDF content: {e}")
                
            return text
        except ImportError:
            logger.warning("PyPDF2 not installed. PDF text extraction not available.")
            return f"PDF content for {file_name} (install PyPDF2 for full support)"
            
    def _extract_text_from_docx(self, content, file_name: str) -> str:
        """Extract text from DOCX file"""
        try:
            import io
            import docx
            
            file_stream = io.BytesIO(content)
            text = ""
            
            try:
                doc = docx.Document(file_stream)
                for para in doc.paragraphs:
                    text += para.text + "\n"
            except Exception as e:
                logger.error(f"Error extracting DOCX content: {e}")
                
            return text
        except ImportError:
            logger.warning("python-docx not installed. DOCX text extraction not available.")
            return f"DOCX content for {file_name} (install python-docx for full support)"
            
    def _extract_text_from_csv(self, content, file_name: str) -> str:
        """Extract text from CSV file"""
        try:
            import io
            import csv
            
            text = ""
            content_str = content.decode('utf-8', errors='replace') if isinstance(content, bytes) else content
            
            try:
                reader = csv.reader(io.StringIO(content_str))
                for row in reader:
                    text += " | ".join(row) + "\n"
            except Exception as e:
                logger.error(f"Error extracting CSV content: {e}")
                
            return text
        except Exception as e:
            logger.error(f"Error processing CSV file: {e}")
            return f"CSV content for {file_name}"
            
    def _extract_text_from_excel(self, content, file_name: str) -> str:
        """Extract text from Excel file"""
        try:
            import io
            import pandas as pd
            
            file_stream = io.BytesIO(content)
            text = ""
            
            try:
                df = pd.read_excel(file_stream)
                text = df.to_string() + "\n"
            except Exception as e:
                logger.error(f"Error extracting Excel content: {e}")
                
            return text
        except ImportError:
            logger.warning("pandas not installed. Excel text extraction not available.")
            return f"Excel content for {file_name} (install pandas for full support)"
            
    def _extract_text_from_html(self, content, file_name: str) -> str:
        """Extract text from HTML file"""
        try:
            from bs4 import BeautifulSoup
            
            content_str = content.decode('utf-8', errors='replace') if isinstance(content, bytes) else content
            
            try:
                soup = BeautifulSoup(content_str, 'html.parser')
                text = soup.get_text(separator="\n")
            except Exception as e:
                logger.error(f"Error extracting HTML content: {e}")
                text = content_str
                
            return text
        except ImportError:
            logger.warning("BeautifulSoup not installed. HTML text extraction will be limited.")
            content_str = content.decode('utf-8', errors='replace') if isinstance(content, bytes) else content
            return content_str
            
    def _extract_text_from_json(self, content, file_name: str) -> str:
        """Extract text from JSON file"""
        try:
            import json
            
            content_str = content.decode('utf-8', errors='replace') if isinstance(content, bytes) else content
            
            try:
                json_data = json.loads(content_str)
                text = json.dumps(json_data, indent=2)
            except Exception as e:
                logger.error(f"Error extracting JSON content: {e}")
                text = content_str
                
            return text
        except Exception as e:
            logger.error(f"Error processing JSON file: {e}")
            return content_str
    
    def chunk_text(self, text: str, chunk_size: int = 3000, chunk_overlap: int = 600) -> List[str]:
        """
        Split text into overlapping chunks.
        
        Args:
            text: The text to split into chunks
            chunk_size: The size of each chunk in characters
            chunk_overlap: The overlap between chunks in characters
            
        Returns:
            List of text chunks
        """
        if not text:
            return []
        
        # Clean the text
        import re
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Split into chunks
        chunks = []
        for i in range(0, len(text), chunk_size - chunk_overlap):
            chunk = text[i:i + chunk_size]
            if chunk:
                chunks.append(chunk)
        
        return chunks
    
    def upload_file_to_pinecone(self, user_id: str, organisation_id: str, file_data: Dict[str, Any], text: str, existing_vector_id: Optional[str] = None) -> Tuple[bool, str, Optional[str]]:
        """
        Upload a file's text embedding to Pinecone.
        
        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organization
            file_data: The file data from Google Drive
            text: The extracted text from the file
            existing_vector_id: The ID of an existing vector to update (optional)
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if upload was successful
            - message: Status message
            - vector_id: The ID of the vector in Pinecone (None if failed)
        """
        if not self.is_initialized():
            return False, "Pinecone or Sentence Transformers client not initialized", None
        
        if not text:
            return False, "No text provided for embedding", None
        
        try:
            # Split text into chunks
            chunks = self.chunk_text(text)
            if not chunks:
                return False, "No valid chunks extracted from text", None
            
            # Generate a base vector ID for this file
            base_vector_id = existing_vector_id if existing_vector_id else str(uuid.uuid4())
            
            # Process each chunk
            vectors = []
            for i, chunk in enumerate(chunks):
                # Generate embedding for this chunk
                embedding = self.generate_embedding(chunk, organisation_id)
                if not embedding:
                    logger.warning(f"Failed to generate embedding for chunk {i} of file {file_data['name']}")
                    continue
                
                # Create a unique ID for this chunk
                chunk_id = f"{base_vector_id}_chunk_{i}"
                
                # Prepare metadata (only store what's needed for retrieval)
                metadata = {
                    'file_id': file_data['id'],
                    'file_name': file_data['name'],
                    'chunk_index': i,
                    'chunk_text': chunk,  # Store full text for LLM context
                    'total_chunks': len(chunks),
                    'source': 'google_drive'
                }
                
                # Add to vectors list
                vectors.append({
                    'id': chunk_id,
                    'values': embedding,
                    'metadata': metadata
                })
            
            # If we have existing vectors for this file, delete them first
            if existing_vector_id:
                try:
                    # Delete existing vectors with pattern matching
                    self.pinecone_client.index.delete(
                        filter={"file_id": file_data['id']}
                    )
                    logger.info(f"Deleted existing vectors for file {file_data['name']}")
                except Exception as e:
                    logger.warning(f"Error deleting existing vectors: {str(e)}")
            
            # Upload vectors in batches (Pinecone has limits on batch size)
            batch_size = 100
            for i in range(0, len(vectors), batch_size):
                batch = vectors[i:i+batch_size]
                self.pinecone_client.index.upsert(vectors=batch)
            
            if existing_vector_id:
                logger.info(f"Updated embeddings for file {file_data['name']} in Pinecone with base ID {base_vector_id} ({len(vectors)} chunks)")
                return True, f"File embeddings updated in Pinecone successfully ({len(vectors)} chunks)", base_vector_id
            else:
                logger.info(f"Uploaded embeddings for file {file_data['name']} to Pinecone with base ID {base_vector_id} ({len(vectors)} chunks)")
                return True, f"File embeddings uploaded to Pinecone successfully ({len(vectors)} chunks)", base_vector_id
            
        except Exception as e:
            logger.error(f"Error uploading file to Pinecone: {str(e)}")
            return False, f"Error uploading file to Pinecone: {str(e)}", None
    
    def store_chunks_in_neo4j(self, file_id: str, chunks: List[str], base_vector_id: str) -> bool:
        """
        Store chunks as nodes in Neo4j with File relationships.
        
        Args:
            file_id: The Google Drive file ID
            chunks: List of text chunks
            base_vector_id: The base vector ID from Pinecone
            
        Returns:
            Boolean indicating success
        """
        try:
            for i, chunk in enumerate(chunks):
                chunk_id = f"{base_vector_id}_chunk_{i}"
                
                # Create Chunk node and relationship to File
                query = """
                MERGE (f:GoogleDriveFile {id: $file_id})
                CREATE (c:Chunk {
                    chunk_id: $chunk_id,
                    content: $content,
                    chunk_index: $chunk_index,
                    file_id: $file_id,
                    vector_id: $vector_id,
                    created_at: datetime()
                })
                CREATE (f)-[:HAS_CHUNK]->(c)
                """
                
                params = {
                    'file_id': file_id,
                    'chunk_id': chunk_id,
                    'content': chunk,
                    'chunk_index': i,
                    'vector_id': base_vector_id
                }
                
                execute_write_query(query, params)
            
            logger.info(f"Stored {len(chunks)} chunks in Neo4j for file {file_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing chunks in Neo4j: {str(e)}")
            return False
    
    def extract_and_store_entities(self, file_id: str, chunks: List[str], base_vector_id: str) -> bool:
        """
        Extract entities and relationships from chunks and store in Neo4j using enhanced graph builder.
        Creates specific entity type nodes (e.g., e:Manager, e:Department) instead of generic Entity nodes.
        
        Args:
            file_id: The Google Drive file ID
            chunks: List of text chunks
            base_vector_id: The base vector ID from Pinecone
            
        Returns:
            Boolean indicating success
        """
        if not self.llm_client.is_available():
            logger.info("LLM client not available, using fallback extraction")
        
        try:
            # Import enhanced graph builder components
            from app.utils.knowledge_graph.graph_builder import GraphBuilder
            from app.utils.knowledge_graph.chunking_engine import DocumentChunk, ChunkMetadata, ChunkingEngine
            
            # Convert text chunks to DocumentChunk objects
            chunking_engine = ChunkingEngine()
            document_chunks = []
            chunk_relationships = []
            
            for i, chunk_text in enumerate(chunks):
                # Create DocumentChunk object with metadata
                chunk_metadata = ChunkMetadata(
                    chunk_id=f"{base_vector_id}_chunk_{i}",
                    chunk_index=i,
                    word_count=len(chunk_text.split()),
                    sentence_count=len([s for s in chunk_text.split('.') if s.strip()]),
                    start_position=i * 1000,  # Approximate based on chunk size
                    end_position=(i + 1) * 1000,
                    strategy_used="overlap_chunking"
                )
                document_chunk = DocumentChunk(text=chunk_text, metadata=chunk_metadata)
                document_chunks.append(document_chunk)
                
                # Extract relationships for this chunk using LLM client
                extraction_result = self.llm_client.extract_entities_and_relationships(chunk_text)
                
                if extraction_result:
                    relationships = extraction_result.get('relationships', [])
                    # Convert dict relationships to EntityRelationship objects if needed
                    converted_relationships = []
                    for rel in relationships:
                        if isinstance(rel, dict):
                            # Convert dict to EntityRelationship object
                            entity_rel = EntityRelationship(
                                subject=rel.get('subject', ''),
                                subject_type=rel.get('subject_type', ''),
                                predicate=rel.get('predicate', ''),
                                object=rel.get('object', ''),
                                object_type=rel.get('object_type', ''),
                                confidence_score=rel.get('confidence_score', 0.8),
                                context=rel.get('context', ''),
                                source_sentence=rel.get('source_sentence', '')
                            )
                            converted_relationships.append(entity_rel)
                        else:
                            # Already an EntityRelationship object
                            converted_relationships.append(rel)
                    chunk_relationships.append(converted_relationships)
                else:
                    chunk_relationships.append([])
            
            # Use enhanced graph builder to create specific entity type nodes
            graph_builder = GraphBuilder()
            document_result = graph_builder.build_document_graph(file_id, document_chunks, chunk_relationships)
            
            # Log results
            logger.info(f"Enhanced graph building completed for file {file_id}: "
                       f"{document_result.successful_chunks}/{document_result.total_chunks} chunks processed, "
                       f"{document_result.total_entities} entities, {document_result.total_relationships} relationships")
            
            return document_result.successful_chunks > 0
            
        except Exception as e:
            logger.error(f"Error in enhanced entity extraction and storage: {str(e)}")
            return False
    
    def _store_entities_in_neo4j(self, chunk_id: str, entities: List[Any]) -> int:
        """
        Store extracted entities in Neo4j using schema objects and constants validation.
        
        Args:
            chunk_id: The chunk ID
            entities: List of Entity schema objects or dictionaries
            
        Returns:
            Number of entities stored
        """
        from app.constants.entities import is_valid_entity_type
        from app.constants.schemas import Entity
        
        stored_count = 0
        
        for entity in entities:
            try:
                # Handle both Entity schema objects and dictionaries
                if isinstance(entity, Entity):
                    entity_name = entity.name.strip()
                    entity_type = entity.entity_type.strip()
                    entity_description = entity.description or ''
                    confidence_score = entity.confidence_score or 0.8
                else:
                    # Fallback for dictionary format
                    entity_name = entity.get('name', '').strip()
                    entity_type = entity.get('entity_type', entity.get('type', '')).strip()
                    entity_description = entity.get('description', '').strip()
                    confidence_score = entity.get('confidence_score', 0.8)
                
                if not entity_name or not entity_type:
                    continue
                
                # Validate entity type using constants
                if not is_valid_entity_type(entity_type):
                    logger.warning(f"Invalid entity type '{entity_type}' for entity '{entity_name}', skipping")
                    continue
                
                entity_id = f"{chunk_id}_{entity_name}_{entity_type}"
                
                # Create Entity node with proper labeling and relationship to Chunk
                # Use the entity type as a Neo4j label for better querying
                query = f"""
                MERGE (c:Chunk {{chunk_id: $chunk_id}})
                MERGE (e:Entity:{entity_type} {{
                    entity_id: $entity_id,
                    name: $name,
                    type: $type
                }})
                ON CREATE SET e.description = $description,
                             e.confidence_score = $confidence_score,
                             e.created_at = datetime()
                ON MATCH SET e.description = CASE
                    WHEN e.description IS NULL OR e.description = ''
                    THEN $description
                    ELSE e.description
                END,
                e.confidence_score = CASE
                    WHEN e.confidence_score IS NULL OR e.confidence_score < $confidence_score
                    THEN $confidence_score
                    ELSE e.confidence_score
                END
                MERGE (c)-[:CONTAINS_ENTITY]->(e)
                """
                
                params = {
                    'chunk_id': chunk_id,
                    'entity_id': entity_id,
                    'name': entity_name,
                    'type': entity_type,
                    'description': entity_description,
                    'confidence_score': confidence_score
                }
                
                execute_write_query(query, params)
                stored_count += 1
                
            except Exception as e:
                entity_name = getattr(entity, 'name', entity.get('name', 'unknown') if isinstance(entity, dict) else 'unknown')
                logger.error(f"Error storing entity {entity_name}: {str(e)}")
                continue
        
        return stored_count
    
    def _store_relationships_in_neo4j(self, relationships: List[Any]) -> int:
        """
        Store extracted relationships in Neo4j using schema objects and constants validation.
        
        Args:
            relationships: List of EntityRelationship schema objects or dictionaries
            
        Returns:
            Number of relationships stored
        """
        from app.constants.relationships import is_valid_relationship_type
        from app.constants.schemas import EntityRelationship
        
        stored_count = 0
        
        for relationship in relationships:
            try:
                # Handle both EntityRelationship schema objects and dictionaries
                if isinstance(relationship, EntityRelationship):
                    source_name = relationship.subject.strip()
                    target_name = relationship.object.strip()
                    rel_type = relationship.predicate.strip()
                    rel_description = relationship.context or ''
                    confidence_score = relationship.confidence_score or 0.8
                    source_sentence = relationship.source_sentence or ''
                    subject_type = relationship.subject_type or ''
                    object_type = relationship.object_type or ''
                else:
                    # Fallback for dictionary format
                    source_name = relationship.get('subject', relationship.get('source', '')).strip()
                    target_name = relationship.get('object', relationship.get('target', '')).strip()
                    rel_type = relationship.get('predicate', relationship.get('type', '')).strip()
                    rel_description = relationship.get('context', relationship.get('description', '')).strip()
                    confidence_score = relationship.get('confidence_score', 0.8)
                    source_sentence = relationship.get('source_sentence', '').strip()
                    subject_type = relationship.get('subject_type', '').strip()
                    object_type = relationship.get('object_type', '').strip()
                
                if not source_name or not target_name or not rel_type:
                    continue
                
                # Validate relationship type using constants
                if not is_valid_relationship_type(rel_type):
                    logger.warning(f"Invalid relationship type '{rel_type}' for relationship '{source_name} -> {target_name}', skipping")
                    continue
                
                # Create relationship between entities using the actual relationship type as the Neo4j relationship type
                # This allows for better querying and follows Neo4j best practices
                query = f"""
                MATCH (e1:Entity {{name: $source_name}})
                MATCH (e2:Entity {{name: $target_name}})
                MERGE (e1)-[r:{rel_type}]->(e2)
                ON CREATE SET r.description = $description,
                             r.confidence_score = $confidence_score,
                             r.source_sentence = $source_sentence,
                             r.subject_type = $subject_type,
                             r.object_type = $object_type,
                             r.created_at = datetime()
                ON MATCH SET r.description = CASE
                    WHEN r.description IS NULL OR r.description = ''
                    THEN $description
                    ELSE r.description
                END,
                r.confidence_score = CASE
                    WHEN r.confidence_score IS NULL OR r.confidence_score < $confidence_score
                    THEN $confidence_score
                    ELSE r.confidence_score
                END,
                r.source_sentence = CASE
                    WHEN r.source_sentence IS NULL OR r.source_sentence = ''
                    THEN $source_sentence
                    ELSE r.source_sentence
                END
                """
                
                params = {
                    'source_name': source_name,
                    'target_name': target_name,
                    'description': rel_description,
                    'confidence_score': confidence_score,
                    'source_sentence': source_sentence,
                    'subject_type': subject_type,
                    'object_type': object_type
                }
                
                result = execute_write_query(query, params)
                if result:
                    stored_count += 1
                
            except Exception as e:
                source_name = getattr(relationship, 'subject', relationship.get('subject', relationship.get('source', 'unknown')) if isinstance(relationship, dict) else 'unknown')
                target_name = getattr(relationship, 'object', relationship.get('object', relationship.get('target', 'unknown')) if isinstance(relationship, dict) else 'unknown')
                logger.error(f"Error storing relationship {source_name} -> {target_name}: {str(e)}")
                continue
        
        return stored_count
    
    def enhanced_upload_file_to_pinecone(self, user_id: str, organisation_id: str, file_data: Dict[str, Any], text: str, existing_vector_id: Optional[str] = None) -> Tuple[bool, str, Optional[str]]:
        """
        Enhanced upload with dual storage (Pinecone + Neo4j) and entity extraction.
        
        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organization
            file_data: The file data from Google Drive
            text: The extracted text from the file
            existing_vector_id: The ID of an existing vector to update (optional)
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if upload was successful
            - message: Status message
            - vector_id: The ID of the vector in Pinecone (None if failed)
        """
        try:
            # 1. Existing Pinecone upload (unchanged)
            success, message, vector_id = self.upload_file_to_pinecone(
                user_id, organisation_id, file_data, text, existing_vector_id
            )
            
            if not success:
                return success, message, vector_id
            
            # 2. Store chunks in Neo4j
            chunks = self.chunk_text(text)
            if chunks:
                # chunk_success = self.store_chunks_in_neo4j(file_data['id'], chunks, vector_id)
                # if not chunk_success:
                #     logger.warning(f"Failed to store chunks in Neo4j for file {file_data['id']}")
                
                # 3. Extract entities and relationships
                entity_success = self.extract_and_store_entities(file_data['id'], chunks, vector_id)
                if not entity_success:
                    logger.warning(f"Failed to extract entities for file {file_data['id']}")
            
            return success, f"{message} (Enhanced with graph storage)", vector_id
            
        except Exception as e:
            logger.error(f"Error in enhanced upload: {str(e)}")
            return False, f"Error in enhanced upload: {str(e)}", None
    
    def update_neo4j_with_vector_id(self, user_id: str, file_id: str, vector_id: str) -> bool:
        """
        Update the Neo4j knowledge graph with the Pinecone vector ID.
        
        Args:
            user_id: The ID of the user
            file_id: The ID of the file in Google Drive
            vector_id: The ID of the vector in Pinecone
            
        Returns:
            Boolean indicating if the update was successful
        """
        try:
            query = """
            MATCH (u:User {id: $user_id})-[:HAS_ACCESS]->(f:GoogleDriveFile {id: $file_id})
            SET f.vector_id = $vector_id,
                f.vectorized_at = $vectorized_at
            RETURN f
            """
            
            params = {
                'user_id': user_id,
                'file_id': file_id,
                'vector_id': vector_id,
                'vectorized_at': datetime.utcnow().isoformat()
            }
            
            result = execute_write_query(query, params)
            
            if result:
                logger.info(f"Updated Neo4j with vector ID {vector_id} for file {file_id}")
                # Invalidate the cache for this user since file access might have changed
                self.invalidate_access_cache(user_id)
                return True
            else:
                logger.warning(f"File {file_id} not found in Neo4j for user {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating Neo4j with vector ID: {str(e)}")
            return False
    
    def get_search_file_ids(self, user_id: str, agent_id: Optional[str] = None, organisation_id:str = None, file_ids: Optional[List[str]] = None) -> List[str]:
        """
        Get the list of file IDs to search based on access control.
        This logic is extracted from search_similar_documents for reuse.
        
        Args:
            user_id: The ID of the user
            agent_id: Optional ID of the agent for department filtering
            file_ids: Optional list of specific file IDs to search within
            
        Returns:
            List of file IDs that the user has access to
        """
        try:
            # Determine which files to search
            if file_ids:
                # If specific file_ids are provided, use them directly
                search_file_ids = file_ids
                logger.info(f"Using provided file_ids for search: {len(search_file_ids)} files")
            else:
                # Get files accessible by this user using the comprehensive method
                logger.info(f"Getting accessible file IDs for user {user_id} in organisation {organisation_id}")
                if not organisation_id:
                    logger.warning("Organisation ID is required for access control")
                    return []
                search_file_ids = self.get_accessible_file_ids(user_id, organisation_id)
                print("search file ids user", search_file_ids)
                if not search_file_ids:
                    return []
                    
                # If agent_id is provided, filter files by department access
                
                if agent_id:
                    print("search file ids agent", agent_id)
                    department_file_ids = self.get_department_accessible_file_ids(agent_id, organisation_id)
                    print("search file ids agent", department_file_ids)
                    if department_file_ids:
                        print("search file ids agent", department_file_ids)
                        # Only include files that both the user and the department have access to
                        search_file_ids = [file_id for file_id in search_file_ids if file_id in department_file_ids]
            
            return search_file_ids
            
        except Exception as e:
            logger.error(f"Error getting search file IDs: {str(e)}")
            return []
    
    def vector_search_only(self, query_text: str, file_ids: List[str], top_k: int = 5) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Perform vector search only without hybrid enhancement.
        Used internally by HybridSearchEngine to avoid circular dependency.
        
        Args:
            query_text: The query text to search for
            file_ids: List of file IDs to search within (access control already applied)
            top_k: Number of results to return
            
        Returns:
            Tuple containing success, message, and vector search results
        """
        if not self.is_initialized():
            return False, "Pinecone or Sentence Transformers client not initialized", []
        
        if not file_ids:
            return False, "No files provided for search", []
        
        try:
            # Generate embedding for the query
            query_embedding = self.generate_embedding(query_text)
            if not query_embedding:
                return False, "Failed to generate embedding for query", []
            
            # Create filter for file IDs
            filter_dict = {"file_id": {"$in": file_ids}}
            
            # Query Pinecone
            query_response = self.pinecone_client.index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True,
                filter=filter_dict
            )
            
            if not query_response.matches:
                return False, "No matching documents found", []
            
            # Process results
            results = []
            for match in query_response.matches:
                metadata = match.metadata
                
                # Get file details from Neo4j
                file_query = """
                MATCH (f:GoogleDriveFile {id: $file_id})
                RETURN f.name as file_name, f.mime_type as mime_type,
                       f.web_view_link as web_view_link, f.created_at as created_time,
                       f.modified_time as modified_time
                """
                file_results = execute_read_query(file_query, {'file_id': metadata['file_id']})
                
                if file_results:
                    file_info = file_results[0]
                    results.append({
                        'file_id': metadata['file_id'],
                        'file_name': file_info.get('file_name', metadata.get('file_name', '')),
                        'mime_type': file_info.get('mime_type', metadata.get('mime_type', '')),
                        'web_view_link': file_info.get('web_view_link', ''),
                        'created_time': file_info.get('created_time', ''),
                        'modified_time': file_info.get('modified_time', ''),
                        'score': float(match.score),
                        'vector_id': match.id,
                        'chunk_text': metadata.get('chunk_text', ''),
                        'search_type': 'vector_only'
                    })
            
            logger.info(f"Vector search found {len(results)} results for {len(file_ids)} files")
            return True, f"Found {len(results)} documents using vector search", results
            
        except Exception as e:
            logger.error(f"Error in vector search: {str(e)}")
            return False, f"Error in vector search: {str(e)}", []

    def search_similar_documents(self, user_id: str, query_text: str, top_k: int = 5, agent_id: Optional[str] = None, organisation_id: str = None, file_ids: Optional[List[str]] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Search for documents similar to the query text using hybrid search (vector + graph).
        
        Args:
            user_id: The ID of the user
            query_text: The query text to search for
            top_k: The number of results to return - returns up to this many results
            agent_id: Optional ID of the agent. If provided, only return files accessible to both user and agent's department
            organisation_id: ID of the organization (mandatory but not used currently)
            file_ids: Optional list of specific file IDs to search within. If provided, search is limited to these files only.
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if search was successful
            - message: Status message
            - results: List of search results, sorted by relevance score
        """
        if not self.is_initialized():
            return False, "Pinecone or Sentence Transformers client not initialized", []
        
        try:
            # Get accessible file IDs using the extracted method
            search_file_ids = self.get_search_file_ids(user_id, agent_id, organisation_id, file_ids)
            
            if not search_file_ids:
                return False, "No files found for this user", []
            
            logger.info(f"Performing vector search for {len(search_file_ids)} accessible files")
            
            # Use vector search directly (this is what Pinecone service is designed for)
            return self.vector_search_only(
                query_text=query_text,
                file_ids=search_file_ids,
                top_k=top_k
            )
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {str(e)}")
            return False, f"Error in hybrid search: {str(e)}", []
            
    def get_accessible_file_ids(self, user_id: str, organisation_id:str) -> List[str]:
        """
        Get all file IDs that a user has access to, either directly or through folder hierarchy.
        Uses Redis cache to improve performance.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            List of file IDs the user has access to
        """
        # Try to get from cache first
        # cache_key = f"user_accessible_files:{user_id}"
        # cached_ids = self.redis_service.get(cache_key)
        
        # if cached_ids:
        #     try:
        #         return json.loads(cached_ids)
        #     except json.JSONDecodeError:
        #         logger.warning(f"Invalid JSON in cache for user {user_id}, fetching fresh data")
        
        # Query for direct access
        direct_query = """
        MATCH (u:User {id: $user_id})-[:HAS_ACCESS]->(f:GoogleDriveFile)
        MATCH (u)-[:BELONGS_TO]->(d:Department {name: "GENERAL"})
        WHERE f.organisation_id = d.organisation_id AND f.organisation_id = $organisation_id
        RETURN f.id as file_id
        """
        
        # Query for access through parent folders
        folder_query = """
        MATCH (u:User {id: $user_id})-[:HAS_ACCESS]->(folder:GoogleDriveFolder)
        MATCH (folder)-[:CONTAINS*1..]->(f:GoogleDriveFile)
        MATCH (u)-[:BELONGS_TO]->(d:Department {name: "GENERAL"})
        WHERE f.organisation_id = d.organisation_id AND f.organisation_id = $organisation_id
        RETURN DISTINCT f.id as file_id
        """
        
        # Execute both queries
        direct_result = execute_read_query(direct_query, {'user_id': user_id, 'organisation_id': organisation_id})
        folder_result = execute_read_query(folder_query, {'user_id': user_id, 'organisation_id': organisation_id})
        
        # Combine results
        file_ids = set()
        if direct_result:
            file_ids.update(record['file_id'] for record in direct_result)
        if folder_result:
            file_ids.update(record['file_id'] for record in folder_result)
        
        # Store in cache with 15-minute expiration
        file_ids_list = list(file_ids)
        # try:
        #     self.redis_service.set(cache_key, json.dumps(file_ids_list), ex=900)
        # except Exception as e:
        #     logger.error(f"Error caching file IDs for user {user_id}: {str(e)}")
        
        return file_ids_list
    
    def get_department_accessible_file_ids(self, agent_id: str, organisation_id:str) -> List[str]:
        """
        Get all file IDs that a department (which the agent belongs to) has access to,
        either directly or through folder hierarchy.
        Uses Redis cache to improve performance.
        
        Args:
            agent_id: The ID of the agent
            
        Returns:
            List of file IDs the department has access to
        """
        # Try to get from cache first
        cache_key = f"department_accessible_files:{agent_id}"
        cached_ids = None #self.redis_service.get(cache_key)
        
        if cached_ids:
            try:
                return json.loads(cached_ids)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in cache for agent {agent_id}, fetching fresh data")
        
        # Get the department ID for this agent
        dept_query = """
        MATCH (a:Agent {id: $agent_id})-[:BELONGS_TO]->(d:Department)
        RETURN d.id as department_id
        """
        
        dept_params = {
            "agent_id": agent_id
        }
        
        dept_result = execute_read_query(dept_query, dept_params)
        
        if not dept_result or not dept_result[0].get('department_id'):
            logger.warning(f"No department found for agent {agent_id}")
            return []
            
        department_id = dept_result[0]['department_id']
        
        # Query for direct access
        direct_query = """
        MATCH (d:Department {id: $dept_id})-[:HAS_ACCESS]->(f:GoogleDriveFile)
        WHERE f.organisation_id = d.organisation_id AND f.organisation_id = $organisation_id
        AND (
            f.agent_id = $agent_id
            OR f.agent_id IS NULL
        )
        RETURN f.id as file_id
        """
        
        # Query for access through parent folders
        folder_query = """
        MATCH (d:Department {id: $dept_id})-[:HAS_ACCESS]->(folder:GoogleDriveFolder)
        MATCH (folder)-[:CONTAINS*1..]->(f:GoogleDriveFile)
        WHERE f.organisation_id = d.organisation_id AND f.organisation_id = $organisation_id
        AND (
            f.agent_id = $agent_id
            OR f.agent_id IS NULL
        )
        RETURN DISTINCT f.id as file_id
        """
        
        # Execute both queries
        direct_result = execute_read_query(direct_query, 
                                           {'dept_id': department_id, 
                                            'agent_id': agent_id, 
                                            'organisation_id': organisation_id})
        folder_result = execute_read_query(folder_query, 
                                           {'dept_id': department_id, 
                                            'agent_id': agent_id, 
                                            'organisation_id': organisation_id})
        
        # Combine results
        file_ids = set()
        if direct_result:
            file_ids.update(record['file_id'] for record in direct_result)
        if folder_result:
            file_ids.update(record['file_id'] for record in folder_result)
        
        # Store in cache with 15-minute expiration
        file_ids_list = list(file_ids)
        try:
            self.redis_service.set(cache_key, json.dumps(file_ids_list), ex=900)
        except Exception as e:
            logger.error(f"Error caching file IDs for agent {agent_id}: {str(e)}")
        
        return file_ids_list
    
    def invalidate_access_cache(self, user_id: str) -> None:
        """
        Invalidate the access cache for a user when permissions change.
        
        Args:
            user_id: The ID of the user
        """
        cache_key = f"user_accessible_files:{user_id}"
        try:
            self.redis_service.delete(cache_key)
            logger.info(f"Invalidated access cache for user {user_id}")
        except Exception as e:
            logger.error(f"Error invalidating access cache for user {user_id}: {str(e)}")
    
    def invalidate_department_access_cache(self, department_id: str) -> None:
        """
        Invalidate the access cache for all agents in a department when permissions change.
        
        Args:
            department_id: The ID of the department
        """
        try:
            # Find all agents in this department
            agents_query = """
            MATCH (a:Agent)-[:BELONGS_TO]->(d:Department {id: $dept_id})
            RETURN a.id as agent_id
            """
            
            agents_result = execute_read_query(agents_query, {'dept_id': department_id})
            
            if agents_result:
                for record in agents_result:
                    agent_id = record['agent_id']
                    cache_key = f"department_accessible_files:{agent_id}"
                    try:
                        self.redis_service.delete(cache_key)
                        logger.info(f"Invalidated access cache for agent {agent_id} in department {department_id}")
                    except Exception as e:
                        logger.error(f"Error invalidating cache for agent {agent_id}: {str(e)}")
            
            logger.info(f"Invalidated access caches for all agents in department {department_id}")
        except Exception as e:
            logger.error(f"Error invalidating department access caches: {str(e)}")
    
    def assemble_context(self, results: List[Dict[str, Any]]) -> str:
        """
        Assemble the retrieved chunks into a coherent context.
        
        Args:
            results: The search results
            
        Returns:
            A string containing the assembled context
        """
        context = ""
        
        # Sort results by score (highest first)
        sorted_results = sorted(results, key=lambda x: x['score'], reverse=True)
        
        for result in sorted_results:
            document_title = result.get('file_name', 'Unknown')
            text = result.get('chunk_text', '')
            
            context += f"Document: {document_title}\n"
            context += f"Text: {text}\n\n"
        
        return context
        
    def batch_search_similar_documents(self, user_id: str, query_texts: List[str], top_k: int = 5, agent_id: Optional[str] = None, organisation_id: str = None, file_ids: Optional[List[str]] = None) -> Tuple[bool, str, List[List[Dict[str, Any]]]]:
        """
        Batch search for documents similar to multiple query texts.
        This is more efficient than calling search_similar_documents multiple times.
        
        Args:
            user_id: The ID of the user
            query_texts: List of query texts to search for
            top_k: The number of results to return for each query
            agent_id: Optional ID of the agent. If provided, only return files accessible to both user and agent's department
            organisation_id: ID of the organization (mandatory but not used currently)
            file_ids: Optional list of specific file IDs to search within. If provided, search is limited to these files only.
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if search was successful
            - message: Status message
            - results: List of search results for each query, where each item is a list of results
        """
        if not self.is_initialized():
            return False, "Pinecone or Sentence Transformers client not initialized", []
        
        if not query_texts:
            return False, "No query texts provided", []
        
        try:
            # Generate embeddings for all queries in batch
            batch_embeddings = []
            for query_text in query_texts:
                embedding = self.generate_embedding(query_text, organisation_id)
                if not embedding:
                    logger.warning(f"Failed to generate embedding for query: {query_text[:50]}...")
                    # Add None to maintain index alignment with original queries
                    batch_embeddings.append(None)
                else:
                    batch_embeddings.append(embedding)
            
            # If all embeddings failed, return error
            if all(embedding is None for embedding in batch_embeddings):
                return False, "Failed to generate embeddings for all queries", []
            
            # Determine which files to search
            if file_ids:
                # If specific file_ids are provided, use them directly
                search_file_ids = file_ids
                logger.info(f"Using provided file_ids for batch search: {len(search_file_ids)} files")
            else:
                # Get files accessible by this user using the comprehensive method
                search_file_ids = self.get_accessible_file_ids(user_id, organisation_id)
                
                if not search_file_ids:
                    return False, "No files found for this user", []
                    
                # If agent_id is provided, filter files by department access
                if agent_id:
                    department_file_ids = self.get_department_accessible_file_ids(agent_id, organisation_id)
                    if department_file_ids:
                        # Only include files that both the user and the department have access to
                        search_file_ids = [file_id for file_id in search_file_ids if file_id in department_file_ids]
                        
                        if not search_file_ids:
                            return False, "No files found with both user and department access", []
            
            # Prepare results container for each query
            all_results = []
            
            # Process each query embedding
            for i, embedding in enumerate(batch_embeddings):
                # Skip queries where embedding generation failed
                if embedding is None:
                    all_results.append([])
                    continue
                
                # Search Pinecone with filter for these file IDs
                search_results = self.pinecone_client.index.query(
                    vector=embedding,
                    top_k=top_k * 3,  # Get more results to account for potential duplicates
                    include_metadata=True,
                    filter={"file_id": {"$in": search_file_ids}}
                )
                
                # Process results without grouping by file_id
                results = []
                for match in search_results.matches:
                    file_id = match.metadata['file_id']
                    
                    # Get file details from Neo4j
                    file_query = """
                    MATCH (f:GoogleDriveFile {id: $file_id})
                    RETURN f.name as name, f.mime_type as mime_type,
                           f.created_at as created_time, f.modified_time as modified_time,
                           f.web_view_link as web_view_link
                    """
                    file_details = execute_read_query(file_query, {'file_id': file_id})
                    
                    if file_details:
                        file_details = file_details[0]
                        results.append({
                            'score': match.score,
                            'vector_id': match.id.split('_chunk_')[0],  # Get base vector ID
                            'file_id': file_id,
                            'file_name': match.metadata['file_name'],
                            'mime_type': match.metadata['mime_type'],
                            'chunk_text': match.metadata.get('chunk_text', ''),
                            'created_time': file_details.get('created_time', ''),
                            'modified_time': file_details.get('modified_time', ''),
                            'web_view_link': file_details.get('web_view_link', '')
                        })
                
                # Sort by score and limit to requested number of results
                results.sort(key=lambda x: x['score'], reverse=True)
                results = results[:top_k]
                
                # Add to all results
                all_results.append(results)
            
            return True, f"Processed {len(query_texts)} queries with {sum(len(r) for r in all_results)} total results", all_results
            
        except Exception as e:
            logger.error(f"Error in batch search: {str(e)}")
            return False, f"Error in batch search: {str(e)}", []