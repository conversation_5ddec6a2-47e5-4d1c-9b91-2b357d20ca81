import structlog
from app.db.neo4j import execute_read_query, execute_write_query
from typing import Optional, List, Dict, Any

logger = structlog.get_logger()

def check_user_access(user_id: str, resource_id: str, resource_type: str = None) -> bool:
    """
    Check if a user has access to a specific resource (agent, file, or folder).
    
    Args:
        user_id: The ID of the user
        resource_id: The ID of the resource (agent, file, or folder)
        resource_type: Optional type of resource ('agent', 'file', 'folder'). If not provided, 
                      the function will attempt to determine the type.
        
    Returns:
        <PERSON><PERSON><PERSON> indicating if the user has access
    """
    # If resource_type is not provided, determine it
    if not resource_type:
        type_query = """
        MATCH (r)
        WHERE r.id = $resource_id
        RETURN labels(r) as types
        """
        
        type_params = {
            'resource_id': resource_id
        }
        
        type_result = execute_read_query(type_query, type_params)
        
        if not type_result:
            logger.error(f"Resource with ID {resource_id} not found")
            return False
        
        types = type_result[0]['types']
        
        if 'Agent' in types:
            resource_type = 'agent'
        elif 'GoogleDriveFile' in types:
            resource_type = 'file'
        elif 'GoogleDriveFolder' in types:
            resource_type = 'folder'
        else:
            logger.error(f"Unknown resource type for ID {resource_id}: {types}")
            return False
    
    # Check access based on resource type
    if resource_type.lower() == 'agent':
        return check_agent_access(user_id, resource_id)
    elif resource_type.lower() in ['file', 'folder']:
        return check_file_folder_access(user_id, resource_id)
    else:
        logger.error(f"Unsupported resource type: {resource_type}")
        return False

def check_agent_access(user_id: str, agent_id: str) -> bool:
    """
    Check if a user has access to a specific agent.
    
    Args:
        user_id: The ID of the user
        agent_id: The ID of the agent
        
    Returns:
        Boolean indicating if the user has access
    """
    query = """
    MATCH (u:User {id: $user_id}), (a:Agent {id: $agent_id})
    RETURN 
        EXISTS((u)-[:OWNS]->(a)) as is_owner,
        EXISTS((u)-[:HAS_ACCESS]->(a)) as has_access
    """
    
    params = {
        "user_id": user_id,
        "agent_id": agent_id
    }
    
    result = execute_read_query(query, params)
    
    if not result:
        logger.error("Failed to check user access to agent")
        return False
    
    is_owner = result[0]['is_owner']
    has_access = result[0]['has_access']
    
    # User has access if they are the owner or have been granted access
    return is_owner or has_access

def check_file_folder_access(user_id: str, resource_id: str) -> bool:
    """
    Check if a user has access to a file or folder.
    Recursively checks parent folders if direct access is not found.
    Also checks access through department membership.
    
    Args:
        user_id: The ID of the user
        resource_id: The ID of the file or folder
        
    Returns:
        Boolean indicating if the user has access
    """
    # First check direct access
    direct_access_query = """
    MATCH (u:User {id: $user_id})-[:HAS_ACCESS]->(f)
    WHERE (f:GoogleDriveFile OR f:GoogleDriveFolder) AND f.id = $resource_id
    RETURN COUNT(f) > 0 as has_access
    """
    
    params = {
        'user_id': user_id,
        'resource_id': resource_id
    }
    
    result = execute_read_query(direct_access_query, params)
    
    if result and result[0]['has_access']:
        return True
    
    # Check access through department
    dept_access_query = """
    MATCH (u:User {id: $user_id})-[:BELONGS_TO]->(d:Department)-[:HAS_ACCESS]->(f)
    WHERE (f:GoogleDriveFile OR f:GoogleDriveFolder) AND f.id = $resource_id
    RETURN COUNT(f) > 0 as has_access
    """
    
    dept_result = execute_read_query(dept_access_query, params)
    
    if dept_result and dept_result[0]['has_access']:
        return True
    
    # If no direct or department access, check parent folders
    parent_query = """
    MATCH (f)
    WHERE (f:GoogleDriveFile OR f:GoogleDriveFolder) AND f.id = $resource_id
    MATCH (parent:GoogleDriveFolder)-[:CONTAINS]->(f)
    RETURN parent.id as parent_id
    """
    
    parent_result = execute_read_query(parent_query, params)
    
    if parent_result:
        for record in parent_result:
            parent_id = record['parent_id']
            if check_file_folder_access(user_id, parent_id):
                return True
    
    return False