# default_departments.py
# Configuration file for default organization departments
from enum import Enum

class DefaultRoles(Enum):
    MEMBER = "MEMBER"
    ADMIN = "ADMIN"

class Visibility(Enum):
    PUBLIC = "PUBLIC"
    PRIVATE = "PRIVATE"

class DefaultPermissions(Enum):
    UNIVERSAL = "UNIVERSAL"
    READ = "READ"

class DefaultDepartments(Enum):
    GENERAL = "GENERAL"
    HR = "HR"
    SALES = "SALES"
    MARKETING = "MARKETING"
    FINANCE = "FINANCE"

DEFAULT_DEPARTMENT_CONFIG = {
    "GENERAL": {"description": "General department for miscellaneous organization activities"},
    "HR": {"description": "Human Resources department for personnel management"},
    "SALES": {"description": "Sales department for managing customer relationships and revenue"},
    "MARKETING": {"description": "Marketing department for brand and promotional activities"},
    "FINANCE": {"description": "Finance department for budget and financial management"}
}

# DefaultDepartments = Enum('DefaultDepartments', {k.upper(): k for k in DEFAULT_DEPARTMENT_CONFIG.keys()})