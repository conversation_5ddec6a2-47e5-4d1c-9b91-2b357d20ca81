"""
Search Strategies for Enterprise KG Hybrid Search

This module provides different search strategies that can be used by the hybrid search engine
to retrieve and rank information from the knowledge graph.
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from .search_schemas import (
    SearchQuery, 
    SearchResult, 
    GraphContext, 
    EntityMatch, 
    RelationshipMatch,
    SearchStrategy,
    SearchMetrics
)
from .graph_rag import GraphRAG
from ...constants.entities import get_entity_category_mapping, get_entity_properties
from ...constants.relationships import get_relationship_category_mapping

logger = logging.getLogger(__name__)


class BaseSearchStrategy(ABC):
    """
    Abstract base class for search strategies.
    """
    
    def __init__(self, graph_rag: GraphRAG):
        """
        Initialize the search strategy.
        
        Args:
            graph_rag: GraphRAG instance for graph operations
        """
        self.graph_rag = graph_rag
        self.entity_categories = get_entity_category_mapping()
        self.relationship_categories = get_relationship_category_mapping()
    
    @abstractmethod
    def execute_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute the search strategy.
        
        Args:
            query: Search query to execute
            
        Returns:
            SearchResult with findings
        """
        pass
    
    def _calculate_coverage_score(self, graph_context: GraphContext, query: SearchQuery) -> float:
        """Calculate how well the results cover the query intent."""
        if not graph_context.entities:
            return 0.0
        
        # Base coverage from entity diversity
        entity_type_diversity = len(graph_context.entity_types_found) / max(1, len(self.graph_rag.entity_types))
        relationship_type_diversity = len(graph_context.relationship_types_found) / max(1, len(self.graph_rag.relationship_types))
        
        # Boost for high-importance entities
        high_importance_entities = sum(1 for e in graph_context.entities if e.properties.get("graph_importance", 0) > 0.7)
        importance_boost = min(0.3, high_importance_entities / max(1, len(graph_context.entities)))
        
        return min(1.0, (entity_type_diversity + relationship_type_diversity) / 2 + importance_boost)
    
    def _calculate_coherence_score(self, graph_context: GraphContext) -> float:
        """Calculate how connected and coherent the results are."""
        if not graph_context.entities or not graph_context.relationships:
            return 0.0
        
        # Calculate connectivity ratio
        max_possible_connections = len(graph_context.entities) * (len(graph_context.entities) - 1)
        if max_possible_connections == 0:
            return 0.0
        
        actual_connections = len(graph_context.relationships) * 2  # Each relationship connects 2 entities
        connectivity_ratio = min(1.0, actual_connections / max_possible_connections)
        
        # Boost for entities with multiple relationships
        well_connected_entities = sum(1 for e in graph_context.entities if e.relationship_count > 2)
        connection_boost = min(0.3, well_connected_entities / max(1, len(graph_context.entities)))
        
        return min(1.0, connectivity_ratio + connection_boost)


class EntityCentricStrategy(BaseSearchStrategy):
    """
    Strategy that focuses on finding and expanding around key entities.
    """
    
    def execute_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute entity-centric search.
        
        Focus on identifying important entities and expanding their context.
        """
        start_time = datetime.now()
        
        try:
            #print("Start entity centric search")
            # Extract graph context with focus on high-importance entities
            graph_context, metrics = self.graph_rag.extract_graph_context(query.chunk_indices, query)
            #print(" entity centric search completed graph context ", graph_context)
            #print(" entity centric search completed metrics ", metrics)
            
            # Boost scores for high-importance entities
            self._boost_entity_importance(graph_context.entities)
            #print("boosted entity performance")            
            # Calculate quality scores
            coverage_score = self._calculate_coverage_score(graph_context, query)
            #print("print coverage score", coverage_score)
            coherence_score = self._calculate_coherence_score(graph_context)
            #print("print coherence score", coherence_score)
            relevance_score = self._calculate_entity_relevance_score(graph_context, query)
            #print("print relevance score", relevance_score)
            
            # Create result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000
            
            result = SearchResult(
                query=query,
                graph_context=graph_context,
                total_results=len(graph_context.entities),
                processing_time_ms=processing_time,
                strategy_used=SearchStrategy.ENTITY_CENTRIC,
                coverage_score=coverage_score,
                coherence_score=coherence_score,
                relevance_score=relevance_score,
                debug_info={
                    "strategy": "entity_centric",
                    "entity_boost_applied": True,
                    "metrics": metrics.__dict__
                }
            )
            #print("Entity centric result created")            
            logger.info(f"Entity-centric search completed: {len(graph_context.entities)} entities, "
                       f"{len(graph_context.relationships)} relationships in {processing_time:.2f}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in entity-centric search: {e}")
            return self._create_error_result(query, str(e), start_time)
    
    def _boost_entity_importance(self, entities: List[EntityMatch]):
        """Boost relevance scores for high-importance entities."""
        for entity in entities:
            importance = entity.properties.get("graph_importance", 0.5)
            if importance > 0.7:
                entity.relevance_score = min(1.0, entity.relevance_score * 1.3)
            elif importance > 0.8:
                entity.relevance_score = min(1.0, entity.relevance_score * 1.5)
    
    def _calculate_entity_relevance_score(self, graph_context: GraphContext, query: SearchQuery) -> float:
        """Calculate relevance score based on entity importance and types."""
        if not graph_context.entities:
            return 0.0
        
        # Average entity relevance (handle None values)
        avg_relevance = sum(e.relevance_score or 0.0 for e in graph_context.entities) / len(graph_context.entities)
        
        # Boost for requested entity types
        if query.entity_types:
            matching_entities = sum(1 for e in graph_context.entities if e.entity_type in query.entity_types)
            type_match_boost = min(0.3, matching_entities / len(graph_context.entities))
            avg_relevance = min(1.0, avg_relevance + type_match_boost)
        
        return avg_relevance
    
    def _create_error_result(self, query: SearchQuery, error: str, start_time: datetime) -> SearchResult:
        """Create an error result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000
        
        return SearchResult(
            query=query,
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.ENTITY_CENTRIC,
            debug_info={"error": error}
        )


class RelationshipCentricStrategy(BaseSearchStrategy):
    """
    Strategy that focuses on finding and analyzing relationships between entities.
    """
    
    def execute_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute relationship-centric search.
        
        Focus on identifying important relationships and their context.
        """
        start_time = datetime.now()
        
        try:
            # Extract graph context with focus on relationships
            graph_context, metrics = self.graph_rag.extract_graph_context(query.chunk_indices, query)
            
            # Boost scores for high-confidence relationships
            self._boost_relationship_confidence(graph_context.relationships)
            
            # Calculate quality scores
            coverage_score = self._calculate_coverage_score(graph_context, query)
            coherence_score = self._calculate_coherence_score(graph_context)
            relevance_score = self._calculate_relationship_relevance_score(graph_context, query)
            
            # Create result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000
            
            result = SearchResult(
                query=query,
                graph_context=graph_context,
                total_results=len(graph_context.relationships),
                processing_time_ms=processing_time,
                strategy_used=SearchStrategy.RELATIONSHIP_CENTRIC,
                coverage_score=coverage_score,
                coherence_score=coherence_score,
                relevance_score=relevance_score,
                debug_info={
                    "strategy": "relationship_centric",
                    "relationship_boost_applied": True,
                    "metrics": metrics.__dict__
                }
            )
            
            logger.info(f"Relationship-centric search completed: {len(graph_context.entities)} entities, "
                       f"{len(graph_context.relationships)} relationships in {processing_time:.2f}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in relationship-centric search: {e}")
            return self._create_error_result(query, str(e), start_time)
    
    def _boost_relationship_confidence(self, relationships: List[RelationshipMatch]):
        """Boost relevance scores for high-confidence relationships."""
        for relationship in relationships:
            if relationship.confidence_score > 0.7:
                relationship.relevance_score = min(1.0, relationship.relevance_score * 1.2)
            elif relationship.confidence_score > 0.8:
                relationship.relevance_score = min(1.0, relationship.relevance_score * 1.4)
    
    def _calculate_relationship_relevance_score(self, graph_context: GraphContext, query: SearchQuery) -> float:
        """Calculate relevance score based on relationship quality and types."""
        if not graph_context.relationships:
            return 0.0
        
        # Average relationship relevance (handle None values)
        avg_relevance = sum(r.relevance_score or 0.0 for r in graph_context.relationships) / len(graph_context.relationships)
        
        # Boost for requested relationship types
        if query.relationship_types:
            matching_relationships = sum(1 for r in graph_context.relationships if r.relationship_type in query.relationship_types)
            type_match_boost = min(0.3, matching_relationships / len(graph_context.relationships))
            avg_relevance = min(1.0, avg_relevance + type_match_boost)
        
        return avg_relevance
    
    def _create_error_result(self, query: SearchQuery, error: str, start_time: datetime) -> SearchResult:
        """Create an error result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        return SearchResult(
            query=query,
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.RELATIONSHIP_CENTRIC,
            debug_info={"error": error}
        )


class ChunkExpansionStrategy(BaseSearchStrategy):
    """
    Strategy that focuses on expanding context around the source chunks.
    """

    def execute_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute chunk expansion search.

        Focus on thoroughly exploring entities and relationships within and around source chunks.
        """
        start_time = datetime.now()

        try:
            # Extract graph context with focus on chunk locality
            graph_context, metrics = self.graph_rag.extract_graph_context(query.chunk_indices, query)

            # Boost scores for entities with strong chunk connections
            self._boost_chunk_locality(graph_context.entities, query.chunk_indices)

            # Calculate quality scores
            coverage_score = self._calculate_coverage_score(graph_context, query)
            coherence_score = self._calculate_coherence_score(graph_context)
            relevance_score = self._calculate_chunk_relevance_score(graph_context, query)

            # Create result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000

            result = SearchResult(
                query=query,
                graph_context=graph_context,
                total_results=len(graph_context.entities) + len(graph_context.relationships),
                processing_time_ms=processing_time,
                strategy_used=SearchStrategy.CHUNK_EXPANSION,
                coverage_score=coverage_score,
                coherence_score=coherence_score,
                relevance_score=relevance_score,
                debug_info={
                    "strategy": "chunk_expansion",
                    "chunk_locality_boost_applied": True,
                    "source_chunks": len(query.chunk_indices),
                    "metrics": metrics.__dict__
                }
            )

            logger.info(f"Chunk expansion search completed: {len(graph_context.entities)} entities, "
                       f"{len(graph_context.relationships)} relationships in {processing_time:.2f}ms")

            return result

        except Exception as e:
            logger.error(f"Error in chunk expansion search: {e}")
            return self._create_error_result(query, str(e), start_time)

    def _boost_chunk_locality(self, entities: List[EntityMatch], source_chunk_ids: List[str]):
        """Boost relevance scores for entities closely tied to source chunks."""
        for entity in entities:
            # Count how many source chunks this entity appears in
            chunk_overlap = len(set(entity.chunk_sources) & set(source_chunk_ids))
            if chunk_overlap > 0:
                # Boost based on chunk overlap
                boost_factor = 1.0 + (chunk_overlap * 0.2)
                entity.relevance_score = min(1.0, entity.relevance_score * boost_factor)
                entity.match_reason += f"_chunk_overlap_{chunk_overlap}"

    def _calculate_chunk_relevance_score(self, graph_context: GraphContext, query: SearchQuery) -> float:
        """Calculate relevance score based on chunk locality."""
        if not graph_context.entities:
            return 0.0

        # Average entity relevance (handle None values)
        avg_relevance = sum(e.relevance_score or 0.0 for e in graph_context.entities) / len(graph_context.entities)

        # Boost for entities found in multiple source chunks
        multi_chunk_entities = sum(1 for e in graph_context.entities
                                 if len(set(e.chunk_sources) & set(query.chunk_indices)) > 1)
        if multi_chunk_entities > 0:
            multi_chunk_boost = min(0.2, multi_chunk_entities / len(graph_context.entities))
            avg_relevance = min(1.0, avg_relevance + multi_chunk_boost)

        return avg_relevance

    def _create_error_result(self, query: SearchQuery, error: str, start_time: datetime) -> SearchResult:
        """Create an error result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        return SearchResult(
            query=query,
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.CHUNK_EXPANSION,
            debug_info={"error": error}
        )


class HierarchicalStrategy(BaseSearchStrategy):
    """
    Strategy that focuses on hierarchical relationships and organizational structure.
    """

    def execute_search(self, query: SearchQuery) -> SearchResult:
        """
        Execute hierarchical search.

        Focus on organizational hierarchies and structured relationships.
        """
        start_time = datetime.now()

        try:
            # Extract graph context with focus on hierarchical relationships
            hierarchical_query = self._enhance_query_for_hierarchy(query)
            graph_context, metrics = self.graph_rag.extract_graph_context(
                hierarchical_query.chunk_indices,
                hierarchical_query
            )

            # Boost scores for hierarchical entities and relationships
            self._boost_hierarchical_elements(graph_context)

            # Calculate quality scores
            coverage_score = self._calculate_coverage_score(graph_context, query)
            coherence_score = self._calculate_coherence_score(graph_context)
            relevance_score = self._calculate_hierarchical_relevance_score(graph_context)

            # Create result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000

            result = SearchResult(
                query=query,
                graph_context=graph_context,
                total_results=len(graph_context.entities) + len(graph_context.relationships),
                processing_time_ms=processing_time,
                strategy_used=SearchStrategy.HIERARCHICAL,
                coverage_score=coverage_score,
                coherence_score=coherence_score,
                relevance_score=relevance_score,
                debug_info={
                    "strategy": "hierarchical",
                    "hierarchical_boost_applied": True,
                    "hierarchical_entities": self._count_hierarchical_entities(graph_context.entities),
                    "metrics": metrics.__dict__
                }
            )

            logger.info(f"Hierarchical search completed: {len(graph_context.entities)} entities, "
                       f"{len(graph_context.relationships)} relationships in {processing_time:.2f}ms")

            return result

        except Exception as e:
            logger.error(f"Error in hierarchical search: {e}")
            return self._create_error_result(query, str(e), start_time)

    def _enhance_query_for_hierarchy(self, query: SearchQuery) -> SearchQuery:
        """Enhance query to focus on hierarchical relationships."""
        # Add hierarchical relationship types
        hierarchical_relationships = {
            "manages", "reports_to", "leads", "member_of", "belongs_to", "part_of",
            "parent_of", "child_of", "contains", "contained_in"
        }

        # Add organizational entity types
        organizational_entities = {
            "Person", "Employee", "Manager", "Executive", "Company", "Department",
            "Team", "Organization"
        }

        enhanced_query = SearchQuery(
            chunk_indices=query.chunk_indices,
            query_text=query.query_text,
            strategy=SearchStrategy.HIERARCHICAL,
            max_results=query.max_results,
            expansion_depth=min(query.expansion_depth + 1, 3),  # Slightly deeper for hierarchy
            entity_types=query.entity_types or organizational_entities,
            relationship_types=query.relationship_types or hierarchical_relationships,
            entity_categories=query.entity_categories,
            min_confidence_score=query.min_confidence_score,
            include_chunk_context=query.include_chunk_context,
            include_file_context=query.include_file_context,
            boost_recent_entities=query.boost_recent_entities,
            boost_high_importance=True  # Always boost for hierarchical search
        )

        return enhanced_query

    def _boost_hierarchical_elements(self, graph_context: GraphContext):
        """Boost scores for hierarchical entities and relationships."""
        # Boost hierarchical entities
        hierarchical_entity_types = {
            "Manager", "Executive", "Department", "Team", "Company", "Organization"
        }

        for entity in graph_context.entities:
            if entity.entity_type in hierarchical_entity_types:
                entity.relevance_score = min(1.0, entity.relevance_score * 1.3)
                entity.match_reason += "_hierarchical_entity"

        # Boost hierarchical relationships
        hierarchical_relationship_types = {
            "manages", "reports_to", "leads", "member_of", "belongs_to", "part_of"
        }

        for relationship in graph_context.relationships:
            if relationship.relationship_type in hierarchical_relationship_types:
                relationship.relevance_score = min(1.0, relationship.relevance_score * 1.2)
                relationship.match_reason += "_hierarchical_relationship"

    def _calculate_hierarchical_relevance_score(self, graph_context: GraphContext) -> float:
        """Calculate relevance score based on hierarchical structure."""
        if not graph_context.entities:
            return 0.0

        # Count hierarchical entities and relationships
        hierarchical_entities = self._count_hierarchical_entities(graph_context.entities)
        hierarchical_relationships = self._count_hierarchical_relationships(graph_context.relationships)

        # Calculate hierarchical density
        entity_hierarchy_ratio = hierarchical_entities / len(graph_context.entities) if graph_context.entities else 0
        relationship_hierarchy_ratio = hierarchical_relationships / len(graph_context.relationships) if graph_context.relationships else 0

        # Average relevance with hierarchical boost (handle None values)
        avg_relevance = sum(e.relevance_score or 0.0 for e in graph_context.entities) / len(graph_context.entities)
        hierarchical_boost = (entity_hierarchy_ratio + relationship_hierarchy_ratio) / 2 * 0.3

        return min(1.0, avg_relevance + hierarchical_boost)

    def _count_hierarchical_entities(self, entities: List[EntityMatch]) -> int:
        """Count entities that are part of organizational hierarchy."""
        hierarchical_types = {
            "Manager", "Executive", "Department", "Team", "Company", "Organization", "Employee"
        }
        return sum(1 for e in entities if e.entity_type in hierarchical_types)

    def _count_hierarchical_relationships(self, relationships: List[RelationshipMatch]) -> int:
        """Count relationships that represent hierarchical connections."""
        hierarchical_types = {
            "manages", "reports_to", "leads", "member_of", "belongs_to", "part_of",
            "parent_of", "child_of", "contains", "contained_in"
        }
        return sum(1 for r in relationships if r.relationship_type in hierarchical_types)

    def _create_error_result(self, query: SearchQuery, error: str, start_time: datetime) -> SearchResult:
        """Create an error result."""
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000

        return SearchResult(
            query=query,
            graph_context=GraphContext(),
            total_results=0,
            processing_time_ms=processing_time,
            strategy_used=SearchStrategy.HIERARCHICAL,
            debug_info={"error": error}
        )
