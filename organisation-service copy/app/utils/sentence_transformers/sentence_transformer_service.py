"""
Sentence Transformers service for generating embeddings locally.
Replaces Vertex AI to avoid credit card requirements.
"""

import logging
from typing import List, Optional
from sentence_transformers import SentenceTransformer
import torch
import numpy as np

logger = logging.getLogger(__name__)

class SentenceTransformerService:
    """Service for generating embeddings using Sentence Transformers."""
    
    def __init__(self, model_name: str = "sentence-transformers/all-roberta-large-v1"):
        """
        Initialize the Sentence Transformer service.
        
        Args:
            model_name: Name of the sentence transformer model to use.
                       Default is "sentence-transformers/all-roberta-large-v1" (1024 dimensions).
                       Other options include:
                       - "all-mpnet-base-v2" (768 dimensions)
                       - "paraphrase-multilingual-mpnet-base-v2" (768 dimensions)
                       - "all-distilroberta-v1" (768 dimensions)
                       - "multi-qa-mpnet-base-dot-v1" (768 dimensions)
                       - "all-MiniLM-L6-v2" (384 dimensions)
        """
        self.model_name = model_name
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load the sentence transformer model."""
        try:
            logger.info(f"Loading Sentence Transformer model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            
            # Set device (GPU if available, otherwise CPU)
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.model = self.model.to(device)
            logger.info(f"Model loaded successfully on device: {device}")
            
        except Exception as e:
            logger.error(f"Failed to load Sentence Transformer model: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for a list of texts.
        
        Args:
            texts: List of text strings to generate embeddings for.
            
        Returns:
            List of embedding vectors (each vector is a list of floats).
        """
        if not texts:
            return []
        
        if not self.model:
            raise RuntimeError("Model not loaded. Call _load_model() first.")
        
        try:
            logger.info(f"Generating embeddings for {len(texts)} texts")
            
            # Generate embeddings
            embeddings = self.model.encode(
                texts,
                convert_to_tensor=False,  # Return as numpy arrays
                show_progress_bar=len(texts) > 10,  # Show progress for large batches
                batch_size=32  # Process in batches for memory efficiency
            )
            
            # Convert to list of lists for consistency with Vertex AI format
            embeddings_list = [embedding.tolist() for embedding in embeddings]
            
            logger.info(f"Successfully generated {len(embeddings_list)} embeddings")
            return embeddings_list
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def generate_single_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text string to generate embedding for.
            
        Returns:
            Embedding vector as a list of floats.
        """
        embeddings = self.generate_embeddings([text])
        return embeddings[0] if embeddings else []
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of embeddings produced by this model.
        
        Returns:
            Embedding dimension (1024 for all-roberta-large-v1).
        """
        if not self.model:
            raise RuntimeError("Model not loaded. Call _load_model() first.")
        
        return self.model.get_sentence_embedding_dimension()
    
    def get_model_info(self) -> dict:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary containing model information.
        """
        if not self.model:
            return {"model_name": self.model_name, "loaded": False}
        
        return {
            "model_name": self.model_name,
            "loaded": True,
            "embedding_dimension": self.get_embedding_dimension(),
            "device": str(self.model.device),
            "max_seq_length": getattr(self.model, 'max_seq_length', 'Unknown')
        }

# Global instance for reuse across the application
_sentence_transformer_service = None

def get_sentence_transformer_service() -> SentenceTransformerService:
    """
    Get a singleton instance of the SentenceTransformerService.
    
    Returns:
        SentenceTransformerService instance.
    """
    global _sentence_transformer_service
    if _sentence_transformer_service is None:
        _sentence_transformer_service = SentenceTransformerService()
    return _sentence_transformer_service