"""
Generic file service for downloading and processing files from URLs.
"""

import os
import uuid
import hashlib
import requests
from datetime import datetime
from typing import Dict, Any, Tu<PERSON>, Optional
import structlog

from .url_validator import URLValidator
from .file_type_detector import FileTypeDetector
from .text_extractor import TextExtractor

logger = structlog.get_logger()

class GenericFileService:
    """
    Service for downloading and processing files from generic URLs.
    """
    
    def __init__(self):
        self.url_validator = URLValidator()
        self.file_type_detector = FileTypeDetector()
        self.text_extractor = TextExtractor()
        
        # Configure requests session
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'OrganisationService/1.0 (File Processor)'
        })
    
    def download_file_from_url(self, url: str) -> Tuple[bool, str, Optional[bytes], Optional[Dict[str, str]]]:
        """
        Download a file from a URL.
        
        Args:
            url: The URL to download from
            
        Returns:
            Tuple of (success, message, content, headers)
        """
        try:
            # Validate URL first
            is_valid, error_message = self.url_validator.validate_url(url)
            if not is_valid:
                return False, error_message, None, None
            
            # Download the file
            logger.info(f"Downloading file from URL: {url}")
            
            response = self.session.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check content length
            content_length = response.headers.get('content-length')
            if content_length:
                try:
                    size = int(content_length)
                    if size > self.url_validator.MAX_FILE_SIZE:
                        return False, f"File too large: {size} bytes (max: {self.url_validator.MAX_FILE_SIZE})", None, None
                except ValueError:
                    pass
            
            # Download content in chunks to handle large files
            content_chunks = []
            total_size = 0
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    content_chunks.append(chunk)
                    total_size += len(chunk)
                    
                    # Check size limit during download
                    if total_size > self.url_validator.MAX_FILE_SIZE:
                        return False, f"File too large: {total_size} bytes (max: {self.url_validator.MAX_FILE_SIZE})", None, None
            
            content = b''.join(content_chunks)
            
            logger.info(f"Successfully downloaded {total_size} bytes from {url}")
            
            return True, "File downloaded successfully", content, dict(response.headers)
            
        except requests.exceptions.Timeout:
            return False, "Download timed out", None, None
        except requests.exceptions.ConnectionError:
            return False, "Could not connect to URL", None, None
        except requests.exceptions.HTTPError as e:
            return False, f"HTTP error: {e.response.status_code}", None, None
        except requests.exceptions.RequestException as e:
            return False, f"Download failed: {str(e)}", None, None
        except Exception as e:
            logger.error(f"Error downloading file from URL: {str(e)}")
            return False, f"Download error: {str(e)}", None, None
    
    def detect_file_type(self, url: str, content_type: str = None, content: bytes = None) -> Optional[str]:
        """
        Detect file type using multiple methods.
        
        Args:
            url: The source URL
            content_type: HTTP Content-Type header (optional)
            content: File content bytes (optional)
            
        Returns:
            MIME type if detected, None otherwise
        """
        return self.file_type_detector.detect_file_type(url, content_type, content)
    
    def extract_text_from_content(self, content: bytes, mime_type: str) -> Optional[str]:
        """
        Extract text from file content.
        
        Args:
            content: File content as bytes
            mime_type: MIME type of the content
            
        Returns:
            Extracted text or None if extraction failed
        """
        return self.text_extractor.extract_text(content, mime_type)
    
    def generate_file_metadata(self, url: str, content: bytes, mime_type: str, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Generate metadata for a file.
        
        Args:
            url: The source URL
            content: File content as bytes
            mime_type: MIME type of the content
            headers: HTTP response headers (optional)
            
        Returns:
            Dictionary containing file metadata
        """
        try:
            # Generate file ID
            file_id = str(uuid.uuid4())
            
            # Extract filename from URL
            url_path = url.split('?')[0]  # Remove query parameters
            filename = os.path.basename(url_path)
            
            # If no filename in URL, generate one
            if not filename or '.' not in filename:
                extension = self.file_type_detector.get_file_extension(mime_type)
                if extension:
                    filename = f"file_{file_id[:8]}{extension}"
                else:
                    filename = f"file_{file_id[:8]}"
            
            # Calculate content hash
            content_hash = hashlib.sha256(content).hexdigest()
            
            # Get file size
            file_size = len(content)
            
            # Current timestamp
            current_time = datetime.utcnow().isoformat()
            
            metadata = {
                'id': file_id,
                'name': filename,
                'mimeType': mime_type,
                'size': file_size,
                'source_url': url,
                'content_hash': content_hash,
                'createdTime': current_time,
                'modifiedTime': current_time,
                'webViewLink': url,  # Use source URL as view link
                'source': 'generic_url'
            }
            
            # Add additional metadata from headers if available
            if headers:
                if 'last-modified' in headers:
                    metadata['modifiedTime'] = headers['last-modified']
                if 'content-disposition' in headers:
                    # Try to extract filename from Content-Disposition header
                    disposition = headers['content-disposition']
                    if 'filename=' in disposition:
                        try:
                            # Simple extraction, could be improved
                            filename_part = disposition.split('filename=')[1]
                            if filename_part.startswith('"') and filename_part.endswith('"'):
                                filename_part = filename_part[1:-1]
                            if filename_part:
                                metadata['name'] = filename_part
                        except Exception:
                            pass  # Keep original filename
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error generating file metadata: {str(e)}")
            # Return minimal metadata
            return {
                'id': str(uuid.uuid4()),
                'name': f"file_{str(uuid.uuid4())[:8]}",
                'mimeType': mime_type or 'application/octet-stream',
                'size': len(content) if content else 0,
                'source_url': url,
                'createdTime': datetime.utcnow().isoformat(),
                'modifiedTime': datetime.utcnow().isoformat(),
                'webViewLink': url,
                'source': 'generic_url'
            }
    
    def process_file_from_url(self, url: str, file_id: str, user_id: str, organisation_id: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Complete file processing pipeline from URL.
        
        Args:
            url: The URL to process
            file_id: The ID to assign to the file
            user_id: The ID of the user
            organisation_id: The ID of the organisation
            
        Returns:
            Tuple of (success, message, file_data)
        """
        try:
            # 1. Download file
            success, message, content, headers = self.download_file_from_url(url)
            if not success:
                return False, message, None
            
            # 2. Detect file type
            content_type = headers.get('content-type') if headers else None
            mime_type = self.detect_file_type(url, content_type, content)
            
            if not mime_type:
                return False, "Unsupported file type", None
            
            if not self.file_type_detector.is_supported_type(mime_type):
                return False, f"File type not supported: {mime_type}", None
            
            # 3. Generate metadata
            file_metadata = self.generate_file_metadata(url, content, mime_type, headers)
            
            # 4. Extract text
            extracted_text = self.extract_text_from_content(content, mime_type)
            
            if not extracted_text:
                return False, "Could not extract text from file", None
            
            # 5. Upload to Pinecone using existing service
            # from app.utils.pinecone.pinecone_service import PineconeService
            # pinecone_service = PineconeService()
            
            # Prepare file data for Pinecone
            # file_data_for_pinecone = {
            #     'id': file_id,
            #     'name': file_metadata['name'],
            #     'mimeType': mime_type,
            #     'size': file_metadata['size'],
            #     'webViewLink': url
            # }
            
            # Upload to Pinecone with chunking
            # success, message, vector_id = pinecone_service.enhanced_upload_file_to_pinecone(
            #     user_id=user_id,
            #     organisation_id=organisation_id,
            #     file_data=file_data_for_pinecone,
            #     text=extracted_text
            # )
            
            # if not success:
            #     return False, f"Failed to upload to Pinecone: {message}", None
            
            # 6. Create file data compatible with existing schema
            file_data = {
                'id': file_id,
                'name': file_metadata['name'],
                'mime_type': mime_type,
                'size': file_metadata['size'],
                'web_view_link': url,  # Use original URL as view link
                'created_time': datetime.utcnow().isoformat(),
                'modified_time': datetime.utcnow().isoformat(),
                'vector_id': None,
                'content_hash': file_metadata.get('content_hash'),
                'content': extracted_text  # Include extracted text
            }
            
            logger.info(f"Successfully processed file from URL: {url}")
            logger.info(f"File: {file_metadata['name']}, Type: {mime_type}, Size: {file_metadata['size']} bytes, Text length: {len(extracted_text)} chars")
            
            return True, "File processed successfully", file_data
            
        except Exception as e:
            logger.error(f"Error processing file from URL: {str(e)}")
            return False, f"Processing error: {str(e)}", None
    
    def is_google_drive_url(self, url: str) -> bool:
        """
        Check if URL is a Google Drive URL.
        
        Args:
            url: The URL to check
            
        Returns:
            True if it's a Google Drive URL
        """
        return self.url_validator.is_google_drive_url(url)
    
    def detect_url_type(self, url: str) -> str:
        """
        Detect the type of URL.
        
        Args:
            url: The URL to analyze
            
        Returns:
            'google_drive', 'generic_url', or 'unknown'
        """
        return self.url_validator.detect_url_type(url)