import uuid
import json
from datetime import datetime
import structlog
import grpc

from app.db.neo4j import execute_write_query, execute_read_query
from app.modules.organisation.repository.source import SourceQueries
from app.grpc_ import organisation_pb2
from app.utils.constants.sources import SourceType
from app.utils.constants.departments import DefaultDepartments
from app.utils.google_service_account import GoogleServiceAccountManager
from app.utils.google_oauth_manager import oauth_manager

logger = structlog.get_logger()

class SourceService:
    """Service handling source operations."""
    
    def __init__(self):
        self.queries = SourceQueries()
        self.google_sa_manager = GoogleServiceAccountManager()

    def addGoogleDriveSource(self, request, context):
        """
        Add a new Google Drive source with credentials to an organization.
        Supports service account keys.

        Args:
            request: Contains organisation_id, name, key
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add Google Drive source",
                   org_id=request.organisation_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Service account key must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Service account key must be provided"
                )

            key = request.key
            
            # Validate JSON format for service account key
            try:
                json.loads(key)
            except json.JSONDecodeError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in service account key")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Invalid JSON format in service account key"
                )

            # Set source type to Google Drive
            db_source_type = SourceType.GOOGLE_DRIVE.value

            # Check if a source of this type already exists for the organization
            existing_query = self.queries.CHECK_EXISTING_SOURCE
            existing_params = {
                "org_id": request.organisation_id,
                "source_type": db_source_type
            }

            existing_result = execute_read_query(existing_query, existing_params)

            if existing_result:
                logger.error(f"Google Drive source already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Google Drive source already exists for this organization")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Google Drive source already exists for this organization")
            
            # Check if a different auth type already exists for this source type
            conflicting_auth_query = self.queries.CHECK_EXISTING_SOURCE_BY_AUTH_TYPE
            conflicting_auth_params = {
                "org_id": request.organisation_id,
                "source_type": db_source_type,
                "auth_type": "service_account"
            }

            conflicting_result = execute_read_query(conflicting_auth_query, conflicting_auth_params)

            if conflicting_result:
                logger.error(f"Google Drive source with different auth type already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Google Drive source with OAuth authentication already exists. Only one authentication type per organization is allowed.")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Google Drive source with OAuth authentication already exists. Only one authentication type per organization is allowed.")

            # Validate service account access
            validation_success, validation_message, accessible_folders = \
                self.google_sa_manager.validate_service_account_access(key)
            
            if not validation_success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Service account validation failed: {validation_message}")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message=f"Service account validation failed: {validation_message}"
                )
            
            # Generate a unique ID for the source
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()

            # Create the source node in Neo4j using repository
            query = self.queries.CREATE_SOURCE
            params = {
                "org_id": request.organisation_id,
                "id": source_id,
                "name": request.name,
                "type": db_source_type,
                "auth_type": "service_account",
                "key": key,
                "jira_url": None,
                "jira_email": None,
                "is_validated": validation_success,
                "validation_message": validation_message,
                "last_validated_at": current_time if validation_success else None,
                "created_at": current_time,
                "updated_at": current_time
            }

            result = execute_write_query(query, params)

            if not result:
                logger.error("Failed to create Google Drive source in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create Google Drive source")
                return organisation_pb2.AddSourceResponse(success=False, message="Failed to create Google Drive source")

            # If service account validation was successful and we have folders, create folder nodes
            if validation_success and accessible_folders:
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                # Extract file_ids from request if provided
                file_ids = list(request.file_ids) if request.file_ids else None
                
                folder_creation_success, synced_files = gdrive_service.create_folder_nodes_with_permissions(
                    request.organisation_id, accessible_folders, file_ids
                )
                
                if folder_creation_success:
                    if file_ids:
                        validation_message += f" Created {len(accessible_folders)} folder nodes and synced {len(synced_files)} specific files with permission-based access."
                    else:
                        validation_message += f" Created {len(accessible_folders)} folder nodes with permission-based access."

            # Schedule automatic sync for Google Drive source
            try:
                # Schedule an immediate sync job for the newly added Google Drive source
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                job_id = gdrive_service._schedule_sync(
                    user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                    organisation_id=request.organisation_id,
                    full_sync=True,  # Full sync for new sources
                    delay_seconds=5  # Small delay to ensure source is fully created
                )
                
                logger.info(f"Automatic sync job scheduled for new Google Drive source",
                          organisation_id=request.organisation_id,
                          job_id=job_id)
                validation_message += f" Automatic sync job scheduled (ID: {job_id})."
                
            except Exception as sync_error:
                logger.warning(f"Failed to schedule automatic sync for new source: {str(sync_error)}")
                validation_message += " Note: Automatic sync scheduling failed, manual sync may be required."

            # Create source model for response
            source_model = organisation_pb2.SourceModel(
                id=source_id,
                organisation_id=request.organisation_id,
                type=organisation_pb2.SourceType.GOOGLE_DRIVE,
                name=request.name,
                created_at=current_time,
                updated_at=current_time
            )
            
            # Create file info objects for synced files
            file_info_objects = []
            if key and validation_success and 'synced_files' in locals():
                for file_data in synced_files:
                    file_info = organisation_pb2.FileInfo(
                        id=file_data['id'],
                        name=file_data['name']
                    )
                    file_info_objects.append(file_info)

            return organisation_pb2.AddSourceResponse(
                success=True,
                message=validation_message,
                source=source_model,
                synced_files=file_info_objects
            )

        except Exception as e:
            logger.error("Error adding Google Drive source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error adding Google Drive source: {str(e)}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Error adding Google Drive source: {str(e)}")
    
    def addJiraSource(self, request, context):
        """
        Add a new Jira source with credentials to an organization.
        Supports API keys.

        Args:
            request: Contains organisation_id, name, key (used as API key),
                    jira_url, jira_email
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add Jira source",
                   org_id=request.organisation_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("API key must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="API key must be provided"
                )

            key = request.key
            
            # Initialize Jira-specific variables
            jira_url = getattr(request, 'jira_url', None)
            jira_email = getattr(request, 'jira_email', None)
            
            # Set source type to Jira
            db_source_type = SourceType.JIRA.value

            existing_result = execute_read_query(self.queries.CHECK_EXISTING_SOURCE, {
                "org_id": request.organisation_id,
                "source_type": db_source_type
            })

            if existing_result:
                logger.error(f"Jira source already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Jira source already exists for this organization")
                return organisation_pb2.AddSourceResponse(success=False, message=f"Jira source already exists for this organization")

            # Check if Jira URL and email are provided
            if not jira_url:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira URL must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Jira URL must be provided"
                )
            
            if not jira_email:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira email must be provided")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Jira email must be provided"
                )
            
            # Basic validation - check that key is not empty
            if not key.strip():
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid Jira API key")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Invalid Jira API key"
                )
            
            # Validate Jira API token by making a request to the Jira API
            import requests
            from requests.auth import HTTPBasicAuth
            
            # Normalize Jira URL (remove trailing slash if present)
            if jira_url.endswith('/'):
                jira_url = jira_url[:-1]
            
            # Construct the API endpoint URL
            api_url = f"{jira_url}/rest/api/3/myself"
            
            try:
                # Make request to Jira API
                response = requests.get(
                    api_url,
                    auth=HTTPBasicAuth(jira_email, key),
                    headers={"Accept": "application/json"}
                )
                
                # Check if request was successful
                if response.status_code == 200:
                    validation_success = True
                    user_data = response.json()
                    validation_message = f"Jira API key validated successfully for user {user_data.get('displayName', jira_email)}"
                else:
                    validation_success = False
                    validation_message = f"Jira API key validation failed: {response.status_code} - {response.text}"
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(validation_message)
                    return organisation_pb2.AddSourceResponse(
                        success=False,
                        message=validation_message
                    )
            except Exception as e:
                validation_success = False
                validation_message = f"Error validating Jira API key: {str(e)}"
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(validation_message)
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message=validation_message
                )

            # Generate a unique ID for the source
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()

            # Create the source node in Neo4j using repository
            params = {
                "org_id": request.organisation_id,
                "id": source_id,
                "name": request.name,
                "type": db_source_type,
                "key": key,
                "jira_url": jira_url,
                "jira_email": jira_email,
                "is_validated": validation_success,
                "validation_message": validation_message,
                "last_validated_at": current_time if validation_success else None,
                "created_at": current_time,
                "updated_at": current_time
            }

            result = execute_write_query(self.queries.CREATE_SOURCE, params)

            if not result:
                logger.error("Failed to create Jira source in Neo4j")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create Jira source")
                return organisation_pb2.AddSourceResponse(success=False, message="Failed to create Jira source")

            # Create source model for response
            source_model = organisation_pb2.SourceModel(
                id=source_id,
                organisation_id=request.organisation_id,
                type=organisation_pb2.SourceType.JIRA,
                name=request.name,
                created_at=current_time,
                updated_at=current_time
            )

            return organisation_pb2.AddSourceResponse(
                success=True,
                message=validation_message,
                source=source_model,
                synced_files=[]
            )

        except Exception as e:
            logger.error("Error adding Jira source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error adding Jira source: {str(e)}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Error adding Jira source: {str(e)}")
            
    def addSource(self, request, context):
        """
        Add a new source with credentials to an organization.
        This is a legacy method that routes to the appropriate source-specific method.

        Args:
            request: Contains organisation_id, type, name, key (used as generic key),
                    jira_url, jira_email (for Jira sources)
            context: gRPC context

        Returns:
            Response indicating success/failure and the created source
        """
        logger.info("Received request to add source (legacy method)",
                   org_id=request.organisation_id,
                   source_type=request.type)

        # Route to the appropriate source-specific method based on type
        if request.type == organisation_pb2.SourceType.GOOGLE_DRIVE:
            return self.addGoogleDriveSource(request, context)
        elif request.type == organisation_pb2.SourceType.JIRA:
            return self.addJiraSource(request, context)
        else:
            logger.error(f"Unsupported source type: {request.type}")
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(f"Unsupported source type: {request.type}")
            return organisation_pb2.AddSourceResponse(success=False, message=f"Unsupported source type: {request.type}")

    def listSources(self, request, context):
        """
        List all sources for an organization.

        Args:
            request: Contains organisation_id
            context: gRPC context

        Returns:
            Response containing a list of sources and isInitialMapping flag
        """
        logger.info("Received request to list sources", org_id=request.organisation_id)

        try:
            query = self.queries.LIST_SOURCES
            params = {
                "org_id": request.organisation_id
            }

            result = execute_read_query(query, params)

            sources = []
            for record in result:
                source = record.get('s', {})
                
                # Map database source type to protobuf enum
                db_source_type = source.get('type', SourceType.GOOGLE_DRIVE.value)
                
                db_to_proto_mapping = {
                    SourceType.GOOGLE_DRIVE.value: organisation_pb2.SourceType.GOOGLE_DRIVE,
                    SourceType.SLACK.value: organisation_pb2.SourceType.SLACK
                }
                
                proto_source_type = db_to_proto_mapping.get(db_source_type, organisation_pb2.SourceType.GOOGLE_DRIVE)

                source_model = organisation_pb2.SourceModel(
                    id=source.get('id'),
                    organisation_id=request.organisation_id,
                    type=proto_source_type,
                    name=source.get('name'),
                    created_at=source.get('created_at'),
                    updated_at=source.get('updated_at')
                )
                sources.append(source_model)
            
            # Check if any department other than general has access to folders
            is_initial_mapping_query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_DEPARTMENT]->(d:Department)
            WHERE NOT toLower(d.name) = toLower($general_dept)
            MATCH (d)-[:HAS_ACCESS]->(f:GoogleDriveFolder)
            RETURN COUNT(f) > 0 as hasMapping
            """
            
            is_initial_mapping_params = {
                "org_id": request.organisation_id,
                "general_dept": DefaultDepartments.GENERAL.value
            }
            
            mapping_result = execute_read_query(is_initial_mapping_query, is_initial_mapping_params)
            is_initial_mapping = mapping_result[0]['hasMapping'] if mapping_result else False

            return organisation_pb2.ListSourcesResponse(
                success=True,
                message=f"Found {len(sources)} sources",
                sources=sources,
                isInitialMapping=is_initial_mapping
            )

        except Exception as e:
            logger.error("Error listing sources", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing sources: {str(e)}")
            return organisation_pb2.ListSourcesResponse(success=False, message=f"Error listing sources: {str(e)}")

    def deleteSource(self, request, context):
        """
        Delete a source.

        Args:
            request: Contains source_id and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure
        """
        logger.info("Received request to delete source", source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate user is an admin of the organization that owns the source
            admin_query = self.queries.VALIDATE_SOURCE_DELETE_PERMISSION
            admin_params = {
                "user_id": request.user_id,
                "source_id": request.source_id
            }

            admin_result = True #execute_read_query(admin_query, admin_params)

            if not admin_result:
                logger.error(f"User {request.user_id} is not authorized to delete source {request.source_id}")
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(f"Only organization admins can delete sources")
                return organisation_pb2.DeleteSourceResponse(success=False, message=f"Permission denied")

            # Delete the source using repository
            query = self.queries.DELETE_SOURCE
            params = {
                "source_id": request.source_id
            }

            execute_write_query(query, params)

            return organisation_pb2.DeleteSourceResponse(
                success=True,
                message="Source deleted successfully"
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting source: {str(e)}")
            return organisation_pb2.DeleteSourceResponse(success=False, message=f"Error deleting source: {str(e)}")

    def updateGoogleDriveSourceCredentials(self, request, context, source_data=None, organisation_id=None):
        """
        Update credentials for an existing Google Drive source.
        """
        logger.info("Received request to update Google Drive source credentials",
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Service account key must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Service account key must be provided"
                )

            # If source_data and organisation_id are not provided, get them
            if not source_data or not organisation_id:
                # Get existing source
                source_query = self.queries.GET_SOURCE_BY_ID
                source_params = {"source_id": request.source_id}
                source_result = execute_read_query(source_query, source_params)

                if not source_result:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Source not found")
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False,
                        message="Source not found"
                    )

                source_data = source_result[0]['s']
                organisation_id = source_result[0]['org_id']

            # Store the key
            key = request.key
            
            # Validate JSON format for Google Drive key
            try:
                json.loads(key)
            except json.JSONDecodeError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in key")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Invalid JSON format in key"
                )

            # Validate service account access
            validation_success, validation_message, accessible_folders = \
                self.google_sa_manager.validate_service_account_access(key)
            
            if not validation_success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Service account validation failed: {validation_message}")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message=f"Service account validation failed: {validation_message}"
                )

            # Update source credentials
            current_time = datetime.utcnow().isoformat()
            
            # Use the standard update query for Google Drive
            update_query = self.queries.UPDATE_SOURCE_CREDENTIALS
            update_params = {
                "source_id": request.source_id,
                "key": key,
                "updated_at": current_time
            }

            execute_write_query(update_query, update_params)

            # Update validation status
            validation_query = self.queries.UPDATE_SOURCE_VALIDATION_STATUS
            validation_params = {
                "source_id": request.source_id,
                "is_validated": validation_success,
                "last_validated_at": current_time if validation_success else None,
                "validation_message": validation_message
            }

            execute_write_query(validation_query, validation_params)

            # Create updated source model for response
            source_model = organisation_pb2.SourceModel(
                id=request.source_id,
                organisation_id=organisation_id,
                type=organisation_pb2.SourceType.GOOGLE_DRIVE,
                name=source_data.get('name', ''),
                created_at=source_data.get('created_at', ''),
                updated_at=current_time
            )

            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=True,
                message=validation_message,
                source=source_model
            )

        except Exception as e:
            logger.error("Error updating Google Drive source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating Google Drive source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False,
                message=f"Error updating Google Drive source credentials: {str(e)}"
            )
    
    def updateJiraSourceCredentials(self, request, context, source_data=None, organisation_id=None):
        """
        Update credentials for an existing Jira source.
        """
        logger.info("Received request to update Jira source credentials",
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate that key is provided
            if not request.key:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("API key must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="API key must be provided"
                )

            # If source_data and organisation_id are not provided, get them
            if not source_data or not organisation_id:
                # Get existing source
                source_query = self.queries.GET_SOURCE_BY_ID
                source_params = {"source_id": request.source_id}
                source_result = execute_read_query(source_query, source_params)

                if not source_result:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Source not found")
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False,
                        message="Source not found"
                    )

                source_data = source_result[0]['s']
                organisation_id = source_result[0]['org_id']

            # Store the key
            key = request.key
            
            # Get Jira-specific fields if available
            jira_url = getattr(request, 'jira_url', None)
            jira_email = getattr(request, 'jira_email', None)
            
            # For Jira, we need URL and email
            if not jira_url and not source_data.get('jira_url'):
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira URL must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Jira URL must be provided"
                )
            
            if not jira_email and not source_data.get('jira_email'):
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Jira email must be provided")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Jira email must be provided"
                )
            
            # Use provided values or existing values from the database
            current_jira_url = jira_url if jira_url else source_data.get('jira_url')
            current_jira_email = jira_email if jira_email else source_data.get('jira_email')
            
            # Basic validation for Jira API key
            if not key.strip():
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid Jira API key")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Invalid Jira API key"
                )
            
            # Validate Jira API token by making a request to the Jira API
            import requests
            from requests.auth import HTTPBasicAuth
            
            # Normalize Jira URL (remove trailing slash if present)
            if current_jira_url.endswith('/'):
                current_jira_url = current_jira_url[:-1]
            
            # Construct the API endpoint URL
            api_url = f"{current_jira_url}/rest/api/3/myself"
            
            try:
                # Make request to Jira API
                response = requests.get(
                    api_url,
                    auth=HTTPBasicAuth(current_jira_email, key),
                    headers={"Accept": "application/json"}
                )
                
                # Check if request was successful
                if response.status_code == 200:
                    validation_success = True
                    user_data = response.json()
                    validation_message = f"Jira API key validated successfully for user {user_data.get('displayName', current_jira_email)}"
                else:
                    validation_success = False
                    validation_message = f"Jira API key validation failed: {response.status_code} - {response.text}"
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(validation_message)
                    return organisation_pb2.UpdateSourceCredentialsResponse(
                        success=False,
                        message=validation_message
                    )
            except Exception as e:
                validation_success = False
                validation_message = f"Error validating Jira API key: {str(e)}"
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(validation_message)
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message=validation_message
                )

            # Update source credentials
            current_time = datetime.utcnow().isoformat()
            
            # For Jira, update URL and email if provided
            update_query = """
            MATCH (s:Source {id: $source_id})
            SET s.key = $key,
                s.jira_url = $jira_url,
                s.jira_email = $jira_email,
                s.updated_at = $updated_at
            RETURN s
            """
            
            update_params = {
                "source_id": request.source_id,
                "key": key,
                "jira_url": jira_url if jira_url else source_data.get('jira_url'),
                "jira_email": jira_email if jira_email else source_data.get('jira_email'),
                "updated_at": current_time
            }

            execute_write_query(update_query, update_params)

            # Update validation status
            validation_query = self.queries.UPDATE_SOURCE_VALIDATION_STATUS
            validation_params = {
                "source_id": request.source_id,
                "is_validated": validation_success,
                "last_validated_at": current_time if validation_success else None,
                "validation_message": validation_message
            }

            execute_write_query(validation_query, validation_params)

            # Create updated source model for response
            source_model = organisation_pb2.SourceModel(
                id=request.source_id,
                organisation_id=organisation_id,
                type=organisation_pb2.SourceType.JIRA,
                name=source_data.get('name', ''),
                created_at=source_data.get('created_at', ''),
                updated_at=current_time
            )

            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=True,
                message=validation_message,
                source=source_model
            )

        except Exception as e:
            logger.error("Error updating Jira source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating Jira source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False,
                message=f"Error updating Jira source credentials: {str(e)}"
            )
    
    def updateSourceCredentials(self, request, context):
        """
        Update credentials for an existing source.
        This is a legacy method that routes to the appropriate source-specific method.
        """
        logger.info("Received request to update source credentials (legacy method)",
                   source_id=request.source_id, user_id=request.user_id)

        try:
            # Get existing source to determine its type
            source_result = execute_read_query(self.queries.GET_SOURCE_BY_ID, {"source_id": request.source_id})

            if not source_result:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source not found")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message="Source not found"
                )

            source_data = source_result[0]['s']
            organisation_id = source_result[0]['org_id']

            # Route to the appropriate source-specific method based on type
            if source_data.get('type') == SourceType.GOOGLE_DRIVE.value:
                return self.updateGoogleDriveSourceCredentials(request, context, source_data, organisation_id)
            elif source_data.get('type') == SourceType.JIRA.value:
                return self.updateJiraSourceCredentials(request, context, source_data, organisation_id)
            else:
                logger.error(f"Unsupported source type: {source_data.get('type')}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Unsupported source type: {source_data.get('type')}")
                return organisation_pb2.UpdateSourceCredentialsResponse(
                    success=False,
                    message=f"Unsupported source type: {source_data.get('type')}"
                )

        except Exception as e:
            logger.error("Error updating source credentials", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating source credentials: {str(e)}")
            return organisation_pb2.UpdateSourceCredentialsResponse(
                success=False,
                message=f"Error updating source credentials: {str(e)}"
            )
    
    def validateSource(self, request, context):
        """
        Validate a source and return accessible folders.
        """
        logger.info("Received request to validate source", 
                   source_id=request.source_id, org_id=request.organisation_id)

        try:
            # Get source data
            source_query = self.queries.GET_SOURCE_BY_ID
            source_params = {"source_id": request.source_id}
            source_result = execute_read_query(source_query, source_params)

            if not source_result:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source not found")
                return organisation_pb2.ValidateSourceResponse(
                    success=False,
                    message="Source not found",
                    accessible_folders=[]
                )

            source_data = source_result[0]['s']
            key = source_data.get('key')  # Using key field as a generic key
            jira_url = source_data.get('jira_url')
            jira_email = source_data.get('jira_email')

            # Validate credentials based on source type
            if source_data.get('type') == SourceType.GOOGLE_DRIVE.value:
                validation_success, validation_message, accessible_folders = \
                    self.google_sa_manager.validate_service_account_access(key)
            elif source_data.get('type') == SourceType.JIRA.value:
                # Validate Jira API token by making a request to the Jira API
                import requests
                from requests.auth import HTTPBasicAuth
                
                # Check if we have all required Jira information
                if not jira_url or not jira_email or not key:
                    validation_success = False
                    validation_message = "Missing Jira configuration (URL, email, or API key)"
                    accessible_folders = []
                else:
                    # Normalize Jira URL (remove trailing slash if present)
                    if jira_url.endswith('/'):
                        jira_url = jira_url[:-1]
                    
                    # Construct the API endpoint URL
                    api_url = f"{jira_url}/rest/api/3/myself"
                    
                    try:
                        # Make request to Jira API
                        response = requests.get(
                            api_url,
                            auth=HTTPBasicAuth(jira_email, key),
                            headers={"Accept": "application/json"}
                        )
                        
                        # Check if request was successful
                        if response.status_code == 200:
                            validation_success = True
                            user_data = response.json()
                            validation_message = f"Jira API key validated successfully for user {user_data.get('displayName', jira_email)}"
                        else:
                            validation_success = False
                            validation_message = f"Jira API key validation failed: {response.status_code} - {response.text}"
                        
                        accessible_folders = []  # Jira doesn't have folders like Google Drive
                    except Exception as e:
                        validation_success = False
                        validation_message = f"Error validating Jira API key: {str(e)}"
                        accessible_folders = []
                
                # Convert folders to proto format
                proto_folders = []
                for folder in accessible_folders:
                    proto_folder = organisation_pb2.Folder(
                        id=folder['id'],
                        name=folder['name']
                    )
                    proto_folders.append(proto_folder)
                
                return organisation_pb2.ValidateSourceResponse(
                    success=validation_success,
                    message=validation_message,
                    accessible_folders=proto_folders
                )
            
            else:
                return organisation_pb2.ValidateSourceResponse(
                    success=False,
                    message="No valid service account credentials found",
                    accessible_folders=[]
                )

        except Exception as e:
            logger.error("Error validating source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error validating source: {str(e)}")
            return organisation_pb2.ValidateSourceResponse(
                success=False,
                message=f"Error validating source: {str(e)}",
                accessible_folders=[]
            )

    def deleteSource(self, request, context):
        """
        Delete a source.

        Args:
            request: Contains source_id and user_id
            context: gRPC context

        Returns:
            Response indicating success/failure
        """
        logger.info("Received request to delete source", source_id=request.source_id, user_id=request.user_id)

        try:
            # Validate user is an admin of the organization that owns the source
            admin_query = self.queries.VALIDATE_SOURCE_DELETE_PERMISSION
            admin_params = {
                "user_id": request.user_id,
                "source_id": request.source_id
            }

            admin_result = True #execute_read_query(admin_query, admin_params)

            if not admin_result:
                logger.error(f"User {request.user_id} is not authorized to delete source {request.source_id}")
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(f"Only organization admins can delete sources")
                return organisation_pb2.DeleteSourceResponse(success=False, message=f"Permission denied")

            # Delete the source using repository
            query = self.queries.DELETE_SOURCE
            params = {
                "source_id": request.source_id
            }

            execute_write_query(query, params)

            return organisation_pb2.DeleteSourceResponse(
                success=True,
                message="Source deleted successfully"
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error deleting source: {str(e)}")
            return organisation_pb2.DeleteSourceResponse(success=False, message=f"Error deleting source: {str(e)}")

    def initiateGoogleDriveOAuth(self, request, context):
        """
        Initiate Google OAuth flow for Google Drive source creation.
        
        Args:
            request: Contains organisation_id, source_name
            context: gRPC context
            
        Returns:
            Response with OAuth URL and state parameter
        """
        logger.info("Received request to initiate Google Drive OAuth",
                   org_id=request.organisation_id)
        
        try:
            # Check if OAuth is configured
            if not oauth_manager.is_configured():
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("OAuth not configured. Missing client credentials.")
                return organisation_pb2.InitiateOAuthResponse(
                    success=False,
                    message="OAuth not configured. Missing client credentials."
                )
            
            # Check if organization already has a Google Drive source
            existing_query = self.queries.CHECK_EXISTING_SOURCE
            existing_params = {
                "org_id": request.organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value
            }
            
            existing_result = execute_read_query(existing_query, existing_params)
            if existing_result:
                logger.error(f"Google Drive source already exists for organization {request.organisation_id}")
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Google Drive source already exists for this organization")
                return organisation_pb2.InitiateOAuthResponse(
                    success=False,
                    message="Google Drive source already exists for this organization"
                )
            
            # Generate OAuth URL
            success, oauth_url_or_error, state = oauth_manager.generate_oauth_url(request.organisation_id)
            
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(oauth_url_or_error)
                return organisation_pb2.InitiateOAuthResponse(
                    success=False,
                    message=oauth_url_or_error
                )
            
            logger.info("OAuth URL generated successfully",
                       organisation_id=request.organisation_id,
                       state=state[:10] + "..." if state else None)
            
            return organisation_pb2.InitiateOAuthResponse(
                success=True,
                message="OAuth URL generated successfully",
                oauth_url=oauth_url_or_error,
                state=state
            )
            
        except Exception as e:
            logger.error("Error initiating OAuth flow", error=str(e), org_id=request.organisation_id)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error initiating OAuth flow: {str(e)}")
            return organisation_pb2.InitiateOAuthResponse(
                success=False,
                message=f"Error initiating OAuth flow: {str(e)}"
            )

    def completeGoogleDriveOAuth(self, request, context):
        """
        Complete Google OAuth flow and create OAuth source.
        
        Args:
            request: Contains code, state from OAuth callback
            context: gRPC context
            
        Returns:
            Response indicating success/failure and created source
        """
        logger.info("Received request to complete Google Drive OAuth",
                   state=request.state[:10] + "..." if request.state else None)
        
        try:
            # Handle OAuth callback
            logger.info("Starting OAuth callback handling",
                       code_length=len(request.code) if request.code else 0,
                       state_length=len(request.state) if request.state else 0)
            
            success, message, token_data = oauth_manager.handle_oauth_callback(
                request.code, request.state
            )
            
            logger.info("OAuth callback completed",
                       success=success,
                       message=message,
                       token_data_keys=list(token_data.keys()) if token_data else None,
                       user_email=token_data.get('user_email') if token_data else None)
            
            if not success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(message)
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message=message
                )
            
            # Create OAuth source in database
            logger.info("About to create OAuth source",
                       organisation_id=token_data.get('organisation_id'),
                       user_email=token_data.get('user_email'))
            
            source_created, source_data = self._create_oauth_source(token_data)
            if not source_created:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create OAuth source")
                return organisation_pb2.AddSourceResponse(
                    success=False,
                    message="Failed to create OAuth source"
                )
            
            # Schedule automatic sync for the new OAuth source
            try:
                from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService
                gdrive_service = GoogleDriveService()
                
                job_id = gdrive_service._schedule_sync(
                    user_id=token_data.get('organisation_id'),
                    organisation_id=token_data.get('organisation_id'),
                    full_sync=True,
                    delay_seconds=5
                )
                
                logger.info(f"Automatic sync job scheduled for new OAuth source",
                           organisation_id=token_data.get('organisation_id'),
                           job_id=job_id)
                message += f" Automatic sync job scheduled (ID: {job_id})."
                
            except Exception as sync_error:
                logger.warning(f"Failed to schedule automatic sync for OAuth source: {str(sync_error)}")
                message += " Note: Automatic sync scheduling failed, manual sync may be required."
            
            logger.info("OAuth source created successfully",
                       organisation_id=token_data.get('organisation_id'),
                       user_email=token_data.get('user_email'))
            
            # Add detailed logging before creating AddSourceResponse
            logger.info("Creating AddSourceResponse",
                       source_data_type=type(source_data),
                       source_data_id=getattr(source_data, 'id', 'NO_ID') if source_data else 'NO_SOURCE_DATA',
                       message=message)
            
            try:
                response = organisation_pb2.AddSourceResponse(
                    success=True,
                    message=message,
                    source=source_data
                )
                logger.info("AddSourceResponse created successfully")
                return response
                
            except Exception as response_error:
                logger.error("Failed to create AddSourceResponse",
                            error=str(response_error),
                            error_type=type(response_error).__name__,
                            source_data=source_data,
                            source_data_type=type(source_data),
                            message=message)
                
                # Try creating response without source field as fallback
                try:
                    fallback_response = organisation_pb2.AddSourceResponse(
                        success=False,
                        message=f"OAuth source created but response serialization failed: {str(response_error)}"
                    )
                    logger.warning("Using fallback AddSourceResponse without source field")
                    return fallback_response
                except Exception as fallback_error:
                    logger.error("Even fallback response failed", error=str(fallback_error))
                    raise response_error
            
        except Exception as e:
            logger.error("Error completing OAuth flow", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error completing OAuth flow: {str(e)}")
            return organisation_pb2.AddSourceResponse(
                success=False,
                message=f"Error completing OAuth flow: {str(e)}"
            )

    def refreshOAuthToken(self, request, context):
        """
        Refresh OAuth access token for an organization.
        
        Args:
            request: Contains organisation_id
            context: gRPC context
            
        Returns:
            Response indicating success/failure of token refresh
        """
        logger.info("Received request to refresh OAuth token",
                   org_id=request.organisation_id)
        
        try:
            # Refresh token
            success, message, new_token_data = oauth_manager.refresh_access_token(request.organisation_id)
            
            if not success:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(message)
                return organisation_pb2.RefreshTokenResponse(
                    success=False,
                    message=message
                )
            
            # Update token in database
            updated = self._update_oauth_tokens(request.organisation_id, new_token_data)
            if not updated:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to update tokens in database")
                return organisation_pb2.RefreshTokenResponse(
                    success=False,
                    message="Failed to update tokens in database"
                )
            
            logger.info("OAuth token refreshed successfully", org_id=request.organisation_id)
            
            return organisation_pb2.RefreshTokenResponse(
                success=True,
                message="Token refreshed successfully",
                expires_at=new_token_data.get('token_expires_at')
            )
            
        except Exception as e:
            logger.error("Error refreshing OAuth token", error=str(e), org_id=request.organisation_id)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error refreshing OAuth token: {str(e)}")
            return organisation_pb2.RefreshTokenResponse(
                success=False,
                message=f"Error refreshing OAuth token: {str(e)}"
            )

    def revokeOAuthToken(self, request, context):
        """
        Revoke OAuth tokens and delete OAuth source.
        
        Args:
            request: Contains organisation_id
            context: gRPC context
            
        Returns:
            Response indicating success/failure of token revocation
        """
        logger.info("Received request to revoke OAuth token",
                   org_id=request.organisation_id)
        
        try:
            # Revoke token with Google
            success, message = oauth_manager.revoke_token(request.organisation_id)
            
            # Delete OAuth source from database (regardless of revocation success)
            deleted = self._delete_oauth_source(request.organisation_id)
            
            if success and deleted:
                logger.info("OAuth token revoked and source deleted", org_id=request.organisation_id)
                return organisation_pb2.RevokeTokenResponse(
                    success=True,
                    message="OAuth token revoked and source deleted successfully"
                )
            elif deleted:
                logger.warning("OAuth source deleted but token revocation failed",
                             org_id=request.organisation_id,
                             revoke_message=message)
                return organisation_pb2.RevokeTokenResponse(
                    success=True,
                    message=f"OAuth source deleted. Token revocation status: {message}"
                )
            else:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to delete OAuth source")
                return organisation_pb2.RevokeTokenResponse(
                    success=False,
                    message="Failed to delete OAuth source"
                )
            
        except Exception as e:
            logger.error("Error revoking OAuth token", error=str(e), org_id=request.organisation_id)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error revoking OAuth token: {str(e)}")
            return organisation_pb2.RevokeTokenResponse(
                success=False,
                message=f"Error revoking OAuth token: {str(e)}"
            )

    def _create_oauth_source(self, token_data):
        """
        Create OAuth source in database.
        
        Args:
            token_data: Dictionary containing OAuth token information
            
        Returns:
            Tuple of (success, source_model)
        """
        try:
            # Check if a different auth type already exists for this source type
            conflicting_auth_query = self.queries.CHECK_EXISTING_SOURCE_BY_AUTH_TYPE
            conflicting_auth_params = {
                "org_id": token_data.get('organisation_id'),
                "source_type": SourceType.GOOGLE_DRIVE.value,
                "auth_type": "oauth"
            }

            conflicting_result = execute_read_query(conflicting_auth_query, conflicting_auth_params)

            if conflicting_result:
                logger.error(f"Google Drive source with different auth type already exists for organization {token_data.get('organisation_id')}")
                return False, {"error": "Google Drive source with service account authentication already exists. Only one authentication type per organization is allowed."}
            
            # Check if OAuth source already exists
            existing_query = self.queries.CHECK_EXISTING_SOURCE
            existing_params = {
                "org_id": token_data.get('organisation_id'),
                "source_type": SourceType.GOOGLE_DRIVE.value
            }

            existing_result = execute_read_query(existing_query, existing_params)

            if existing_result:
                logger.error(f"Google Drive OAuth source already exists for organization {token_data.get('organisation_id')}")
                return False, {"error": "Google Drive OAuth source already exists for this organization"}
            
            source_id = str(uuid.uuid4())
            current_time = datetime.utcnow().isoformat()
            
            # Prepare OAuth credentials JSON
            oauth_credentials = {
                'access_token': token_data.get('access_token'),
                'refresh_token': token_data.get('refresh_token'),
                'token_expires_at': token_data.get('token_expires_at'),
                'scopes': token_data.get('scopes', []),
                'user_email': token_data.get('user_email'),
                'user_name': token_data.get('user_name')
            }
            
            # Create source with OAuth-specific fields
            query = """
            MATCH (o:Organisation {id: $org_id})
            CREATE (o)-[:HAS_SOURCE]->(s:Source {
                id: $source_id,
                name: $name,
                type: $source_type,
                auth_type: 'oauth',
                oauth_credentials: $oauth_credentials,
                oauth_user_email: $user_email,
                oauth_scopes: $scopes,
                is_validated: true,
                validation_message: 'OAuth authentication successful',
                last_validated_at: $current_time,
                created_at: $current_time,
                updated_at: $current_time
            })
            RETURN s
            """
            
            params = {
                "org_id": token_data.get('organisation_id'),
                "source_id": source_id,
                "name": f"Google Drive OAuth - {token_data.get('user_email', 'Unknown')}",
                "source_type": SourceType.GOOGLE_DRIVE.value,
                "oauth_credentials": json.dumps(oauth_credentials),
                "user_email": token_data.get('user_email'),
                "scopes": json.dumps(token_data.get('scopes', [])),
                "current_time": current_time
            }
            
            result = execute_write_query(query, params)
            
            if result:
                # Validate source_id before creating SourceModel
                if not source_id or not isinstance(source_id, str) or len(source_id.strip()) == 0:
                    logger.error("Invalid source_id for SourceModel creation",
                                source_id=source_id,
                                source_id_type=type(source_id),
                                organisation_id=token_data.get('organisation_id'))
                    return False, None
                
                # Validate organisation_id
                org_id = token_data.get('organisation_id')
                if not org_id or not isinstance(org_id, str) or len(org_id.strip()) == 0:
                    logger.error("Invalid organisation_id for SourceModel creation",
                                organisation_id=org_id,
                                source_id=source_id)
                    return False, None
                
                # Validate name
                name = params.get("name")
                if not name or not isinstance(name, str) or len(name.strip()) == 0:
                    logger.error("Invalid name for SourceModel creation",
                                name=name,
                                source_id=source_id,
                                organisation_id=org_id)
                    return False, None
                
                # Add diagnostic logging before creating SourceModel
                logger.info("Creating SourceModel with validated parameters",
                           source_id=source_id,
                           source_id_type=type(source_id),
                           source_id_length=len(source_id),
                           organisation_id=org_id,
                           name=name,
                           current_time=current_time)
                
                try:
                    # Create source model for response with explicit string conversion
                    source_model = organisation_pb2.SourceModel(
                        id=str(source_id).strip(),
                        organisation_id=str(org_id).strip(),
                        type=organisation_pb2.SourceType.GOOGLE_DRIVE,
                        name=str(name).strip(),
                        created_at=str(current_time),
                        updated_at=str(current_time)
                    )
                    
                    logger.info("SourceModel created successfully",
                               organisation_id=org_id,
                               source_id=source_id,
                               model_id=source_model.id)
                    
                except Exception as model_error:
                    logger.error("Failed to create SourceModel",
                                error=str(model_error),
                                error_type=type(model_error).__name__,
                                source_id=source_id,
                                organisation_id=org_id,
                                name=name,
                                current_time=current_time)
                    # Return error instead of raising to prevent service crash
                    return False, None
                
                logger.info("OAuth source created successfully",
                           organisation_id=org_id,
                           source_id=source_id)
                return True, source_model
            else:
                logger.error("Failed to create OAuth source in database")
                return False, None
                
        except Exception as e:
            logger.error("Error creating OAuth source", error=str(e))
            return False, None

    def _update_oauth_tokens(self, organisation_id, token_data):
        """
        Update OAuth tokens in database.
        
        Args:
            organisation_id: Organization ID
            token_data: New token data
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            oauth_credentials = {
                'access_token': token_data.get('access_token'),
                'refresh_token': token_data.get('refresh_token'),
                'token_expires_at': token_data.get('token_expires_at'),
                'scopes': token_data.get('scopes', []),
                'user_email': token_data.get('user_email'),
                'user_name': token_data.get('user_name')
            }
            
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = $source_type AND s.auth_type = 'oauth'
            SET s.oauth_credentials = $oauth_credentials,
                s.updated_at = $updated_at,
                s.last_validated_at = $updated_at
            """
            
            params = {
                "org_id": organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value,
                "oauth_credentials": json.dumps(oauth_credentials),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            execute_write_query(query, params)
            return True
            
        except Exception as e:
            logger.error("Failed to update OAuth tokens", error=str(e))
            return False

    def _delete_oauth_source(self, organisation_id):
        """
        Delete OAuth source from database.
        
        Args:
            organisation_id: Organization ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            query = """
            MATCH (o:Organisation {id: $org_id})-[:HAS_SOURCE]->(s:Source)
            WHERE s.type = $source_type AND s.auth_type = 'oauth'
            DETACH DELETE s
            """
            
            params = {
                "org_id": organisation_id,
                "source_type": SourceType.GOOGLE_DRIVE.value
            }
            
            execute_write_query(query, params)
            return True
            
        except Exception as e:
            logger.error("Failed to delete OAuth source", error=str(e))
            return False