from app.modules.organisation.models.schema_loader import schema

class OrganisationQuery:

    def __init__(self):
        # Get labels and relationships from schema
        self.org_label = schema.get_node_labels()[0]  # "Organisation"
        self.user_label = schema.get_node_labels()[1]  # "User"
        self.dept_label = schema.get_node_labels()[2]  # "Department"
        self.org_user_rel = schema.get_relationship_types()[0]  # "CREATED_BY"
        self.org_dept_rel = schema.get_relationship_types()[1]  # "HAS_DEPARTMENT"
        self.user_dept_rel = schema.get_relationship_types()[2]  # "BELONGS_TO"

    @property
    def CREATE_ORG_AND_ADMIN(self):
        return f"""
            // Create organization if it doesn't exist (using MERGE instead of CREATE to prevent duplicates)
            MERGE (o:{self.org_label} {{id: $org_id}})
            ON CREATE SET
                o.name = $org_name,
                o.website_url = $website_url,
                o.industry = $industry,
                o.created_by = $admin_id,
                o.created_at = $timestamp,
                o.logo = $logo,
                o.updated_at = $timestamp,
                o.mcp_key = $mcp_key,
                o.is_key_revoked = false

            WITH o
            // Optimized user MERGE with conditional logic
            MERGE (u:{self.user_label} {{email: $admin_email}})
            ON CREATE SET
                u += {{
                    id: $admin_id,
                    name: $admin_name,
                    creation_type: $creation_type,
                    created_at: $timestamp,
                    updated_at: $timestamp
                }}
            ON MATCH SET
                u.updated_at = $timestamp,
                u += CASE 
                    WHEN u.creation_type = $auto_created THEN {{
                        id: $admin_id,
                        name: $admin_name,
                        creation_type: $creation_type
                    }}
                    ELSE {{}} 
                END

            // Create relationship between user and organization
            MERGE (o)-[:{self.org_user_rel}]->(u)

            RETURN o, u
        """

    @property
    def CREATE_DEPARTMENTS_BATCH(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $org_id}})
            MATCH (u:{self.user_label} {{id: $admin_id}})
            
            UNWIND $departments AS dept
            CREATE (d:{self.dept_label} {{
                id: dept.id,
                name: dept.name,
                description: dept.description,
                organisation_id: $org_id,
                visibility: dept.visibility,
                created_by: $admin_id,
                created_at: $timestamp,
                updated_at: $timestamp
            }})
            
            CREATE (o)-[:{self.org_dept_rel}]->(d)
            CREATE (u)-[:{self.user_dept_rel} {{
                role: $admin_role,
                joined_at: $timestamp,
                invited_by: null,
                permission: $admin_permission
            }}]->(d)
            
            RETURN collect(d) as departments
        """

    @property
    def FETCH_ORG_DETAILS(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $id}})
            OPTIONAL MATCH (o)-[:{self.org_dept_rel}]->(d:{self.dept_label})
            OPTIONAL MATCH (d)<-[:{self.user_dept_rel}]-(u:{self.user_label})
            WITH o, d, COUNT(DISTINCT u) AS member_count
            RETURN o, 
                COLLECT({{
                    id: d.id,
                    name: d.name,
                    description: d.description,
                    member_count: member_count
                }}) AS departments
        """
        
    @property
    def UPDATE_ORGANISATION(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $org_id}})
            SET o.name = $org_name,
                o.website_url = $website_url,
                o.industry = $industry,
                o.updated_at = $timestamp,
                o.logo = $logo
            RETURN o
        """
    
    @property
    def FETCH_ORG_DETAILS_OF_USER(self):
        return f"""
            MATCH (u:{self.user_label} {{email: $user_email, creation_type: 'signed_in'}})-[:{self.user_dept_rel}]->(d:{self.dept_label} {{name: $dept}})<-[:{self.org_dept_rel}]-(o:{self.org_label})
            OPTIONAL MATCH (o)-[:{self.org_user_rel}]->(creator:{self.user_label})
            RETURN DISTINCT o, 
                creator.email as creator_id,
                EXISTS((u)-[:{self.user_dept_rel} {{role: $user_role}}]->(d)) as is_admin
        """

    @property
    def VALIDATE_USER_ORGANISATION_MEMBERSHIP(self):
        return f"""
            MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.user_dept_rel}]->(d:{self.dept_label})<-[:{self.org_dept_rel}]-(o:{self.org_label} {{id: $organisation_id}})
            RETURN COUNT(u) > 0 as is_member
        """
        
    @property
    def REVOKE_MCP_KEY(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $org_id}})
            SET o.is_key_revoked = true,
                o.mcp_key = null
            RETURN o
        """
        
    @property
    def REGENERATE_MCP_KEY(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $org_id}})
            SET o.mcp_key = $new_mcp_key,
                o.is_key_revoked = false
            RETURN o
        """
    
    @property
    def FETCH_ORG_WITH_ADMIN_DETAILS(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $id}})
            MATCH (o)-[:{self.org_user_rel}]->(admin:{self.user_label})
            OPTIONAL MATCH (o)-[:{self.org_dept_rel}]->(d:{self.dept_label})
            OPTIONAL MATCH (d)<-[:{self.user_dept_rel}]-(u:{self.user_label})
            WITH o, admin, d, COUNT(DISTINCT u) AS member_count
            RETURN o,
                admin,
                COLLECT({{
                    id: d.id,
                    name: d.name,
                    description: d.description,
                    member_count: member_count
                }}) AS departments
        """
        
    @property
    def CHECK_USER_IN_ANY_ORGANISATION(self):
        return f"""
            MATCH (u:{self.user_label} {{id: $user_id}})-[:{self.user_dept_rel}]->(d:{self.dept_label})<-[:{self.org_dept_rel}]-(o:{self.org_label})
            RETURN COUNT(DISTINCT o) > 0 as is_member_of_any_org
        """
        
    @property
    def FETCH_ORGANISATION_DETAILS(self):
        return f"""
            MATCH (o:{self.org_label} {{id: $organisation_id}})
            RETURN o
        """
