from app.modules.agents.models.schema_loader import agent_schema

class AgentRelationshipQueries:
    """Queries for managing agent relationships."""
    
    def __init__(self):
        # Get schema definitions
        self.user_label = "User"  # From organisation schema
        self.dept_label = "Department"  # From organisation schema
        self.agent_label = agent_schema.get_node_labels()[0]  # "Agent"
        
        # Relationship types
        self.owns_rel = agent_schema.get_relationship_types()[0]  # "OWNS"
        self.belongs_to_rel = agent_schema.get_relationship_types()[1]  # "BELONGS_TO"
        self.has_access_rel = agent_schema.get_relationship_types()[2]  # "HAS_ACCESS"

    @property
    def CREATE_OWNERSHIP_RELATIONSHIP(self):
        """Create ownership relationship between user and agent."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        CREATE (u)-[:{self.owns_rel} {{created_at: $created_at}}]->(a)
        RETURN u, a
        """

    @property
    def CREATE_DEPARTMENT_RELATIONSHIP(self):
        """Create relationship between agent and department."""
        return f"""
        MATCH (d:{self.dept_label} {{id: $dept_id}})
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        CREATE (a)-[:{self.belongs_to_rel} {{assigned_at: $assigned_at}}]->(d)
        RETURN a, d
        """

    @property
    def CREATE_ACCESS_RELATIONSHIP(self):
        """Create access relationship between user and agent."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        CREATE (u)-[:{self.has_access_rel} {{
            access_type: $access_type,
            granted_at: $granted_at,
            granted_by: $granted_by
        }}]->(a)
        RETURN u, a
        """

    @property
    def CREATE_BATCH_ACCESS_RELATIONSHIPS(self):
        """Create multiple access relationships for an agent."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        UNWIND $user_ids as user_id
        MATCH (u:{self.user_label} {{id: user_id}})
        CREATE (u)-[:{self.has_access_rel} {{
            access_type: $access_type,
            granted_at: $granted_at,
            granted_by: $granted_by
        }}]->(a)
        RETURN count(*) as relationships_created
        """

    @property
    def REMOVE_OWNERSHIP_RELATIONSHIP(self):
        """Remove ownership relationship."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[r:{self.owns_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        DELETE r
        """

    @property
    def REMOVE_DEPARTMENT_RELATIONSHIP(self):
        """Remove department relationship."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})-[r:{self.belongs_to_rel}]->(d:{self.dept_label})
        DELETE r
        """

    @property
    def REMOVE_ACCESS_RELATIONSHIP(self):
        """Remove access relationship between user and agent."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[r:{self.has_access_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        DELETE r
        """

    @property
    def REMOVE_ALL_ACCESS_RELATIONSHIPS(self):
        """Remove all access relationships for an agent."""
        return f"""
        MATCH (u:{self.user_label})-[r:{self.has_access_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        DELETE r
        """

    @property
    def UPDATE_OWNERSHIP_RELATIONSHIP(self):
        """Transfer ownership of an agent to another user."""
        return f"""
        MATCH (old_owner:{self.user_label})-[old_rel:{self.owns_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        MATCH (new_owner:{self.user_label} {{id: $new_owner_id}})
        DELETE old_rel
        CREATE (new_owner)-[:{self.owns_rel} {{created_at: $created_at}}]->(a)
        RETURN new_owner, a
        """

    @property
    def UPDATE_DEPARTMENT_RELATIONSHIP(self):
        """Move agent to a different department."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})-[old_rel:{self.belongs_to_rel}]->(old_dept:{self.dept_label})
        MATCH (new_dept:{self.dept_label} {{id: $new_dept_id}})
        DELETE old_rel
        CREATE (a)-[:{self.belongs_to_rel} {{assigned_at: $assigned_at}}]->(new_dept)
        RETURN a, new_dept
        """

    @property
    def UPDATE_ACCESS_RELATIONSHIP(self):
        """Update access type for a user-agent relationship."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})-[r:{self.has_access_rel}]->(a:{self.agent_label} {{id: $agent_id}})
        SET r.access_type = $access_type,
            r.granted_at = $granted_at,
            r.granted_by = $granted_by
        RETURN r
        """

    @property
    def GET_AGENT_RELATIONSHIPS(self):
        """Get all relationships for an agent."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        OPTIONAL MATCH (owner:{self.user_label})-[owns:{self.owns_rel}]->(a)
        OPTIONAL MATCH (a)-[belongs:{self.belongs_to_rel}]->(dept:{self.dept_label})
        OPTIONAL MATCH (users:{self.user_label})-[access:{self.has_access_rel}]->(a)
        RETURN a, owner, owns, dept, belongs, collect({{user: users, access: access}}) as access_relationships
        """

    @property
    def GET_USER_AGENT_RELATIONSHIPS(self):
        """Get all agent relationships for a user."""
        return f"""
        MATCH (u:{self.user_label} {{id: $user_id}})
        OPTIONAL MATCH (u)-[owns:{self.owns_rel}]->(owned_agents:{self.agent_label})
        OPTIONAL MATCH (u)-[access:{self.has_access_rel}]->(accessible_agents:{self.agent_label})
        RETURN u, 
               collect(distinct {{agent: owned_agents, relationship: owns}}) as owned_agents,
               collect(distinct {{agent: accessible_agents, relationship: access}}) as accessible_agents
        """

    @property
    def VALIDATE_RELATIONSHIP_EXISTS(self):
        """Check if a specific relationship exists."""
        return f"""
        MATCH (from_node)-[r]->(to_node)
        WHERE from_node.id = $from_id 
          AND to_node.id = $to_id 
          AND type(r) = $relationship_type
        RETURN r
        """

    @property
    def COUNT_AGENT_RELATIONSHIPS(self):
        """Count relationships for an agent by type."""
        return f"""
        MATCH (a:{self.agent_label} {{id: $agent_id}})
        OPTIONAL MATCH (a)<-[owns:{self.owns_rel}]-()
        OPTIONAL MATCH (a)-[belongs:{self.belongs_to_rel}]->()
        OPTIONAL MATCH (a)<-[access:{self.has_access_rel}]-()
        RETURN count(owns) as ownership_count,
               count(belongs) as department_count,
               count(access) as access_count
        """