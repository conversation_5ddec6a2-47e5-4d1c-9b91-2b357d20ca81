"""
Google Drive Connection Management

This module handles connection establishment and management for Google Drive API
with support for both OAuth and service account authentication.
"""

import logging
from typing import Dict, Any, Optional
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.utils.source_credentials import get_google_credentials, get_source_auth_info
from app.utils.google_oauth_manager import oauth_manager

logger = logging.getLogger(__name__)


class GoogleDriveConnection:
    """
    Manages connection to Google Drive API with dual authentication support.
    Supports both OAuth and service account authentication methods.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize connection with configuration.
        
        Args:
            config: Connection configuration dictionary
        """
        self.config = config
        self.service = None
        self._authenticated = False
        self._auth_type = None
        self._user_email = None
        
        # Extract connection parameters
        self.organisation_id = config.get('organisation_id')
        self.scopes = config.get('scopes', [
            'https://www.googleapis.com/auth/drive.readonly',
            'https://www.googleapis.com/auth/drive'
        ])
    
    def connect(self) -> bool:
        """
        Establish connection and authenticate with Google Drive API.
        Automatically detects and uses the appropriate authentication method.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to Google Drive API for organisation {self.organisation_id}")
            
            if not self.organisation_id:
                logger.error("Organisation ID is required for Google Drive connection")
                return False
            
            # Get source authentication info
            auth_info = get_source_auth_info(self.organisation_id)
            if not auth_info:
                logger.error(f"No source found for organisation {self.organisation_id}")
                return False
            
            self._auth_type = auth_info.get('auth_type', 'service_account')
            logger.info(f"Using {self._auth_type} authentication for organisation {self.organisation_id}")
            
            # Get appropriate credentials
            credentials = get_google_credentials(self.organisation_id)
            if not credentials:
                logger.error(f"No valid credentials found for organisation {self.organisation_id}")
                return False
            
            # Build the Google Drive service
            self.service = build('drive', 'v3', credentials=credentials)
            
            # Test connection with a simple API call
            self._test_connection()
            
            self._authenticated = True
            logger.info(f"Successfully connected to Google Drive API using {self._auth_type}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Google Drive API: {str(e)}")
            self._authenticated = False
            return False
    
    def disconnect(self):
        """Clean up connection resources."""
        try:
            if self.service:
                # Google API client doesn't require explicit cleanup
                # but we can clear the reference
                self.service = None
            
            self._authenticated = False
            self._auth_type = None
            self._user_email = None
            logger.info("Disconnected from Google Drive API")
            
        except Exception as e:
            logger.warning(f"Error during Google Drive disconnect: {str(e)}")
    
    def is_connected(self) -> bool:
        """
        Check if connection is active and authenticated.
        
        Returns:
            bool: True if connected and authenticated
        """
        return self._authenticated and self.service is not None
    
    def get_service(self):
        """
        Get the Google Drive service instance.
        
        Returns:
            Google Drive service instance
            
        Raises:
            ConnectionError: If not connected
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to Google Drive API. Call connect() first.")
        
        return self.service
    
    def get_auth_type(self) -> Optional[str]:
        """
        Get the authentication type being used.
        
        Returns:
            str: 'oauth' or 'service_account' or None if not connected
        """
        return self._auth_type
    
    def get_user_email(self) -> Optional[str]:
        """
        Get the authenticated user's email address.
        
        Returns:
            str: User email or None if not available
        """
        return self._user_email
    
    def refresh_connection(self) -> bool:
        """
        Refresh the connection (useful for long-running processes).
        For OAuth connections, this will attempt to refresh the access token.
        
        Returns:
            bool: True if refresh successful, False otherwise
        """
        try:
            if self.is_connected():
                # For OAuth connections, try to refresh token if needed
                if self._auth_type == 'oauth':
                    logger.info(f"Refreshing OAuth token for organisation {self.organisation_id}")
                    success, message, _ = oauth_manager.refresh_access_token(self.organisation_id)
                    if not success:
                        logger.warning(f"Token refresh failed: {message}")
                        # Try to reconnect
                        return self.connect()
                
                # Test current connection
                self._test_connection()
                return True
            else:
                # Reconnect if not connected
                return self.connect()
                
        except Exception as e:
            logger.warning(f"Connection refresh failed, attempting reconnect: {str(e)}")
            return self.connect()
    
    def _test_connection(self):
        """Test the connection by making a simple API call."""
        try:
            # Test with a simple about() call to verify credentials
            about = self.service.about().get(fields="user").execute()
            user_info = about.get('user', {})
            self._user_email = user_info.get('emailAddress', 'Unknown')
            
            logger.debug(f"Connection test successful. User: {self._user_email}")
            
        except HttpError as e:
            if e.resp.status == 401:
                raise ConnectionError("Authentication failed - invalid or expired credentials")
            elif e.resp.status == 403:
                raise ConnectionError("Access denied - insufficient permissions")
            else:
                raise ConnectionError(f"API test failed: {str(e)}")
        except Exception as e:
            raise ConnectionError(f"Connection test failed: {str(e)}")
    
    def validate_permissions(self) -> Dict[str, bool]:
        """
        Validate that the authenticated user/service account has required permissions.
        
        Returns:
            Dict[str, bool]: Dictionary of permission checks
        """
        permissions = {
            'can_list_files': False,
            'can_read_files': False,
            'can_access_metadata': False
        }
        
        if not self.is_connected():
            return permissions
        
        try:
            # Test listing files (basic read permission)
            self.service.files().list(pageSize=1, fields="files(id,name)").execute()
            permissions['can_list_files'] = True
            permissions['can_read_files'] = True
            
            # Test getting file metadata
            self.service.about().get(fields="user").execute()
            permissions['can_access_metadata'] = True
            
        except HttpError as e:
            logger.warning(f"Permission validation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error validating permissions: {str(e)}")
        
        return permissions
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get information about the current connection.
        
        Returns:
            Dict[str, Any]: Connection information
        """
        info = {
            'connected': self.is_connected(),
            'organisation_id': self.organisation_id,
            'auth_type': self._auth_type,
            'user_email': self._user_email,
            'scopes': self.scopes,
            'service_available': self.service is not None
        }
        
        if self.is_connected():
            try:
                about = self.service.about().get(fields="user,storageQuota").execute()
                info.update({
                    'storage_quota': about.get('storageQuota', {}),
                    'permissions': self.validate_permissions()
                })
            except Exception as e:
                logger.warning(f"Could not get connection details: {str(e)}")
        
        return info
    
    def handle_auth_error(self, error: Exception) -> bool:
        """
        Handle authentication errors and attempt recovery.
        
        Args:
            error: The authentication error that occurred
            
        Returns:
            bool: True if recovery was successful, False otherwise
        """
        try:
            logger.warning(f"Handling auth error: {str(error)}")
            
            if isinstance(error, HttpError) and error.resp.status == 401:
                # Token expired or invalid
                if self._auth_type == 'oauth':
                    logger.info("Attempting OAuth token refresh")
                    success, message, _ = oauth_manager.refresh_access_token(self.organisation_id)
                    if success:
                        # Reconnect with refreshed token
                        return self.connect()
                    else:
                        logger.error(f"Token refresh failed: {message}")
                        return False
                else:
                    # Service account credentials might be invalid
                    logger.error("Service account authentication failed")
                    return False
            
            # For other errors, try to reconnect
            return self.connect()
            
        except Exception as e:
            logger.error(f"Error handling auth error: {str(e)}")
            return False