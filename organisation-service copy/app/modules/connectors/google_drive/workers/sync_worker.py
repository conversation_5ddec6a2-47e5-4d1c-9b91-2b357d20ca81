import json
import time
import signal
import threading
from datetime import datetime, timedelta
import structlog

from app.utils.redis.redis_service import RedisService
from app.modules.connectors.google_drive.services.google_drive_service import GoogleDriveService

logger = structlog.get_logger()

class GoogleDriveSyncWorker:
    """
    Worker to process Google Drive sync jobs from Redis.
    """
    
    def __init__(self, poll_interval=5):
        """
        Initialize the worker.
        
        Args:
            poll_interval: Time in seconds between polling Redis for new jobs
        """
        self.redis_service = RedisService()
        self.drive_service = GoogleDriveService()
        self.poll_interval = poll_interval
        self.running = False
        self.thread = None
    
    def start(self):
        """
        Start the worker in a background thread.
        """
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info("Google Drive sync worker started")
    
    def stop(self):
        """
        Stop the worker.
        """
        self.running = False
        if self.thread:
            self.thread.join(timeout=30)
        logger.info("Google Drive sync worker stopped")
    
    def _run(self):
        """
        Main worker loop.
        """
        while self.running:
            try:
                # Get the next job from the sorted set
                jobs = self.redis_service.zrangebyscore(
                    "gdrive_sync_queue", 
                    "-inf", 
                    time.time(),
                    start=0,
                    num=1
                )
                if not jobs:
                    # No jobs ready yet, sleep and try again
                    time.sleep(self.poll_interval)
                    continue
                
                job_id = jobs[0]
                
                # Remove the job from the queue
                self.redis_service.zrem("gdrive_sync_queue", job_id)
                
                # Get the job data
                job_data_str = self.redis_service.get(job_id)
                if not job_data_str:
                    logger.warning(f"Job {job_id} not found in Redis")
                    continue
                
                # Parse job data
                job_data = json.loads(job_data_str)
                job_type = job_data.get('job_type', 'sync_drive')  # Default to existing behavior
                organisation_id = job_data.get('organisation_id')
                
                if not organisation_id:
                    logger.error(f"Invalid job data - missing organisation_id: {job_data}")
                    continue
                
                # Process the job based on type
                if job_type == 'sync_drive':
                    user_id = job_data.get('user_id')
                    full_sync = job_data.get('full_sync', False)
                    
                    if not user_id:
                        logger.error(f"Invalid sync_drive job data - missing user_id: {job_data}")
                        continue
                    
                    logger.info(f"Processing Google Drive sync job for organization {organisation_id}")
                    success, message, files_synced, folders_synced = self.drive_service.sync_drive(
                        organisation_id, full_sync
                    )
                    
                    # Log the result
                    if success:
                        logger.info(f"Google Drive sync completed for user {user_id}: {files_synced} files, {folders_synced} folders")
                    else:
                        logger.error(f"Google Drive sync failed for user {user_id}: {message}")
                
                elif job_type == 'sync_file_by_url':
                    drive_urls = job_data.get('drive_urls', [])
                    agent_id = job_data.get('agent_id')
                    user_id = job_data.get('user_id')
                    
                    if not drive_urls:
                        logger.error(f"Invalid sync_file_by_url job data - missing drive_urls: {job_data}")
                        continue
                    
                    if not agent_id:
                        logger.error(f"Invalid sync_file_by_url job data - missing agent_id: {job_data}")
                        continue
                    
                    logger.info(f"Processing file sync by URL job for organization {organisation_id}, URLs: {len(drive_urls)}")
                    success, message, synced_files, total_files, successful_syncs, failed_syncs = self._process_file_by_url_job(
                        organisation_id, drive_urls, agent_id, user_id
                    )
                    
                    # Log the result
                    if success:
                        logger.info(f"File sync by URL completed for organization {organisation_id}: {successful_syncs} successful, {failed_syncs} failed")
                    else:
                        logger.error(f"File sync by URL failed for organization {organisation_id}: {message}")
                    
                    # Store the result in Redis for the gRPC service to retrieve
                    result_key = f"file_url_sync_result:{job_id}"
                    result_data = {
                        'success': success,
                        'message': message,
                        'synced_files': synced_files,
                        'total_files': total_files,
                        'successful_syncs': successful_syncs,
                        'failed_syncs': failed_syncs,
                        'completed_at': datetime.utcnow().isoformat()
                    }
                    self.redis_service.set(result_key, json.dumps(result_data), ex=3600)  # 1 hour expiration
                        
                elif job_type == 'sync_folders_by_ids':
                    folder_ids = job_data.get('folder_ids', [])
                    
                    if not folder_ids:
                        logger.error(f"Invalid sync_folders_by_ids job data - missing folder_ids: {job_data}")
                        continue
                    
                    logger.info(f"Processing Google Drive folder sync job for organization {organisation_id}, folders: {folder_ids}")
                    success, message, files_synced, folders_synced, synced_folders = self._process_folder_sync_job(
                        organisation_id, folder_ids
                    )
                    
                    # Log the result
                    if success:
                        logger.info(f"Google Drive folder sync completed for organization {organisation_id}: {files_synced} files, {folders_synced} folders, {len(synced_folders)} folders processed")
                    else:
                        logger.error(f"Google Drive folder sync failed for organization {organisation_id}: {message}")
                        
                    # Store the result in Redis for the gRPC service to retrieve
                    result_key = f"folder_sync_result:{job_id}"
                    result_data = {
                        'success': success,
                        'message': message,
                        'files_synced': files_synced,
                        'folders_synced': folders_synced,
                        'synced_folders': synced_folders,
                        'completed_at': datetime.utcnow().isoformat()
                    }
                    self.redis_service.set(result_key, json.dumps(result_data), ex=3600)  # 1 hour expiration
                    
                else:
                    logger.error(f"Unknown job type: {job_type}")
                    continue
                
                # Delete the job
                self.redis_service.delete(job_id)
                
            except Exception as e:
                logger.error(f"Error processing Google Drive sync job: {str(e)}")
                time.sleep(self.poll_interval)
    
    def process_job_now(self, user_id, organisation_id, full_sync=False):
        """
        Process a sync job immediately.
        
        Args:
            user_id: The ID of the user
            organisation_id: The ID of the organization
            full_sync: Whether to perform a full sync
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if sync was successful
            - message: Status message
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
        """
        try:
            logger.info(f"Processing immediate Google Drive sync job for organization {organisation_id}")
            return self.drive_service.sync_drive(organisation_id, full_sync)
        except Exception as e:
            logger.error(f"Error processing immediate Google Drive sync job: {str(e)}")
            return False, f"Error processing sync job: {str(e)}", 0, 0
    
    def _process_folder_sync_job(self, organisation_id: str, folder_ids: list) -> tuple:
        """
        Process a folder sync job.
        
        Args:
            organisation_id: The ID of the organization
            folder_ids: List of folder IDs to sync
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if sync was successful
            - message: Status message
            - files_synced: Number of files synced
            - folders_synced: Number of folders synced
            - synced_folders: List of synced folder info
        """
        try:
            # Get service account credentials
            service = self.drive_service.get_service_account_drive_service(organisation_id)
            if not service:
                return False, "No Google Drive service account credentials found", 0, 0, []
            
            total_files_synced = 0
            total_folders_synced = 0
            synced_folder_info = []
            
            # Sync each folder by ID recursively
            for folder_id in folder_ids:
                try:
                    # Sync the folder and its contents recursively
                    files_synced, folders_synced = self.drive_service.sync_folder_recursively_with_permissions(
                        service,
                        organisation_id,
                        folder_id
                    )
                    
                    total_files_synced += files_synced
                    total_folders_synced += folders_synced
                    
                    # Get folder name for response
                    try:
                        folder_data = service.files().get(fileId=folder_id, fields="name").execute()
                        folder_name = folder_data.get('name', 'Unknown folder')
                    except Exception:
                        folder_name = f"Folder {folder_id}"
                    
                    # Add to synced folders list
                    synced_folder_info.append({
                        'id': folder_id,
                        'name': folder_name
                    })
                    
                except Exception as folder_error:
                    logger.error(f"Error syncing folder {folder_id}", error=str(folder_error))
            
            if total_files_synced == 0 and total_folders_synced == 0:
                return False, "No folders found or accessible", 0, 0, []
            
            message = f"Synced {len(synced_folder_info)} folders recursively with {total_files_synced} files and {total_folders_synced} folders"
            return True, message, total_files_synced, total_folders_synced, synced_folder_info
            
        except Exception as e:
            logger.error(f"Error processing folder sync job: {str(e)}")
            return False, f"Error processing folder sync job: {str(e)}", 0, 0, []
    
    def _process_file_by_url_job(self, organisation_id: str, drive_urls: list, agent_id: str, user_id: str = None) -> tuple:
        """
        Process a file sync by URL job.
        
        Args:
            organisation_id: The ID of the organization
            drive_urls: List of URLs to sync
            agent_id: The ID of the agent
            user_id: Optional ID of the user
            
        Returns:
            Tuple containing:
            - success: Boolean indicating if sync was successful
            - message: Status message
            - synced_files: List of synced file info dictionaries
            - total_files: Total number of files processed
            - successful_syncs: Number of successful syncs
            - failed_syncs: Number of failed syncs
        """
        try:
            # Import URL validator to determine URL type
            from app.utils.file_processing.url_validator import URLValidator
            url_validator = URLValidator()
            
            synced_files = []
            successful_syncs = 0
            failed_syncs = 0
            
            # Process each URL
            for drive_url in drive_urls:
                try:
                    # Validate and determine URL type
                    is_valid, url_type, error_msg = url_validator.validate_and_classify_url(drive_url)
                    
                    if not is_valid:
                        # Add failed sync info
                        synced_files.append({
                            'file_id': "",
                            'file_name': "",
                            'drive_url': drive_url,
                            'sync_status': "failed",
                            'error_message': error_msg
                        })
                        failed_syncs += 1
                        continue
                    
                    if url_type == "gdrive_id":
                        # Handle Google Drive URL
                        file_id = self.drive_service.extract_file_id_from_url(drive_url)
                        if not file_id:
                            synced_files.append({
                                'file_id': "",
                                'file_name': "",
                                'drive_url': drive_url,
                                'sync_status': "failed",
                                'error_message': "Invalid Google Drive URL"
                            })
                            failed_syncs += 1
                            continue
                        
                        # Sync the Google Drive file
                        success, message, file_data = self.drive_service.sync_file_by_id(
                            file_id,
                            agent_id,
                            user_id,
                            organisation_id
                        )
                        
                    else:
                        # Handle generic HTTP/HTTPS URL
                        import uuid
                        file_id = str(uuid.uuid4())  # Generate unique ID for generic files
                        
                        # Sync the generic file
                        success, message, file_data = self.drive_service.sync_file_by_id(
                            file_id,
                            agent_id,
                            user_id,
                            organisation_id,
                            url=drive_url  # Pass URL for generic processing
                        )
                    
                    if success:
                        # Add successful sync info
                        synced_files.append({
                            'file_id': file_data['id'],
                            'file_name': file_data['name'],
                            'drive_url': drive_url,
                            'sync_status': "completed",
                            'error_message': ""
                        })
                        successful_syncs += 1
                    else:
                        # Add failed sync info
                        synced_files.append({
                            'file_id': "",
                            'file_name': "",
                            'drive_url': drive_url,
                            'sync_status': "failed",
                            'error_message': message
                        })
                        failed_syncs += 1
                        
                except Exception as url_error:
                    logger.error(f"Error processing URL {drive_url}", error=str(url_error))
                    synced_files.append({
                        'file_id': "",
                        'file_name': "",
                        'drive_url': drive_url,
                        'sync_status': "failed",
                        'error_message': f"Error: {str(url_error)}"
                    })
                    failed_syncs += 1
            
            # Determine overall success
            overall_success = successful_syncs > 0
            total_files = len(drive_urls)
            
            if successful_syncs == total_files:
                overall_message = f"All {total_files} files synced successfully"
            elif successful_syncs > 0:
                overall_message = f"{successful_syncs} of {total_files} files synced successfully, {failed_syncs} failed"
            else:
                overall_message = f"All {total_files} files failed to sync"
            
            return overall_success, overall_message, synced_files, total_files, successful_syncs, failed_syncs
            
        except Exception as e:
            logger.error(f"Error processing file sync by URL job: {str(e)}")
            return False, f"Error processing file sync by URL job: {str(e)}", [], len(drive_urls), 0, len(drive_urls)
    
    def schedule_file_by_url_job(self, organisation_id: str, drive_urls: list, agent_id: str, user_id: str = None, delay_seconds: int = 0) -> str:
        """
        Schedule a file sync by URL job.
        
        Args:
            organisation_id: The ID of the organization
            drive_urls: List of URLs to sync
            agent_id: The ID of the agent
            user_id: Optional ID of the user
            delay_seconds: Delay before executing the job
            
        Returns:
            The job ID
        """
        job_data = {
            'job_type': 'sync_file_by_url',
            'organisation_id': organisation_id,
            'drive_urls': drive_urls,
            'agent_id': agent_id,
            'user_id': user_id,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }
        
        # Store in Redis with appropriate expiration
        job_id = f"gdrive_file_url_sync:{organisation_id}:{int(time.time())}"
        self.redis_service.set(
            job_id,
            json.dumps(job_data),
            ex=86400  # 24 hour expiration
        )
        
        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("gdrive_sync_queue", {job_id: score})
        
        return job_id
        
    def schedule_folder_sync_job(self, organisation_id: str, folder_ids: list, delay_seconds: int = 0) -> str:
        """
        Schedule a folder sync job.
        
        Args:
            organisation_id: The ID of the organization
            folder_ids: List of folder IDs to sync
            delay_seconds: Delay before executing the job
            
        Returns:
            The job ID
        """
        job_data = {
            'job_type': 'sync_folders_by_ids',
            'organisation_id': organisation_id,
            'folder_ids': folder_ids,
            'scheduled_at': (datetime.utcnow() + timedelta(seconds=delay_seconds)).isoformat()
        }
        
        # Store in Redis with appropriate expiration
        job_id = f"gdrive_folder_sync:{organisation_id}:{int(time.time())}"
        self.redis_service.set(
            job_id,
            json.dumps(job_data),
            ex=86400  # 24 hour expiration
        )
        
        # Add to sorted set for processing
        score = time.time() + delay_seconds
        self.redis_service.zadd("gdrive_sync_queue", {job_id: score})
        
        return job_id