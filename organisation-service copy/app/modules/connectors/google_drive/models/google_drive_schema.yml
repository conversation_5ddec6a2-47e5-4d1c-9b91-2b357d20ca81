# Google Drive Connector Schema
version: 1.0
description: "Schema definition for Google Drive connector entities and relationships"

nodes:
  GoogleDriveFile:
    description: "Represents a file in Google Drive"
    properties:
      id:
        type: string
        required: true
        description: "Google Drive file ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this file"
      name:
        type: string
        description: "File name"
      mime_type:
        type: string
        description: "MIME type of the file"
      size:
        type: integer
        description: "File size in bytes"
      web_view_link:
        type: string
        description: "Web view URL for the file"
      created_time:
        type: timestamp
        description: "File creation timestamp"
      modified_time:
        type: timestamp
        description: "File last modified timestamp"
      vector_id:
        type: string
        description: "Pinecone vector ID for semantic search"
      vectorized_at:
        type: timestamp
        description: "Timestamp when the file was last vectorized"
      last_vectorized_modified_time:
        type: timestamp
        description: "File's modified_time when it was last vectorized (for change detection)"
      content_hash:
        type: string
        description: "Hash of file content for change detection"
      permissions:
        type: string
        description: "JSON string of file permissions"

  GoogleDriveFolder:
    description: "Represents a folder in Google Drive"
    properties:
      id:
        type: string
        required: true
        description: "Google Drive folder ID"
      organisation_id:
        type: string
        required: true
        description: "ID of the organisation that owns this folder"
      name:
        type: string
        description: "Folder name"
      created_time:
        type: timestamp
        description: "Folder creation timestamp"
      modified_time:
        type: timestamp
        description: "Folder last modified timestamp"
      permissions:
        type: string
        description: "JSON string of folder permissions"


relationships:
  HAS_ACCESS:
    from: User
    to: [GoogleDriveFile, GoogleDriveFolder]
    description: "User has access to Google Drive file or folder"
    direction: "->"
    properties:
      access_type:
        type: string
        description: "Type of access (owner, editor, viewer)"
      granted_at:
        type: timestamp
        description: "When access was granted"

  CONTAINS:
    from: GoogleDriveFolder
    to: [GoogleDriveFile, GoogleDriveFolder]
    description: "Folder contains files or subfolders"
    direction: "->"
    properties:
      added_at:
        type: timestamp
        description: "When item was added to folder"