"""
Relationship type definitions for Enterprise KG

This module defines all supported relationship types in the enterprise knowledge graph.
Add new relationship types here as the system evolves.
"""

from enum import Enum
from typing import Set, Dict, List, Tuple


class RelationshipType(Enum):
    """
    Enumeration of all supported relationship types in the enterprise knowledge graph.
    """
    # Organizational & People
    WORKS_FOR = "WORKS_FOR"
    MANAGES = "MANAGES"
    REPORTS_TO = "REPORTS_TO"
    LEADS = "LEADS"
    MEMBER_OF = "MEMBER_OF"
    BELONGS_TO = "BELONGS_TO"
    PART_OF = "PART_OF"
    HAS_ROLE = "HAS_ROLE"
    HAS_SKILL = "HAS_SKILL"
    REQUIRES_SKILL = "REQUIRES_SKILL"

    # Project, Product & Initiative
    OWNS = "OWNS"
    RESPONSIBLE_FOR = "RESPONSIBLE_FOR"
    PARTICIPATES_IN = "PARTICIPATES_IN"
    CONTRIBUTES_TO = "CONTRIBUTES_TO"
    DEPENDS_ON = "DEPENDS_ON"
    BLOCKS = "BLOCKS"
    ENABLES = "ENABLES"
    TRACKS = "TRACKS"
    DEVELOPS = "DEVELOPS"
    MAINTAINS = "MAINTAINS"
    HAS_FEATURE = "HAS_FEATURE"
    SUPPORTS = "SUPPORTS"
    MITIGATES = "MITIGATES"

    # Document & Knowledge
    AUTHORED_BY = "AUTHORED_BY"
    REVIEWED_BY = "REVIEWED_BY"
    APPROVED_BY = "APPROVED_BY"
    REFERENCES = "REFERENCES"
    CITES = "CITES"
    SUPERSEDES = "SUPERSEDES"
    IMPLEMENTS = "IMPLEMENTS"
    DOCUMENTS = "DOCUMENTS"

    # Business & Partnerships
    COLLABORATES_WITH = "COLLABORATES_WITH"
    COMPETES_WITH = "COMPETES_WITH"
    PARTNERS_WITH = "PARTNERS_WITH"
    SUPPLIES = "SUPPLIES"
    PURCHASES_FROM = "PURCHASES_FROM"
    CONTRACTS_WITH = "CONTRACTS_WITH"
    ACQUIRED = "ACQUIRED"
    ACQUIRED_BY = "ACQUIRED_BY"

    # Technical
    USES = "USES"
    INTEGRATES_WITH = "INTEGRATES_WITH"
    CONNECTS_TO = "CONNECTS_TO"
    HOSTS = "HOSTS"
    RUNS_ON = "RUNS_ON"
    ACCESSES = "ACCESSES"
    CONSUMES_API = "CONSUMES_API"
    EXPOSES_API = "EXPOSES_API"
    STORES_DATA_IN = "STORES_DATA_IN"

    # Financial
    FUNDS = "FUNDS"
    COSTS = "COSTS"
    GENERATES_REVENUE = "GENERATES_REVENUE"
    HAS_BUDGET = "HAS_BUDGET"
    ALLOCATED_TO = "ALLOCATED_TO"
    INVESTED_IN = "INVESTED_IN"

    # Location
    LOCATED_IN = "LOCATED_IN"
    OPERATES_IN = "OPERATES_IN"
    BASED_IN = "BASED_IN"
    HOSTED_AT = "HOSTED_AT"

    # Hierarchical & Structural
    PARENT_OF = "PARENT_OF"
    CHILD_OF = "CHILD_OF"
    CONTAINS = "CONTAINS"
    CONTAINED_IN = "CONTAINED_IN"

    # Events & Communication
    ATTENDED_BY = "ATTENDED_BY"
    DISCUSSES = "DISCUSSES"
    SCHEDULED_FOR = "SCHEDULED_FOR"

    # Generic & Linking
    RELATED_TO = "RELATED_TO"
    ASSOCIATED_WITH = "ASSOCIATED_WITH"
    MENTIONS = "MENTIONS"
    INVOLVED_IN = "INVOLVED_IN" # Retained for broad use

    # Source & Provenance (Crucial for KG lifecycle)
    EXTRACTED_FROM = "EXTRACTED_FROM"
    SOURCED_FROM = "SOURCED_FROM"
    MENTIONED_IN = "MENTIONED_IN"


# Helper functions for relationship type management
def get_all_relationship_types() -> Set[str]:
    """Get all relationship type values as strings."""
    return {rel_type.value for rel_type in RelationshipType}


def get_organizational_relationships() -> Set[str]:
    """Get relationship types related to organizational structure."""
    return {
        RelationshipType.WORKS_FOR.value, RelationshipType.MANAGES.value,
        RelationshipType.REPORTS_TO.value, RelationshipType.LEADS.value,
        RelationshipType.MEMBER_OF.value, RelationshipType.BELONGS_TO.value,
        RelationshipType.PART_OF.value, RelationshipType.HAS_ROLE.value,
        RelationshipType.HAS_SKILL.value,
    }

def get_project_relationships() -> Set[str]:
    """Get relationship types related to projects."""
    return {
        RelationshipType.INVOLVED_IN.value, RelationshipType.OWNS.value,
        RelationshipType.RESPONSIBLE_FOR.value, RelationshipType.PARTICIPATES_IN.value,
        RelationshipType.CONTRIBUTES_TO.value, RelationshipType.DEPENDS_ON.value,
        RelationshipType.BLOCKS.value, RelationshipType.ENABLES.value,
        RelationshipType.DEVELOPS.value, RelationshipType.MAINTAINS.value,
    }

def get_document_relationships() -> Set[str]:
    """Get relationship types related to documents."""
    return {
        RelationshipType.AUTHORED_BY.value, RelationshipType.REVIEWED_BY.value,
        RelationshipType.APPROVED_BY.value, RelationshipType.REFERENCES.value,
        RelationshipType.CITES.value, RelationshipType.SUPERSEDES.value,
        RelationshipType.IMPLEMENTS.value, RelationshipType.MENTIONS.value,
        RelationshipType.EXTRACTED_FROM.value, RelationshipType.MENTIONED_IN.value,
        RelationshipType.SOURCED_FROM.value, RelationshipType.DOCUMENTS.value,
    }


def get_technical_relationships() -> Set[str]:
    """Get relationship types related to technology."""
    return {
        RelationshipType.USES.value, RelationshipType.INTEGRATES_WITH.value,
        RelationshipType.CONNECTS_TO.value, RelationshipType.HOSTS.value,
        RelationshipType.RUNS_ON.value, RelationshipType.ACCESSES.value,
        RelationshipType.CONSUMES_API.value, RelationshipType.EXPOSES_API.value,
        RelationshipType.STORES_DATA_IN.value
    }


def get_financial_relationships() -> Set[str]:
    """Get relationship types related to finance."""
    return {
        RelationshipType.FUNDS.value, RelationshipType.COSTS.value,
        RelationshipType.GENERATES_REVENUE.value, RelationshipType.HAS_BUDGET.value,
        RelationshipType.ALLOCATED_TO.value, RelationshipType.INVESTED_IN.value
    }


def is_valid_relationship_type(relationship_type: str) -> bool:
    """Check if a string is a valid relationship type."""
    return relationship_type in get_all_relationship_types()


def get_inverse_relationship(relationship_type: RelationshipType) -> RelationshipType:
    """Get the inverse relationship type if it exists."""
    inverse_mapping = {
        RelationshipType.MANAGES: RelationshipType.REPORTS_TO,
        RelationshipType.REPORTS_TO: RelationshipType.MANAGES,
        RelationshipType.PARENT_OF: RelationshipType.CHILD_OF,
        RelationshipType.CHILD_OF: RelationshipType.PARENT_OF,
        RelationshipType.CONTAINS: RelationshipType.CONTAINED_IN,
        RelationshipType.CONTAINED_IN: RelationshipType.CONTAINS,
        RelationshipType.SUPPLIES: RelationshipType.PURCHASES_FROM,
        RelationshipType.PURCHASES_FROM: RelationshipType.SUPPLIES,
        RelationshipType.ACQUIRED: RelationshipType.ACQUIRED_BY,
        RelationshipType.ACQUIRED_BY: RelationshipType.ACQUIRED,
        RelationshipType.CONSUMES_API: RelationshipType.EXPOSES_API,
        RelationshipType.EXPOSES_API: RelationshipType.CONSUMES_API,
        RelationshipType.HOSTS: RelationshipType.RUNS_ON,
        RelationshipType.RUNS_ON: RelationshipType.HOSTS,
    }
    return inverse_mapping.get(relationship_type, relationship_type)


def get_relationship_description(relationship_type: RelationshipType) -> str:
    """Get a human-readable description for a relationship type."""
    descriptions = {
        RelationshipType.WORKS_FOR: "Person works for an organization",
        RelationshipType.MANAGES: "Person manages another person or entity",
        RelationshipType.REPORTS_TO: "Person reports to another person",
        RelationshipType.HAS_SKILL: "Person possesses a specific skill",
        RelationshipType.OWNS: "Entity has ownership or ultimate responsibility for another entity",
        RelationshipType.DEVELOPS: "Team or person develops a product or system",
        RelationshipType.MAINTAINS: "Team or person maintains a product or system",
        RelationshipType.HAS_FEATURE: "Product or system has a specific feature",
        RelationshipType.AUTHORED_BY: "Document was authored by a person",
        RelationshipType.DOCUMENTS: "Document describes a process, project, or system",
        RelationshipType.COLLABORATES_WITH: "Entity collaborates with another entity",
        RelationshipType.USES: "Person or system uses a tool or application",
        RelationshipType.INTEGRATES_WITH: "System integrates with another system",
        RelationshipType.CONSUMES_API: "Application consumes data from an API",
        RelationshipType.EXPOSES_API: "System exposes data or functionality via an API",
        RelationshipType.STORES_DATA_IN: "Application stores data in a specific database",
        RelationshipType.FUNDS: "Entity provides funding for another entity",
        RelationshipType.HAS_BUDGET: "Project or department has a specific budget",
        RelationshipType.ALLOCATED_TO: "Budget is allocated to a project or team",
        RelationshipType.LOCATED_IN: "Entity is located in a place",
        RelationshipType.HOSTED_AT: "Server or System is hosted at a data center",
        RelationshipType.CONTAINS: "Entity contains another entity",
        RelationshipType.ATTENDED_BY: "Person attended a meeting or event",
        RelationshipType.DISCUSSES: "Meeting was about a specific project or topic",
        RelationshipType.RELATED_TO: "Entity is related to another entity in a general sense",
        RelationshipType.MENTIONS: "Document mentions an entity",
        RelationshipType.EXTRACTED_FROM: "Entity was extracted from a specific document",
    }
    return descriptions.get(relationship_type, "Unknown relationship type")


def get_relationship_category_mapping() -> Dict[str, callable]:
    """Get mapping of category names to their corresponding getter functions."""
    return {
        "Organizational Relationships": get_organizational_relationships,
        "Project Relationships": get_project_relationships,
        "Document Relationships": get_document_relationships,
        "Technical Relationships": get_technical_relationships,
        "Financial Relationships": get_financial_relationships,
    }


def get_common_entity_relationship_patterns() -> List[Tuple[str, str, str]]:
    """
    Get common entity-relationship patterns. This list is now much more granular.
    Returns list of (subject_entity_type, relationship_type, object_entity_type) tuples.
    """
    return [
        # People, Skills & Org Structure
        ("Employee", "REPORTS_TO", "Manager"),
        ("Manager", "LEADS", "Team"),
        ("Employee", "MEMBER_OF", "Team"),
        ("Team", "PART_OF", "Department"),
        ("Employee", "HAS_SKILL", "Skill"),
        ("JobRole", "REQUIRES_SKILL", "Skill"),

        # Project, Product & Team
        ("Team", "DEVELOPS", "Product"),
        ("Team", "MAINTAINS", "System"),
        ("Manager", "OWNS", "Project"),
        ("Product", "HAS_FEATURE", "Feature"),
        ("Project", "TRACKS", "KPI"),
        
        # Technical Connections
        ("Application", "CONSUMES_API", "API"),
        ("System", "EXPOSES_API", "API"),
        ("Application", "STORES_DATA_IN", "Database"),
        ("Application", "RUNS_ON", "Server"),
        ("Server", "HOSTED_AT", "Data Center"),
        ("Team", "USES", "Tool"),

        # Documents & Knowledge
        ("Employee", "AUTHORED_BY", "Report"),
        ("Process", "DOCUMENTS", "Procedure"),
        ("Project", "MENTIONED_IN", "Presentation"),
        ("System", "DOCUMENTS", "Knowledge Base Article"),

        # Financials
        ("Department", "HAS_BUDGET", "Budget"),
        ("Budget", "ALLOCATED_TO", "Project"),
        ("Project", "COSTS", "Cost"),
        ("Product", "GENERATES_REVENUE", "Revenue"),

        # Strategy & Business Concepts
        ("Team", "RESPONSIBLE_FOR", "Capability"),
        ("Project", "MITIGATES", "Risk"),
        ("Initiative", "SUPPORTS", "Goal"),
    ]
