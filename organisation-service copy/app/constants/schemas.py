"""
Data schema definitions for Enterprise KG

This module defines the data structures used throughout the enterprise knowledge graph system.
These schemas are used by the LLM extraction process and data storage components.
"""

import dataclasses
from typing import List, Optional, Dict
from datetime import datetime


@dataclasses.dataclass
class DocumentSummary:
    """
    Summary information extracted from a document.

    This schema is used by the LLM to extract high-level information
    about documents before detailed entity/relationship extraction.
    """
    title: str
    summary: str
    document_type: Optional[str] = None
    key_topics: Optional[List[str]] = None
    language: Optional[str] = None
    confidence_score: Optional[float] = None


@dataclasses.dataclass
class EntityRelationship:
    """
    Represents a relationship between two entities in the knowledge graph.

    This is the core schema for entity-relationship extraction.
    The LLM will extract relationships in this format.

    Examples:
    - EntityRelationship(subject="<PERSON>", predicate="involved_in", object="AI Project")
    - EntityRelationship(subject="Marketing Team", predicate="reports_to", object="<PERSON>")
    """
    subject: str
    predicate: str
    object: str

    # Entity types for proper Neo4j labeling
    subject_type: Optional[str] = None
    object_type: Optional[str] = None

    # Optional metadata
    confidence_score: Optional[float] = None
    context: Optional[str] = None
    source_sentence: Optional[str] = None

    # Optional organizational context (for integration with existing graphs)
    source_file_id: Optional[str] = None        # Link to existing file node
    org_context: Optional[Dict[str, str]] = None # Organizational hierarchy context
    extraction_timestamp: Optional[datetime] = None

    def __post_init__(self):
        """Validate the relationship after initialization."""
        if not self.subject or not self.subject.strip():
            raise ValueError("Subject cannot be empty")
        if not self.predicate or not self.predicate.strip():
            raise ValueError("Predicate cannot be empty")
        if not self.object or not self.object.strip():
            raise ValueError("Object cannot be empty")

        # Clean up whitespace
        self.subject = self.subject.strip()
        self.predicate = self.predicate.strip()
        self.object = self.object.strip()


@dataclasses.dataclass
class Entity:
    """
    Represents an individual entity in the knowledge graph.

    This schema can be used for entity-only extraction or
    as a component of relationship extraction.
    """
    name: str
    entity_type: str

    # Optional metadata
    description: Optional[str] = None
    aliases: Optional[List[str]] = None
    confidence_score: Optional[float] = None
    properties: Optional[dict] = None

    def __post_init__(self):
        """Validate the entity after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Entity name cannot be empty")
        if not self.entity_type or not self.entity_type.strip():
            raise ValueError("Entity type cannot be empty")

        # Clean up whitespace
        self.name = self.name.strip()
        self.entity_type = self.entity_type.strip()


@dataclasses.dataclass
class ExtractedData:
    """
    Complete extraction result from a document.

    This schema combines document summary, entities, and relationships
    for comprehensive document analysis.
    """
    document_summary: DocumentSummary
    entities: List[Entity]
    relationships: List[EntityRelationship]

    # Metadata
    extraction_timestamp: Optional[datetime] = None
    extraction_model: Optional[str] = None
    processing_time_seconds: Optional[float] = None


@dataclasses.dataclass
class ProcessingMetadata:
    """
    Metadata about the document processing pipeline.

    This schema tracks information about how documents were processed.
    """
    document_id: str
    document_path: str
    file_size_bytes: int
    processing_start_time: datetime
    processing_end_time: Optional[datetime] = None

    # Processing stages
    summarization_completed: bool = False
    entity_extraction_completed: bool = False
    relationship_extraction_completed: bool = False
    vector_storage_completed: bool = False
    graph_storage_completed: bool = False

    # Error tracking
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None

    @property
    def processing_duration_seconds(self) -> Optional[float]:
        """Calculate processing duration if end time is available."""
        if self.processing_end_time:
            return (self.processing_end_time - self.processing_start_time).total_seconds()
        return None

    @property
    def is_completed(self) -> bool:
        """Check if all processing stages are completed."""
        return (
            self.summarization_completed and
            self.entity_extraction_completed and
            self.relationship_extraction_completed and
            self.vector_storage_completed and
            self.graph_storage_completed
        )


@dataclasses.dataclass
class ChunkData:
    """
    Represents a chunk of text from document splitting.

    This schema is used when documents are split into smaller chunks
    for processing or vector embedding.
    """
    chunk_id: str
    text: str
    start_position: int
    end_position: int

    # Optional metadata
    chunk_index: Optional[int] = None
    overlap_with_previous: Optional[int] = None
    overlap_with_next: Optional[int] = None
    embedding: Optional[List[float]] = None


@dataclasses.dataclass
class GraphNode:
    """
    Represents a node in the knowledge graph for storage.

    This schema is used when storing entities as nodes in Neo4j.
    """
    node_id: str
    label: str  # Entity type
    name: str

    # Properties
    properties: dict = dataclasses.field(default_factory=dict)

    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None


@dataclasses.dataclass
class GraphRelationship:
    """
    Represents a relationship in the knowledge graph for storage.

    This schema is used when storing relationships as edges in Neo4j.
    """
    relationship_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str

    # Properties
    properties: dict = dataclasses.field(default_factory=dict)

    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    source_documents: Optional[List[str]] = None
    confidence_score: Optional[float] = None


@dataclasses.dataclass
class VectorDocument:
    """
    Represents a document for vector storage in Pinecone.

    This schema is used when storing document embeddings.
    """
    document_id: str
    text: str
    embedding: List[float]

    # Metadata for filtering and retrieval
    metadata: dict = dataclasses.field(default_factory=dict)

    def __post_init__(self):
        """Validate vector document after initialization."""
        if not self.document_id:
            raise ValueError("Document ID cannot be empty")
        if not self.text:
            raise ValueError("Text cannot be empty")
        if not self.embedding:
            raise ValueError("Embedding cannot be empty")


# Type aliases for convenience
EntityList = List[Entity]
RelationshipList = List[EntityRelationship]
ChunkList = List[ChunkData]
