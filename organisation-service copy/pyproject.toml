[tool.poetry]
name = "organisation-service"
version = "0.1.0"
description = ""
authors = ["kartikjn <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
python-dotenv = "^1.0.0"
sqlalchemy = "2.0.36"
psycopg2-binary = "^2.9.9"
neo4j = "^5.13.0"  # Neo4j Python driver
pydantic = "^2.4.0"  # Data validation
grpcio = "^1.71.0"  # gRPC support
grpcio-tools = "^1.71.0"  # gRPC tools
protobuf = ">=3.20.2,<6.0.0"
alembic = "^1.12.0" 
pydantic-neo4j = "^0.3.7"
structlog = "^25.3.0"
pydantic-settings = "^2.9.1"
kafka-python = "^2.2.6"
confluent-kafka = "^2.10.0"
google-auth = "^2.40.1"
google-auth-httplib2 = "^0.2.0"
google-api-python-client = "^2.169.0"
google-auth-oauthlib = "^1.2.2"
redis = "^6.1.0"
pypdf = "^4.0.1"  # For PDF text extraction
python-docx = "^1.1.0"  # For DOCX text extraction
pandas = "^2.2.0"  # For Excel file processing
openpyxl = "^3.1.2"  # For Excel file processing
beautifulsoup4 = "^4.12.3"  # For HTML parsing
lxml = "^5.1.0"  # For HTML parsing
pypdf2 = "^3.0.1"
pyyaml = "^6.0.2"
pinecone = "^7.0.1"
python-pptx = "^0.6.21"  # For PowerPoint text extraction
requests = "^2.31.0"  # For HTTP requests
openai = "^1.0.0"  # For LLM-based entity extraction
fastapi-mcp = "^0.3.4"


[tool.poetry.group.dev.dependencies]
grpcio-tools = "^1.71.0"
grpcio = "^1.71.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
