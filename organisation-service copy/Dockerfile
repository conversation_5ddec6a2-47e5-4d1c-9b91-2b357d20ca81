FROM python:3.11-slim

ARG REPO_URL
ARG GIT_TOKEN
ARG ENV


# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
        git \
        build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry (just for exporting requirements)
RUN pip install --no-cache-dir poetry==$POETRY_VERSION

# Set working directory
WORKDIR /app

# Copy poetry files first to leverage Docker cache
COPY pyproject.toml poetry.lock ./

# Export requirements.txt
RUN poetry export --output requirements.txt --without-hashes --with-credentials

# Install compatible NumPy first (before any packages that depend on it)
RUN pip install --no-cache-dir numpy==1.24.4

# Install compatible versions of core numerical packages
RUN pip install --no-cache-dir \
    scipy==1.11.4 \
    scikit-learn==1.3.2 \
    joblib==1.3.2 \
    threadpoolctl==3.2.0

# Install PyTorch first (CPU version as specified)
RUN pip install --no-cache-dir torch==2.0.1 --index-url https://download.pytorch.org/whl/cpu

# Install transformers and related packages with compatible versions (no-deps to prevent upgrades)
RUN pip install --no-cache-dir --no-deps \
    transformers==4.35.2 \
    tokenizers==0.15.0 \
    safetensors==0.4.1 \
    huggingface-hub==0.19.4

# Install sentence-transformers with specific version (no-deps to prevent upgrades)
RUN pip install --no-cache-dir --no-deps sentence-transformers==2.2.2

# Install sentence-transformers additional dependencies that don't conflict
RUN pip install --no-cache-dir \
    nltk==3.8.1 \
    sentencepiece==0.1.99 \
    regex==2023.10.3 \
    tqdm

# Create a filtered requirements.txt excluding packages we've already installed
RUN grep -v -E "^(numpy|scipy|scikit-learn|joblib|threadpoolctl|torch|transformers|tokenizers|safetensors|huggingface-hub|sentence-transformers|nltk|sentencepiece|regex)" requirements.txt > filtered_requirements.txt || touch filtered_requirements.txt

# Install remaining requirements
RUN pip install --no-cache-dir -r filtered_requirements.txt

# Clean up any CUDA packages that might have been installed
RUN pip uninstall -y \
    nvidia-cublas-12-* \
    nvidia-nccl-cu12-* \
    nvidia-nvtx-cu12-* \
    nvidia-cuda-nvrtc-cu12-* \
    nvidia-cuda-runtime-cu12-* \
    nvidia-cuda-cupti-cu12-* \
    nvidia-cusparselt-cu12-* \
    2>/dev/null || true

#Verify all critical packages can be imported
RUN python -c "\
import numpy; print(f'NumPy: {numpy.__version__}'); \
import scipy; print(f'SciPy: {scipy.__version__}'); \
import torch; print(f'PyTorch: {torch.__version__}'); \
import transformers; print(f'Transformers: {transformers.__version__}'); \
import sentence_transformers; print(f'Sentence Transformers: {sentence_transformers.__version__}'); \
import sklearn; print(f'Scikit-learn: {sklearn.__version__}'); \
print('All core packages imported successfully')"

# Copy application code
COPY . .

# Generate gRPC code
RUN python -m app.scripts.generate_grpc

# Start API Gateway
CMD ["python", "-m", "app.main", "--host", "0.0.0.0"]